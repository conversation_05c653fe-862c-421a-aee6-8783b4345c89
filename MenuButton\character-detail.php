<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include required files
require_once '../includes/resource_manager.php';
require_once '../includes/character_manager.php';
require_once '../includes/item_manager.php';
require_once '../includes/item_manager.php';

// Get character ID from URL
$characterId = isset($_GET['id']) ? (int)$_GET['id'] : 1;

// Get character data
$characterManager = new CharacterManager();
$character = $characterManager->getCharacterById($characterId);

// Redirect if character not found
if (!$character) {
    header("Location: Character.php");
    exit();
}

// Get user resources
$user_id = $_SESSION['user_id'];
$resources = ResourceManager::getUserResources($user_id);

// Get character stats and power
$stats = $characterManager->getCharacterStats($characterId);
$power = $characterManager->getCharacterPower($characterId);

// Get item manager and user's Experience Log count
$itemManager = new ItemManager();
$userInventory = $itemManager->getUserInventory($user_id);
$experienceLogCount = $userInventory[1] ?? 0; // Experience Log has ID 1

// Calculate upgrade requirements
$currentLevel = $character['level'];
$maxLevel = 100;
$requiredLogs = 5 + ($currentLevel * 5); // Level 1->2: 10 logs, Level 2->3: 15 logs, etc.
$canUpgrade = $experienceLogCount >= $requiredLogs && $currentLevel < $maxLevel;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - <?php echo htmlspecialchars($character['name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="character-detail.css">
</head>
<body style="background-image: url('<?php echo $character['background']; ?>');">
    <div class="character-detail-container">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="nav-left">
                <a href="Character.php" class="back-btn style-text">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title"><?php echo htmlspecialchars($character['name']); ?></div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="character-main">
            <!-- Left Panel - Character Image -->
            <div class="character-image-panel">
                <div class="character-portrait">
                    <!-- Character Name and Title Above Image -->
                    <div class="character-name-display">
                        <h2><?php echo htmlspecialchars($character['name']); ?></h2>
                        <h3 class="character-title-display"><?php echo htmlspecialchars($character['title']); ?></h3>
                    </div>

                    <img src="<?php echo $character['portrait']; ?>" alt="<?php echo htmlspecialchars($character['name']); ?>"
                         onerror="this.src='<?php echo $character['image']; ?>'">

                    <!-- Element Badge -->
                    <div class="element-badge" style="background: <?php echo CharacterManager::getElementColor($character['element']); ?>">
                        <?php echo CharacterManager::getElementIcon($character['element']); ?>
                        <span><?php echo ucfirst($character['element']); ?></span>
                    </div>

                    

                    <!-- Character Level Below Stars -->
                    <div class="character-level-display">
                        <span class="level-text">Level <?php echo $character['level']; ?></span>
                    </div>

                    <!-- Rarity Stars -->
                    <div class="rarity-display">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <span class="star <?php echo $i <= $character['rarity'] ? 'filled' : 'empty'; ?>">★</span>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Character Info -->
            <div class="character-info-panel">
                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" onclick="showTab('upgrade')">Upgrade</button>
                    <button class="tab-btn" onclick="showTab('skills')">Skills</button>
                    <button class="tab-btn" onclick="showTab('equipment')">Equipment</button>
                    <button class="tab-btn" onclick="showTab('constellation')">Constellation</button>
                    <button class="tab-btn" onclick="showTab('info')">Info</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Upgrade Tab -->
                    <div id="upgrade-tab" class="tab-panel active">
                        <div class="character-upgrade">
                            <h3>🔥 Character Upgrade</h3>
                            <div class="upgrade-header-info">
                                <span class="upgrade-subtitle">Level up your character to unlock new potential!</span>
                            </div>
                            <div class="upgrade-info">
                                <div class="level-progress">
                                    <div class="current-level">
                                        <span class="level-label">Current Level</span>
                                        <span class="level-number"><?php echo $currentLevel; ?></span>
                                    </div>
                                    <div class="level-arrow">→</div>
                                    <div class="next-level">
                                        <span class="level-label">Next Level</span>
                                        <span class="level-number"><?php echo $currentLevel + 1; ?></span>
                                    </div>
                                </div>

                                <div class="upgrade-requirements">
                                    <div class="requirement-item">
                                        <span class="requirement-icon">📖</span>
                                        <span class="requirement-name">Experience Log</span>
                                        <span class="requirement-progress <?php echo $canUpgrade ? 'can-upgrade' : 'insufficient'; ?>">
                                            <?php echo min($experienceLogCount, $requiredLogs); ?>/<?php echo $requiredLogs; ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="upgrade-progress-bar">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo min(100, ($experienceLogCount / $requiredLogs) * 100); ?>%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <?php echo round(min(100, ($experienceLogCount / $requiredLogs) * 100)); ?>% Ready
                                    </div>
                                </div>

                                <?php if ($currentLevel >= $maxLevel): ?>
                                    <div class="max-level-notice">
                                        <i class="fas fa-crown"></i>
                                        <span>Maximum Level Reached!</span>
                                    </div>
                                <?php elseif ($canUpgrade): ?>
                                    <div class="upgrade-ready">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Ready to upgrade!</span>
                                        <button class="upgrade-action-btn" onclick="upgradeCharacter(<?php echo $characterId; ?>)">
                                            <i class="fas fa-arrow-up"></i> Upgrade to Lv.<?php echo $currentLevel + 1; ?>
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="upgrade-insufficient">
                                        <i class="fas fa-exclamation-circle"></i>
                                        <span>Need <?php echo $requiredLogs - $experienceLogCount; ?> more Experience Log(s)</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Upgrade Benefits Preview -->
                        <div class="upgrade-benefits">
                            <h3>💪 Upgrade Benefits</h3>
                            <div class="benefits-grid">
                                <div class="benefit-item">
                                    <span class="benefit-icon">❤️</span>
                                    <span class="benefit-name">Health</span>
                                    <span class="benefit-value">+5% Base HP</span>
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">⚔️</span>
                                    <span class="benefit-name">Attack</span>
                                    <span class="benefit-value">+5% Base ATK</span>
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">🛡️</span>
                                    <span class="benefit-name">Defense</span>
                                    <span class="benefit-value">+5% Base DEF</span>
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">💨</span>
                                    <span class="benefit-name">Speed</span>
                                    <span class="benefit-value">+5% Base SPD</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills Tab -->
                    <div id="skills-tab" class="tab-panel">
                        <div class="skills-container">
                            <h3>Combat Skills</h3>
                            <?php foreach($character['skills'] as $skill): ?>
                                <div class="skill-item">
                                    <div class="skill-icon"><?php echo $skill['icon']; ?></div>
                                    <div class="skill-info">
                                        <div class="skill-name"><?php echo htmlspecialchars($skill['name']); ?></div>
                                        <div class="skill-type"><?php echo ucfirst($skill['type']); ?></div>
                                        <div class="skill-description"><?php echo htmlspecialchars($skill['description']); ?></div>
                                        <div class="skill-damage">Damage: <?php echo $skill['damage']; ?></div>
                                        <?php if($skill['cooldown'] > 0): ?>
                                            <div class="skill-cooldown">Cooldown: <?php echo $skill['cooldown']; ?> turns</div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Equipment Tab -->
                    <div id="equipment-tab" class="tab-panel">
                        <div class="equipment-container">
                            <h3>Equipment</h3>
                            <div class="equipment-item">
                                <span class="equipment-type">Weapon:</span>
                                <span class="equipment-name"><?php echo htmlspecialchars($character['equipment']['weapon']); ?></span>
                            </div>
                            <div class="equipment-item">
                                <span class="equipment-type">Artifact Set:</span>
                                <span class="equipment-name"><?php echo htmlspecialchars($character['equipment']['artifact_set']); ?></span>
                            </div>
                            <div class="equipment-item">
                                <span class="equipment-type">Accessories:</span>
                                <div class="accessories-list">
                                    <?php foreach($character['equipment']['accessories'] as $accessory): ?>
                                        <span class="accessory-name"><?php echo htmlspecialchars($accessory); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Constellation Tab -->
                    <div id="constellation-tab" class="tab-panel">
                        <div class="constellation-container">
                            <h3>Constellation: <?php echo htmlspecialchars($character['constellation']['name']); ?></h3>
                            <div class="constellation-level">Level <?php echo $character['constellation']['level']; ?></div>
                            <div class="constellation-bonuses">
                                <h4>Active Bonuses:</h4>
                                <?php foreach($character['constellation']['bonuses'] as $bonus): ?>
                                    <div class="bonus-item"><?php echo htmlspecialchars($bonus); ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Info Tab -->
                    <div id="info-tab" class="tab-panel">
                        <div class="character-info-section">
                            <div class="character-description">
                                <h3>📜 Description</h3>
                                <p><?php echo htmlspecialchars($character['description']); ?></p>
                            </div>

                            <div class="character-lore">
                                <h3>📚 Lore</h3>
                                <p><?php echo htmlspecialchars($character['lore']); ?></p>
                            </div>

                            <div class="character-stats">
                                <h3>⚔️ Base Stats</h3>
                                <div class="stats-grid">
                                    <?php foreach($stats as $statName => $statValue): ?>
                                        <div class="stat-item">
                                            <span class="stat-name"><?php echo $statName; ?></span>
                                            <span class="stat-value"><?php echo $statValue; ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Notification Element -->
    <div id="notification" class="notification"></div>

    <!-- Include JavaScript -->
    <script src="shared-functions.js"></script>
    <script src="character-detail.js"></script>

    <script>
        // Pass PHP data to JavaScript
        const characterData = {
            id: <?php echo $characterId; ?>,
            name: '<?php echo addslashes($character['name']); ?>',
            currentLevel: <?php echo $currentLevel; ?>,
            maxLevel: <?php echo $maxLevel; ?>,
            experienceLogCount: <?php echo $experienceLogCount; ?>,
            requiredLogs: <?php echo $requiredLogs; ?>,
            canUpgrade: <?php echo $canUpgrade ? 'true' : 'false'; ?>
        };
    </script>
</body>
</html>
