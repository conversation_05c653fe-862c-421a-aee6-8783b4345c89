<?php
/**
 * Resource Management API
 * Handles AJAX requests for resource operations
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

require_once '../includes/resource_manager.php';

$user_id = $_SESSION['user_id'];

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get user resources
        $resources = ResourceManager::getUserResources($user_id);
        echo json_encode([
            'success' => true,
            'resources' => $resources
        ]);
        break;
        
    case 'POST':
        // Handle resource updates
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'add_coins':
                $amount = intval($input['amount'] ?? 0);
                if ($amount > 0) {
                    $success = ResourceManager::updateCoins($user_id, $amount, 'add');
                    echo json_encode([
                        'success' => $success,
                        'message' => $success ? 'Coins added successfully' : 'Failed to add coins'
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Invalid amount']);
                }
                break;
                
            case 'add_diamonds':
                $amount = intval($input['amount'] ?? 0);
                if ($amount > 0) {
                    $success = ResourceManager::updateDiamonds($user_id, $amount, 'add');
                    echo json_encode([
                        'success' => $success,
                        'message' => $success ? 'Diamonds added successfully' : 'Failed to add diamonds'
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Invalid amount']);
                }
                break;
                
            case 'purchase':
                $coins_cost = intval($input['coins_cost'] ?? 0);
                $diamonds_cost = intval($input['diamonds_cost'] ?? 0);
                
                if (ResourceManager::canAfford($user_id, $coins_cost, $diamonds_cost)) {
                    $success = ResourceManager::processPurchase($user_id, $coins_cost, $diamonds_cost);
                    echo json_encode([
                        'success' => $success,
                        'message' => $success ? 'Purchase completed' : 'Purchase failed'
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Insufficient resources'
                    ]);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
                break;
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
?>
