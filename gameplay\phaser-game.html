<!DOCTYPE html>
<html>
<head>
  <title>Qistina Street Platformer</title>
  <script src="https://cdn.jsdelivr.net/npm/phaser@3/dist/phaser.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
    canvas {
      display: block;
      width: 100vw !important;
      height: 100vh !important;
      background: #87CEEB;
    }
  </style>
</head>
<body>

<script>
const config = {
  type: Phaser.AUTO,
  width: window.innerWidth,
  height: window.innerHeight,
  backgroundColor: '#87CEEB', // Sky blue background
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { y: 800 }, // Add gravity for Mario-like gameplay
      debug: false
    }
  },
  scene: {
    preload: preload,
    create: create,
    update: update
  },
  scale: {
    mode: Phaser.Scale.RESIZE,
    autoCenter: Phaser.Scale.CENTER_BOTH
  }
};

const game = new Phaser.Game(config);
let player;
let player2; // Second character (<PERSON><PERSON><PERSON><PERSON>)
let cursors;
let platforms;
let isOnGround = false;
let isOnGround2 = false;
let <PERSON><PERSON><PERSON>, a<PERSON><PERSON>, d<PERSON><PERSON>, w<PERSON><PERSON>;
let q<PERSON><PERSON>, o<PERSON><PERSON>, p<PERSON><PERSON>, i<PERSON><PERSON>; // Keys for second character

function preload() {
  // Load background image
  this.load.image('background', 'winter-city.png');

  // Load Qistina character sprite
  this.load.spritesheet('qistina', 'zulaikha2.png', {
    frameWidth: 384,
    frameHeight: 1024
  });

  // Add load event listeners for debugging
  this.load.on('filecomplete-spritesheet-qistina', function () {
    console.log('Qistina sprite loaded successfully!');
  });

  this.load.on('loaderror', function (file) {
    console.log('Failed to load file:', file.src);
  });

  console.log('Loading Qistina street game assets...');
}

function create() {
  // Add background image
  try {
    const bg = this.add.image(0, 0, 'background');
    bg.setOrigin(0, 0);
    bg.setDisplaySize(this.sys.game.config.width, this.sys.game.config.height);
    console.log('Background image loaded successfully');
  } catch (error) {
    console.log('Background image not found, using solid color');
    // Create a simple gradient background as fallback
    const graphics = this.add.graphics();
    graphics.fillGradientStyle(0x87CEEB, 0x87CEEB, 0x98FB98, 0x98FB98, 1);
    graphics.fillRect(0, 0, this.sys.game.config.width, this.sys.game.config.height);
  }

  // Create platforms group
  platforms = this.physics.add.staticGroup();

  // Create street road platform (full width at bottom)
  const streetGraphics = this.add.graphics();

  // Draw asphalt road base
  streetGraphics.fillStyle(0x2F2F2F); // Dark gray asphalt
  streetGraphics.fillRect(0, 0, this.sys.game.config.width, 80);

  // Draw yellow center line (dashed)
  streetGraphics.fillStyle(0xFFFF00); // Yellow
  for (let x = 0; x < this.sys.game.config.width; x += 60) {
    streetGraphics.fillRect(x, 35, 40, 4);
  }

  // Draw white bottom line only
  streetGraphics.fillStyle(0xFFFFFF); // White
  streetGraphics.fillRect(0, 67, this.sys.game.config.width, 3); // Bottom line only

  streetGraphics.generateTexture('street', this.sys.game.config.width, 80);

  // Clear the graphics object after generating texture to prevent it from showing
  streetGraphics.clear();
  streetGraphics.destroy();

  // Add street platform at the bottom
  const street = platforms.create(this.sys.game.config.width / 2, this.sys.game.config.height - 40, 'street');
  street.setScale(1).refreshBody();

  // Create Qistina character
  console.log('Checking if qistina texture exists:', this.textures.exists('qistina'));

  try {
    if (this.textures.exists('qistina')) {
      console.log('Qistina sprite found! Creating character...');
      player = this.physics.add.sprite(100, this.sys.game.config.height - 200, 'qistina');
      player.setScale(0.35);

      // Create walking animation (only use frames 0-1, not all 4 frames)
      this.anims.create({
        key: 'walk',
        frames: this.anims.generateFrameNumbers('qistina', { start: 0, end: 1 }),
        frameRate: 6,
        repeat: -1
      });

      // Create idle animation
      this.anims.create({
        key: 'idle',
        frames: [{ key: 'qistina', frame: 0 }],
        frameRate: 10
      });

      // Create E key special animation (cycles through all 4 frames)
      this.anims.create({
        key: 'expressionCycle',
        frames: [
          { key: 'qistina', frame: 0 },
          { key: 'qistina', frame: 1 },
          { key: 'qistina', frame: 2 },
          { key: 'qistina', frame: 3 }
        ],
        frameRate:7,
        repeat: -1
      });

      console.log('All animations created for qistina sprite');
    } else {
      throw new Error('Qistina sprite not found');
    }
  } catch (error) {
    console.log('Using fallback Qistina character');

    // Create multiple colored rectangles for animation frames
    const graphics1 = this.add.graphics();
    graphics1.fillStyle(0xFF0000); // Red
    graphics1.fillRect(0, 0, 32, 48);
    graphics1.generateTexture('fallback1', 32, 48);

    const graphics2 = this.add.graphics();
    graphics2.fillStyle(0x00FF00); // Green
    graphics2.fillRect(0, 0, 32, 48);
    graphics2.generateTexture('fallback2', 32, 48);

    const graphics3 = this.add.graphics();
    graphics3.fillStyle(0x0000FF); // Blue
    graphics3.fillRect(0, 0, 32, 48);
    graphics3.generateTexture('fallback3', 32, 48);

    const graphics4 = this.add.graphics();
    graphics4.fillStyle(0xFFFF00); // Yellow
    graphics4.fillRect(0, 0, 32, 48);
    graphics4.generateTexture('fallback4', 32, 48);

    player = this.physics.add.sprite(100, this.sys.game.config.height - 200, 'fallback1');

    // Create animations for fallback character
    this.anims.create({
      key: 'walk',
      frames: [
        { key: 'fallback1', frame: 0 },
      ],
      frameRate: 10,
      repeat: -1
    });

    this.anims.create({
      key: 'idle',
      frames: [{ key: 'fallback1', frame: 0 }],
      frameRate: 10
    });

    this.anims.create({
      key: 'expressionCycle',
      frames: [
        { key: 'fallback1', frame: 0 },
        { key: 'fallback2', frame: 0 },
        { key: 'fallback3', frame: 0 },
        { key: 'fallback4', frame: 0 }
      ],
      frameRate: 8,
      repeat: -1
    });
  }

  // Set player physics properties
  player.setBounce(0.2);
  player.setCollideWorldBounds(true);

  // Player collides with platforms
  this.physics.add.collider(player, platforms);

  // Create cursor keys for movement
  cursors = this.input.keyboard.createCursorKeys();

  // Add individual WASD keys
  wKey = this.input.keyboard.addKey('W');
  aKey = this.input.keyboard.addKey('A');
  dKey = this.input.keyboard.addKey('D');

  // Add E key for special animation
  eKey = this.input.keyboard.addKey('E');

  console.log('Qistina street platformer created!');
}

function update() {
  // Check if player is on ground
  isOnGround = player.body.touching.down;

  // Handle movement first
  if (cursors.left.isDown || aKey.isDown) {
    player.setVelocityX(-150); // Move left on street
    player.setFlipX(false); // Flip sprite to face left
  }
  else if (cursors.right.isDown || dKey.isDown) {
    player.setVelocityX(150); // Move right on street
    player.setFlipX(true); // Face right (normal direction)
    player.play('expressionCycle', true);
  }
  else {
    player.setVelocityX(0); // Stop horizontal movement
  }

  // Handle animations with proper priority
  if (eKey.isDown) {
    // E key has highest priority - play expression animation (4 frames)
    player.play('expressionCycle', true);
  }
  else {
    // For all other cases (moving or not moving), just show static idle frame
    player.play('idle', true);
  }

  // Jumping - only when on ground (Mario style)
  if ((cursors.up.isDown || wKey.isDown) && isOnGround) {
    player.setVelocityY(-500); // Jump with upward velocity
  }
}
</script>

</body>
</html>

















