<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit();
}

// Include store manager
require_once '../includes/store_manager.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

$user_id = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'GET':
            if ($action === 'items') {
                // Get store items
                $category = $_GET['category'] ?? null;
                $featured_only = isset($_GET['featured']) && $_GET['featured'] === 'true';
                
                $items = StoreManager::getStoreItems($category, $featured_only);
                echo json_encode([
                    'success' => true,
                    'items' => $items
                ]);
                
            } elseif ($action === 'get_item') {
                // Get single store item
                $item_id = $_GET['item_id'] ?? 0;
                
                if (!$item_id) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Item ID required']);
                    break;
                }
                
                $item = StoreManager::getStoreItem($item_id);
                if ($item) {
                    echo json_encode([
                        'success' => true,
                        'item' => $item
                    ]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Item not found']);
                }
                
            } elseif ($action === 'purchases') {
                // Get user purchase history
                $limit = $_GET['limit'] ?? 10;
                $purchases = StoreManager::getUserPurchases($user_id, $limit);
                echo json_encode([
                    'success' => true,
                    'purchases' => $purchases
                ]);
                
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if ($action === 'purchase') {
                // Process purchase
                $item_id = $input['item_id'] ?? 0;
                $payment_method = $input['payment_method'] ?? '';

                if (!$item_id || !$payment_method) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Item ID and payment method required']);
                    break;
                }

                // Get item details
                $item = StoreManager::getStoreItem($item_id);
                if (!$item) {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Item not found']);
                    break;
                }

                // Handle diamond purchases (in-game currency)
                if ($payment_method === 'diamonds') {
                    $result = StoreManager::purchaseWithDiamonds($user_id, $item_id);
                    echo json_encode($result);
                    break;
                }

                // Handle real money purchases (diamonds only)
                if ($item['price_usd'] <= 0) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'This item can only be purchased with diamonds']);
                    break;
                }

                // Generate transaction ID
                $transaction_id = 'TXN_' . time() . '_' . $user_id . '_' . $item_id;

                // Create purchase record
                $purchase_id = StoreManager::createPurchase(
                    $user_id,
                    $item_id,
                    $transaction_id,
                    $payment_method,
                    $item['price_usd'],
                    $input // Store all input as payment data
                );

                if (!$purchase_id) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Failed to create purchase record']);
                    break;
                }

                // For demo purposes, immediately complete the purchase
                // In a real implementation, you would integrate with actual payment processors
                if ($payment_method === 'demo') {
                    $result = StoreManager::completePurchase($purchase_id);
                    echo json_encode($result);
                } else {
                    // For real payment methods, you would:
                    // 1. Redirect to payment processor
                    // 2. Handle webhooks/callbacks
                    // 3. Complete purchase after payment confirmation

                    // For now, simulate successful payment
                    $result = StoreManager::completePurchase($purchase_id);
                    echo json_encode($result);
                }
                
            } elseif ($action === 'complete_purchase') {
                // Complete a pending purchase (called by payment webhooks)
                $purchase_id = $input['purchase_id'] ?? 0;
                
                if (!$purchase_id) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Purchase ID required']);
                    break;
                }
                
                $result = StoreManager::completePurchase($purchase_id);
                echo json_encode($result);
                
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
