<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/resource_manager.php';

class MailManager {
    
    /**
     * Get all mail messages for a user
     */
    public static function getUserMail($user_id, $archived = false) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT * FROM mail_messages 
                WHERE user_id = ? AND is_archived = ? 
                ORDER BY created_at DESC";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $user_id, $archived);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $mail = [];
        while ($row = $result->fetch_assoc()) {
            // Parse attachment data if exists
            if ($row['attachment_data']) {
                $row['attachment_data'] = json_decode($row['attachment_data'], true);
            }
            $mail[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $mail;
    }
    
    /**
     * Get unread mail count for a user
     */
    public static function getUnreadCount($user_id) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT COUNT(*) as count FROM mail_messages 
                WHERE user_id = ? AND is_read = FALSE AND is_archived = FALSE";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        $count = $row['count'];
        
        $stmt->close();
        $conn->close();
        
        return $count;
    }
    
    /**
     * Mark a mail message as read
     */
    public static function markAsRead($user_id, $mail_id) {
        $conn = getDatabaseConnection();
        
        $sql = "UPDATE mail_messages 
                SET is_read = TRUE, read_at = NOW() 
                WHERE id = ? AND user_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $mail_id, $user_id);
        $success = $stmt->execute();
        
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Mark all mail as read for a user
     */
    public static function markAllAsRead($user_id) {
        $conn = getDatabaseConnection();
        
        $sql = "UPDATE mail_messages 
                SET is_read = TRUE, read_at = NOW() 
                WHERE user_id = ? AND is_read = FALSE";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $success = $stmt->execute();
        
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Archive/Unarchive a mail message
     */
    public static function toggleArchive($user_id, $mail_id) {
        $conn = getDatabaseConnection();
        
        $sql = "UPDATE mail_messages 
                SET is_archived = NOT is_archived 
                WHERE id = ? AND user_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $mail_id, $user_id);
        $success = $stmt->execute();
        
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Delete a mail message
     */
    public static function deleteMail($user_id, $mail_id) {
        $conn = getDatabaseConnection();
        
        $sql = "DELETE FROM mail_messages WHERE id = ? AND user_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $mail_id, $user_id);
        $success = $stmt->execute();
        
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Send a mail message to a user
     */
    public static function sendMail($user_id, $subject, $message, $sender_name = 'System', $mail_type = 'system', $attachment_data = null, $expires_at = null) {
        $conn = getDatabaseConnection();
        
        $attachment_json = $attachment_data ? json_encode($attachment_data) : null;
        $has_attachment = $attachment_data ? true : false;
        
        $sql = "INSERT INTO mail_messages (user_id, sender_name, subject, message, mail_type, has_attachment, attachment_data, expires_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("issssiis", $user_id, $sender_name, $subject, $message, $mail_type, $has_attachment, $attachment_json, $expires_at);
        $success = $stmt->execute();
        
        $mail_id = $conn->insert_id;
        
        $stmt->close();
        $conn->close();
        
        return $success ? $mail_id : false;
    }
    
    /**
     * Claim attachment rewards from a mail
     */
    public static function claimAttachment($user_id, $mail_id) {
        $conn = getDatabaseConnection();
        
        // Get mail with attachment
        $sql = "SELECT * FROM mail_messages 
                WHERE id = ? AND user_id = ? AND has_attachment = TRUE";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $mail_id, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $stmt->close();
            $conn->close();
            return ['success' => false, 'message' => 'Mail not found or no attachment'];
        }
        
        $mail = $result->fetch_assoc();
        $attachment_data = json_decode($mail['attachment_data'], true);
        
        if (!$attachment_data || isset($attachment_data['claimed'])) {
            $stmt->close();
            $conn->close();
            return ['success' => false, 'message' => 'Attachment already claimed or invalid'];
        }
        
        // Process rewards
        $rewards_given = [];
        
        if (isset($attachment_data['coins']) && $attachment_data['coins'] > 0) {
            ResourceManager::updateCoins($user_id, $attachment_data['coins'], 'add');
            $rewards_given['coins'] = $attachment_data['coins'];
        }
        
        if (isset($attachment_data['diamonds']) && $attachment_data['diamonds'] > 0) {
            ResourceManager::updateDiamonds($user_id, $attachment_data['diamonds'], 'add');
            $rewards_given['diamonds'] = $attachment_data['diamonds'];
        }
        
        if (isset($attachment_data['energy']) && $attachment_data['energy'] > 0) {
            ResourceManager::updateEnergy($user_id, $attachment_data['energy'], 'add');
            $rewards_given['energy'] = $attachment_data['energy'];
        }
        
        // Mark attachment as claimed
        $attachment_data['claimed'] = true;
        $attachment_data['claimed_at'] = date('Y-m-d H:i:s');
        
        $update_sql = "UPDATE mail_messages 
                       SET attachment_data = ?, is_read = TRUE, read_at = NOW() 
                       WHERE id = ? AND user_id = ?";
        
        $update_stmt = $conn->prepare($update_sql);
        $attachment_json = json_encode($attachment_data);
        $update_stmt->bind_param("sii", $attachment_json, $mail_id, $user_id);
        $update_stmt->execute();
        
        $stmt->close();
        $update_stmt->close();
        $conn->close();
        
        return [
            'success' => true,
            'rewards' => $rewards_given,
            'message' => 'Attachment claimed successfully'
        ];
    }
    
    /**
     * Initialize welcome mail for new users
     */
    public static function sendWelcomeMail($user_id) {
        $welcome_message = "Welcome to JILBOOBS WORLD! We're excited to have you join our adventure. Here's a small gift to get you started on your journey.";
        
        $attachment_data = [
            'coins' => 500,
            'diamonds' => 100,
            'energy' => 20
        ];
        
        return self::sendMail(
            $user_id,
            "Welcome to JILBOOBS WORLD!",
            $welcome_message,
            "JILBOOBS Team",
            "system",
            $attachment_data
        );
    }
}
?>
