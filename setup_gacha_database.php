<?php
/**
 * Gacha System Database Setup
 * Run this file once to set up the gacha system tables
 */

require_once 'config/database.php';

echo "<h2>Setting up Gacha System Database Tables...</h2>";

try {
    $conn = getDatabaseConnection();
    
    // Create gacha_pools table
    echo "<h3>Creating gacha_pools table...</h3>";
    $sql_gacha_pools = "CREATE TABLE IF NOT EXISTS gacha_pools (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        banner_image VARCHAR(500),
        ticket_type ENUM('standard', 'premium', 'limited') DEFAULT 'standard',
        ticket_cost INT DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        start_date TIMESTAMP NULL,
        end_date TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if (!$conn->query($sql_gacha_pools)) {
        die("Error creating gacha_pools table: " . $conn->error);
    }
    echo "<p style='color: green;'>✓ gacha_pools table created successfully!</p>";
    
    // Create gacha_items table
    echo "<h3>Creating gacha_items table...</h3>";
    $sql_gacha_items = "CREATE TABLE IF NOT EXISTS gacha_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        pool_id INT NOT NULL,
        item_type ENUM('character', 'weapon', 'item', 'resource') NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        item_description TEXT,
        rarity INT NOT NULL DEFAULT 3,
        drop_rate DECIMAL(5,4) NOT NULL,
        image_url VARCHAR(500),
        reward_data JSON,
        is_featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (pool_id) REFERENCES gacha_pools(id) ON DELETE CASCADE
    )";
    
    if (!$conn->query($sql_gacha_items)) {
        die("Error creating gacha_items table: " . $conn->error);
    }
    echo "<p style='color: green;'>✓ gacha_items table created successfully!</p>";
    
    // Create user_gacha_tickets table
    echo "<h3>Creating user_gacha_tickets table...</h3>";
    $sql_user_tickets = "CREATE TABLE IF NOT EXISTS user_gacha_tickets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        ticket_type ENUM('standard', 'premium', 'limited') NOT NULL,
        quantity INT DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_ticket_type (user_id, ticket_type)
    )";
    
    if (!$conn->query($sql_user_tickets)) {
        die("Error creating user_gacha_tickets table: " . $conn->error);
    }
    echo "<p style='color: green;'>✓ user_gacha_tickets table created successfully!</p>";
    
    // Create user_gacha_history table
    echo "<h3>Creating user_gacha_history table...</h3>";
    $sql_gacha_history = "CREATE TABLE IF NOT EXISTS user_gacha_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        pool_id INT NOT NULL,
        item_id INT NOT NULL,
        item_type ENUM('character', 'weapon', 'item', 'resource') NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        rarity INT NOT NULL,
        pulled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (pool_id) REFERENCES gacha_pools(id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES gacha_items(id) ON DELETE CASCADE
    )";
    
    if (!$conn->query($sql_gacha_history)) {
        die("Error creating user_gacha_history table: " . $conn->error);
    }
    echo "<p style='color: green;'>✓ user_gacha_history table created successfully!</p>";
    
    // Create user_inventory table
    echo "<h3>Creating user_inventory table...</h3>";
    $sql_user_inventory = "CREATE TABLE IF NOT EXISTS user_inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        item_type ENUM('character', 'weapon', 'item', 'resource') NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        item_data JSON,
        quantity INT DEFAULT 1,
        obtained_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (!$conn->query($sql_user_inventory)) {
        die("Error creating user_inventory table: " . $conn->error);
    }
    echo "<p style='color: green;'>✓ user_inventory table created successfully!</p>";
    
    echo "<h3>Inserting default gacha data...</h3>";
    
    // Insert default gacha pool
    $sql_insert_pool = "INSERT IGNORE INTO gacha_pools (id, name, description, ticket_type, ticket_cost) VALUES 
        (1, 'Standard Banner', 'Standard character and item pool', 'standard', 1),
        (2, 'Premium Banner', 'Premium characters with higher rates', 'premium', 1),
        (3, 'Limited Banner', 'Limited time exclusive characters', 'limited', 1)";
    
    if (!$conn->query($sql_insert_pool)) {
        die("Error inserting default pools: " . $conn->error);
    }
    echo "<p style='color: blue;'>✓ Default gacha pools inserted!</p>";
    
    // Insert default gacha items for standard pool
    $sql_insert_items = "INSERT IGNORE INTO gacha_items (pool_id, item_type, item_name, item_description, rarity, drop_rate, reward_data) VALUES 
        (1, 'character', 'Qistina', 'A brave warrior with fire abilities', 5, 0.0060, '{\"character_id\": 1, \"level\": 1}'),
        (1, 'character', 'Zulaikha', 'A skilled mage with water magic', 5, 0.0060, '{\"character_id\": 2, \"level\": 1}'),
        (1, 'character', 'Fifii', 'An earth guardian with strong defense', 4, 0.0510, '{\"character_id\": 3, \"level\": 1}'),
        (1, 'character', 'Azz', 'A mysterious character with dark powers', 4, 0.0510, '{\"character_id\": 4, \"level\": 1}'),
        (1, 'item', 'Health Potion', 'Restores 50 HP', 3, 0.1500, '{\"item_id\": \"health_potion\", \"amount\": 1}'),
        (1, 'item', 'Energy Drink', 'Restores 10 Energy', 3, 0.1500, '{\"item_id\": \"energy_drink\", \"amount\": 1}'),
        (1, 'resource', 'Coins', 'Game currency', 3, 0.3000, '{\"type\": \"coins\", \"amount\": 1000}'),
        (1, 'resource', 'Diamonds', 'Premium currency', 4, 0.1000, '{\"type\": \"diamonds\", \"amount\": 100}'),
        (1, 'item', 'Upgrade Stone', 'Used to upgrade characters', 3, 0.2000, '{\"item_id\": \"upgrade_stone\", \"amount\": 1}'),
        (1, 'item', 'Rare Gem', 'A valuable gemstone', 4, 0.0800, '{\"item_id\": \"rare_gem\", \"amount\": 1}')";
    
    if (!$conn->query($sql_insert_items)) {
        die("Error inserting default items: " . $conn->error);
    }
    echo "<p style='color: blue;'>✓ Default gacha items inserted!</p>";
    
    $conn->close();
    
    echo "<h3>Gacha System Setup Complete!</h3>";
    echo "<p>Default gacha pools and items have been created.</p>";
    echo "<p><strong>Default tickets for new users:</strong> 3x Standard, 3x Premium, 3x Limited</p>";
    echo "<div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc;'>";
    echo "<a href='MenuButton/gacha.php' style='display: inline-block; margin-right: 20px; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px;'>→ Go to Gacha Page</a>";
    echo "<a href='menu/home.php' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>🏠 Back to Game</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
