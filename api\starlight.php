<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Include required managers
require_once '../includes/starlight_manager.php';
require_once '../includes/resource_manager.php';

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'claim':
            handleClaimReward($user_id);
            break;
            
        case 'get_progress':
            handleGetProgress($user_id);
            break;
            
        case 'add_exp':
            handleAddExperience($user_id);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

/**
 * Handle claim reward request
 */
function handleClaimReward($user_id) {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['level']) || !isset($input['tier'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Missing level or tier']);
        return;
    }
    
    $level = intval($input['level']);
    $tier = $input['tier'];
    
    // Validate tier
    if (!in_array($tier, ['free', 'premium'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid tier']);
        return;
    }
    
    // Validate level
    if ($level < 1 || $level > 100) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid level']);
        return;
    }
    
    // Attempt to claim reward
    $result = StarlightManager::claimReward($user_id, $level, $tier);
    
    if ($result['success']) {
        // Get updated user resources
        $resources = ResourceManager::getUserResources($user_id);
        
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'reward' => $result['reward'],
            'resources' => $resources
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
}

/**
 * Handle get progress request
 */
function handleGetProgress($user_id) {
    $progress = StarlightManager::getUserStarlightProgress($user_id);
    $is_premium = StarlightManager::isUserPremium($user_id);
    $resources = ResourceManager::getUserResources($user_id);
    
    echo json_encode([
        'success' => true,
        'progress' => $progress,
        'is_premium' => $is_premium,
        'resources' => $resources
    ]);
}

/**
 * Handle add experience request
 */
function handleAddExperience($user_id) {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['exp_amount'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Missing exp_amount']);
        return;
    }
    
    $exp_amount = intval($input['exp_amount']);
    
    // Validate exp amount
    if ($exp_amount < 1 || $exp_amount > 10000) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid exp amount']);
        return;
    }
    
    // Add experience
    $success = StarlightManager::addExperience($user_id, $exp_amount);
    
    if ($success) {
        // Get updated progress
        $progress = StarlightManager::getUserStarlightProgress($user_id);
        
        echo json_encode([
            'success' => true,
            'message' => "Added {$exp_amount} experience",
            'progress' => $progress
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to add experience'
        ]);
    }
}
?>
