<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/resource_manager.php';

class GachaManager {
    
    /**
     * Initialize default tickets for a new user
     */
    public static function initializeUserTickets($user_id) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("INSERT IGNORE INTO user_gacha_tickets (user_id, ticket_type, quantity) VALUES
            (?, 'standard', 3),
            (?, 'premium', 3),
            (?, 'limited', 3)");
        $stmt->bind_param("iii", $user_id, $user_id, $user_id);
        
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Get user's gacha tickets
     */
    public static function getUserTickets($user_id) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("SELECT ticket_type, quantity FROM user_gacha_tickets WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $tickets = [
            'standard' => 0,
            'premium' => 0,
            'limited' => 0
        ];
        
        while ($row = $result->fetch_assoc()) {
            $tickets[$row['ticket_type']] = $row['quantity'];
        }
        
        $stmt->close();
        $conn->close();
        
        return $tickets;
    }
    
    /**
     * Get all active gacha pools
     */
    public static function getActivePools() {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT * FROM gacha_pools WHERE is_active = TRUE 
                AND (start_date IS NULL OR start_date <= NOW()) 
                AND (end_date IS NULL OR end_date >= NOW()) 
                ORDER BY id";
        
        $result = $conn->query($sql);
        $pools = [];
        
        while ($row = $result->fetch_assoc()) {
            $pools[] = $row;
        }
        
        $conn->close();
        return $pools;
    }
    
    /**
     * Get items in a gacha pool
     */
    public static function getPoolItems($pool_id) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("SELECT * FROM gacha_items WHERE pool_id = ? ORDER BY rarity DESC, drop_rate ASC");
        $stmt->bind_param("i", $pool_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $items = [];
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $items;
    }
    
    /**
     * Perform a gacha pull
     */
    public static function performPull($user_id, $pool_id, $pull_count = 1) {
        $conn = getDatabaseConnection();
        
        // Get pool info
        $stmt = $conn->prepare("SELECT * FROM gacha_pools WHERE id = ? AND is_active = TRUE");
        $stmt->bind_param("i", $pool_id);
        $stmt->execute();
        $pool = $stmt->get_result()->fetch_assoc();
        $stmt->close();
        
        if (!$pool) {
            $conn->close();
            return ['success' => false, 'message' => 'Invalid gacha pool'];
        }
        
        // Check if user has enough tickets
        $tickets = self::getUserTickets($user_id);
        $ticket_type = $pool['ticket_type'];
        $total_cost = $pool['ticket_cost'] * $pull_count;
        
        if ($tickets[$ticket_type] < $total_cost) {
            $conn->close();
            return ['success' => false, 'message' => 'Not enough tickets'];
        }
        
        // Get pool items
        $items = self::getPoolItems($pool_id);
        if (empty($items)) {
            $conn->close();
            return ['success' => false, 'message' => 'No items in pool'];
        }
        
        $pulled_items = [];
        
        // Perform pulls
        for ($i = 0; $i < $pull_count; $i++) {
            $pulled_item = self::rollItem($items);
            if ($pulled_item) {
                $pulled_items[] = $pulled_item;
                
                // Add to user inventory
                self::addToInventory($user_id, $pulled_item);
                
                // Add to gacha history
                self::addToHistory($user_id, $pool_id, $pulled_item);
                
                // Process rewards
                self::processReward($user_id, $pulled_item);
            }
        }
        
        // Deduct tickets
        $stmt = $conn->prepare("UPDATE user_gacha_tickets SET quantity = quantity - ? WHERE user_id = ? AND ticket_type = ?");
        $stmt->bind_param("iis", $total_cost, $user_id, $ticket_type);
        $stmt->execute();
        $stmt->close();
        
        $conn->close();
        
        return [
            'success' => true,
            'items' => $pulled_items,
            'remaining_tickets' => $tickets[$ticket_type] - $total_cost
        ];
    }
    
    /**
     * Roll for an item based on drop rates
     */
    private static function rollItem($items) {
        $total_rate = 0;
        foreach ($items as $item) {
            $total_rate += $item['drop_rate'];
        }
        
        $random = mt_rand() / mt_getrandmax() * $total_rate;
        $current_rate = 0;
        
        foreach ($items as $item) {
            $current_rate += $item['drop_rate'];
            if ($random <= $current_rate) {
                return $item;
            }
        }
        
        // Fallback to last item
        return end($items);
    }
    
    /**
     * Add item to user inventory
     */
    private static function addToInventory($user_id, $item) {
        $conn = getDatabaseConnection();
        
        $reward_data = json_decode($item['reward_data'], true);
        
        // Check if item already exists in inventory
        $stmt = $conn->prepare("SELECT id, quantity FROM user_inventory WHERE user_id = ? AND item_type = ? AND item_name = ?");
        $stmt->bind_param("iss", $user_id, $item['item_type'], $item['item_name']);
        $stmt->execute();
        $existing = $stmt->get_result()->fetch_assoc();
        $stmt->close();
        
        if ($existing) {
            // Update quantity
            $new_quantity = $existing['quantity'] + ($reward_data['amount'] ?? 1);
            $stmt = $conn->prepare("UPDATE user_inventory SET quantity = ? WHERE id = ?");
            $stmt->bind_param("ii", $new_quantity, $existing['id']);
            $stmt->execute();
            $stmt->close();
        } else {
            // Insert new item
            $quantity = $reward_data['amount'] ?? 1;
            $stmt = $conn->prepare("INSERT INTO user_inventory (user_id, item_type, item_name, item_data, quantity) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("isssi", $user_id, $item['item_type'], $item['item_name'], $item['reward_data'], $quantity);
            $stmt->execute();
            $stmt->close();
        }
        
        $conn->close();
    }
    
    /**
     * Add to gacha history
     */
    private static function addToHistory($user_id, $pool_id, $item) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("INSERT INTO user_gacha_history (user_id, pool_id, item_id, item_type, item_name, rarity) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("iiissi", $user_id, $pool_id, $item['id'], $item['item_type'], $item['item_name'], $item['rarity']);
        $stmt->execute();
        $stmt->close();
        
        $conn->close();
    }
    
    /**
     * Process item rewards (coins, diamonds, etc.)
     */
    private static function processReward($user_id, $item) {
        if ($item['item_type'] === 'resource') {
            $reward_data = json_decode($item['reward_data'], true);
            $type = $reward_data['type'];
            $amount = $reward_data['amount'];
            
            if ($type === 'coins') {
                ResourceManager::updateCoins($user_id, $amount, 'add');
            } elseif ($type === 'diamonds') {
                ResourceManager::updateDiamonds($user_id, $amount, 'add');
            } elseif ($type === 'energy') {
                ResourceManager::updateEnergy($user_id, $amount, 'add');
            }
        }
    }
    
    /**
     * Get user's gacha history
     */
    public static function getUserHistory($user_id, $limit = 50) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("SELECT gh.*, gp.name as pool_name FROM user_gacha_history gh 
                               JOIN gacha_pools gp ON gh.pool_id = gp.id 
                               WHERE gh.user_id = ? 
                               ORDER BY gh.pulled_at DESC 
                               LIMIT ?");
        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $history = [];
        while ($row = $result->fetch_assoc()) {
            $history[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $history;
    }
    
    /**
     * Add tickets to user (for rewards, purchases, etc.)
     */
    public static function addTickets($user_id, $ticket_type, $amount) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("INSERT INTO user_gacha_tickets (user_id, ticket_type, quantity) VALUES (?, ?, ?) 
                               ON DUPLICATE KEY UPDATE quantity = quantity + ?");
        $stmt->bind_param("isii", $user_id, $ticket_type, $amount, $amount);
        
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        return $success;
    }
}
?>
