<?php
/**
 * Character Upgrade API
 * Handles character level upgrades using Experience Log items
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit();
}

// Include required files
require_once '../includes/character_manager.php';
require_once '../includes/item_manager.php';

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $characterManager = new CharacterManager();
    $itemManager = new ItemManager();

    switch ($action) {
        case 'get_upgrade_info':
            $characterId = (int)($_GET['character_id'] ?? 0);
            
            if (!$characterId) {
                echo json_encode(['success' => false, 'message' => 'Character ID required']);
                exit();
            }

            $character = $characterManager->getCharacterById($characterId);
            if (!$character) {
                echo json_encode(['success' => false, 'message' => 'Character not found']);
                exit();
            }

            // Get user's Experience Log count
            $userInventory = $itemManager->getUserInventory($user_id);
            $experienceLogCount = $userInventory[1] ?? 0; // Experience Log has ID 1

            // Calculate upgrade requirements
            $currentLevel = $character['level'];
            $nextLevel = $currentLevel + 1;
            $maxLevel = 100; // Set maximum level

            // Calculate Experience Log requirement based on level
            // Level 1->2: 10 logs, Level 2->3: 15 logs, Level 3->4: 20 logs, etc.
            $requiredLogs = calculateRequiredExperienceLogs($currentLevel);

            $canUpgrade = $experienceLogCount >= $requiredLogs && $currentLevel < $maxLevel;

            echo json_encode([
                'success' => true,
                'character' => [
                    'id' => $character['id'],
                    'name' => $character['name'],
                    'current_level' => $currentLevel,
                    'next_level' => $nextLevel,
                    'max_level' => $maxLevel,
                    'is_max_level' => $currentLevel >= $maxLevel
                ],
                'upgrade_requirements' => [
                    'required_logs' => $requiredLogs,
                    'owned_logs' => $experienceLogCount,
                    'can_upgrade' => $canUpgrade,
                    'progress' => min($experienceLogCount, $requiredLogs) . '/' . $requiredLogs
                ],
                'stat_preview' => calculateStatIncrease($character, $nextLevel)
            ]);
            break;

        case 'upgrade_character':
            $input = json_decode(file_get_contents('php://input'), true);
            $characterId = (int)($input['character_id'] ?? 0);

            if (!$characterId) {
                echo json_encode(['success' => false, 'message' => 'Character ID required']);
                exit();
            }

            $character = $characterManager->getCharacterById($characterId);
            if (!$character) {
                echo json_encode(['success' => false, 'message' => 'Character not found']);
                exit();
            }

            // Get user's Experience Log count
            $userInventory = $itemManager->getUserInventory($user_id);
            $experienceLogCount = $userInventory[1] ?? 0;

            $currentLevel = $character['level'];
            $requiredLogs = calculateRequiredExperienceLogs($currentLevel);
            $maxLevel = 100;

            // Validate upgrade
            if ($currentLevel >= $maxLevel) {
                echo json_encode(['success' => false, 'message' => 'Character is already at maximum level']);
                exit();
            }

            if ($experienceLogCount < $requiredLogs) {
                echo json_encode([
                    'success' => false, 
                    'message' => "Insufficient Experience Logs. Need {$requiredLogs}, have {$experienceLogCount}"
                ]);
                exit();
            }

            // Perform upgrade
            $newLevel = $currentLevel + 1;
            $upgradeSuccess = $characterManager->updateCharacterLevel($characterId, $newLevel);

            if ($upgradeSuccess) {
                // In a real implementation, you would also:
                // 1. Deduct Experience Logs from user inventory
                // 2. Update character stats based on new level
                // 3. Log the upgrade in database

                echo json_encode([
                    'success' => true,
                    'message' => "Successfully upgraded {$character['name']} to level {$newLevel}!",
                    'new_level' => $newLevel,
                    'used_logs' => $requiredLogs,
                    'remaining_logs' => $experienceLogCount - $requiredLogs
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to upgrade character']);
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

/**
 * Calculate required Experience Logs for level upgrade
 * @param int $currentLevel Current character level
 * @return int Required Experience Logs
 */
function calculateRequiredExperienceLogs($currentLevel) {
    // Progressive requirement: 
    // Level 1->2: 10 logs
    // Level 2->3: 15 logs  
    // Level 3->4: 20 logs
    // Level 4->5: 25 logs
    // And so on...
    return 5 + ($currentLevel * 5);
}

/**
 * Calculate stat increase for next level
 * @param array $character Character data
 * @param int $nextLevel Next level
 * @return array Stat increases
 */
function calculateStatIncrease($character, $nextLevel) {
    $currentStats = $character['stats'];
    
    // Calculate stat increase per level (approximately 5% increase)
    $statIncrease = [
        'hp' => round($currentStats['hp'] * 0.05),
        'attack' => round($currentStats['attack'] * 0.05),
        'defense' => round($currentStats['defense'] * 0.05),
        'speed' => round($currentStats['speed'] * 0.05),
        'critical_rate' => min(1, $currentStats['critical_rate'] * 0.02), // Smaller increase for percentages
        'critical_damage' => round($currentStats['critical_damage'] * 0.02)
    ];

    $newStats = [
        'hp' => $currentStats['hp'] + $statIncrease['hp'],
        'attack' => $currentStats['attack'] + $statIncrease['attack'],
        'defense' => $currentStats['defense'] + $statIncrease['defense'],
        'speed' => $currentStats['speed'] + $statIncrease['speed'],
        'critical_rate' => $currentStats['critical_rate'] + $statIncrease['critical_rate'],
        'critical_damage' => $currentStats['critical_damage'] + $statIncrease['critical_damage']
    ];

    return [
        'current_stats' => $currentStats,
        'stat_increase' => $statIncrease,
        'new_stats' => $newStats
    ];
}
?>
