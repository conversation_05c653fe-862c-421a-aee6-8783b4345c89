<?php
// Setup script to initialize store items
require_once 'config/database.php';
require_once 'includes/store_manager.php';

echo "Setting up store...\n";

// Initialize default store items
StoreManager::initializeDefaultStoreItems();

echo "Default store items initialized!\n";

// Create some placeholder images directory
$store_assets_dir = 'assets/store';
if (!file_exists($store_assets_dir)) {
    mkdir($store_assets_dir, 0777, true);
    echo "Created store assets directory: $store_assets_dir\n";
}

echo "Store setup complete! You can now visit the store page.\n";
echo "Note: Store images are placeholder paths. Add actual images to assets/store/ directory.\n";
?>
