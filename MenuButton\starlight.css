/* Starlight Page - Honkai Star Rail Style CSS */

:root {
    --primary-color: #00b8ff;
    --secondary-color: #ff4d7e;
    --dark-bg: #1a1a1a;
    --card-bg: rgba(40, 44, 52, 0.85);
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --accent-orange: #ff6b00;
    --starlight-bg: rgba(20, 25, 35, 0.95);
    --starlight-border: rgba(74, 144, 226, 0.3);
    --claim-button: #ff4444;
    --claim-button-hover: #ff6666;
    --premium-gold: #ffd700;
    --free-silver: #c0c0c0;
    --level-bg: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-light);
    overflow-x: hidden;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.starlight-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Animated Gaming Background */
.gaming-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 75%, #1a1a2e 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -2;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Floating Particles */
.particle {
    position: absolute;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    animation: float 8s infinite ease-in-out;
}

.particle:nth-child(1) { width: 4px; height: 4px; top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 6px; height: 6px; top: 60%; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { width: 3px; height: 3px; top: 40%; left: 70%; animation-delay: 4s; }
.particle:nth-child(4) { width: 5px; height: 5px; top: 80%; left: 80%; animation-delay: 6s; }
.particle:nth-child(5) { width: 4px; height: 4px; top: 30%; left: 50%; animation-delay: 1s; }
.particle:nth-child(6) { width: 7px; height: 7px; top: 70%; left: 90%; animation-delay: 3s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

/* Neon Orbs */
.neon-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    pointer-events: none;
    animation: pulse 4s infinite ease-in-out;
}

.neon-orb:nth-child(7) {
    width: 100px;
    height: 100px;
    top: 15%;
    left: 85%;
    background: radial-gradient(circle, rgba(255, 20, 147, 0.4) 0%, transparent 70%);
    animation-delay: 0s;
}

.neon-orb:nth-child(8) {
    width: 150px;
    height: 150px;
    top: 70%;
    left: 10%;
    background: radial-gradient(circle, rgba(0, 191, 255, 0.3) 0%, transparent 70%);
    animation-delay: 2s;
}

.neon-orb:nth-child(9) {
    width: 80px;
    height: 80px;
    top: 40%;
    left: 60%;
    background: radial-gradient(circle, rgba(138, 43, 226, 0.4) 0%, transparent 70%);
    animation-delay: 1s;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 0.9; }
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

/* Main content area */
.starlight-content {
    position: absolute;
    top: 85px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20px;
    overflow-y: auto;
}

/* Starlight header */
.starlight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--starlight-bg);
    border-radius: 15px;
    border: 1px solid var(--starlight-border);
    backdrop-filter: blur(10px);
}

.starlight-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.starlight-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #1a1a1a;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.starlight-details h2 {
    font-size: 24px;
    margin-bottom: 5px;
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.starlight-details p {
    color: var(--text-gray);
    font-size: 14px;
}

.level-progress {
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 300px;
}

.level-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-level {
    font-size: 18px;
    font-weight: 700;
    color: var(--premium-gold);
}

.exp-text {
    font-size: 14px;
    color: var(--text-gray);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--premium-gold), #ffed4e);
    border-radius: 4px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Premium status */
.premium-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    color: #1a1a1a;
    border-radius: 25px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.premium-status.free {
    background: linear-gradient(135deg, var(--free-silver), #e0e0e0);
    color: #333;
}

/* Battle Pass Layout */
.battle-pass-wrapper {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    align-items: flex-start;
}

.gift-character {
    flex-shrink: 0;
    width: 200px;
    height: 300px;
    background: linear-gradient(135deg, rgba(20, 25, 35, 0.95), rgba(30, 35, 45, 0.9));
    border-radius: 15px;
    border: 1px solid var(--starlight-border);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.gift-character::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.gift-character-icon {
    font-size: 80px;
    margin-bottom: 15px;
    animation: float-gift 2s ease-in-out infinite;
}

.gift-character-text {
    text-align: center;
    color: var(--text-light);
    font-weight: 600;
    font-size: 14px;
    line-height: 1.4;
}

.gift-character-subtitle {
    color: var(--text-gray);
    font-size: 12px;
    margin-top: 8px;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes float-gift {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.battle-pass-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: linear-gradient(135deg, rgba(20, 25, 35, 0.95), rgba(30, 35, 45, 0.9));
    border-radius: 15px;
    padding: 20px;
    border: 1px solid var(--starlight-border);
    backdrop-filter: blur(10px);
    min-width: 0; /* Important for flex shrinking */
    max-width: calc(100vw - 280px); /* Account for character + gaps + padding */
}

.battle-pass-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--starlight-bg);
    border-radius: 10px;
    border: 1px solid var(--starlight-border);
}

.level-numbers {
    display: flex;
    gap: 2px;
    min-width: fit-content;
    padding: 10px 0;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: var(--premium-gold) rgba(255, 255, 255, 0.1);
}

.level-numbers::-webkit-scrollbar {
    height: 6px;
}

.level-numbers::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.level-numbers::-webkit-scrollbar-thumb {
    background: var(--premium-gold);
    border-radius: 3px;
}

.level-numbers::-webkit-scrollbar-thumb:hover {
    background: #ffed4e;
}

.level-number-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    width: 120px;
    height: 40px;
    background: var(--level-bg);
    border: 1px solid var(--starlight-border);
    font-weight: 700;
    font-size: 16px;
    color: var(--premium-gold);
    position: relative;
    transition: all 0.3s ease;
    border-radius: 8px 8px 0 0;
    flex-shrink: 0;
}

.level-number-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.level-number-item.current {
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    color: #1a1a1a;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    animation: pulse-current 2s infinite;
}

.level-number-item.completed {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
}

@keyframes pulse-current {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
}

/* Free Tier Row */
.tier-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tier-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 15px 25px;
    background: linear-gradient(135deg, var(--starlight-bg), rgba(30, 35, 45, 0.9));
    border-radius: 10px;
    border: 1px solid var(--starlight-border);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.tier-header:hover {
    border-color: var(--premium-gold);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
}

.tier-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.tier-header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tier-label {
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tier-label.free {
    background: linear-gradient(135deg, var(--free-silver), #e0e0e0);
    color: #333;
}

.tier-label.premium {
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    color: #1a1a1a;
}

.tier-rewards {
    display: flex;
    gap: 2px;
    min-width: fit-content;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding-bottom: 10px;
    scrollbar-width: thin;
    scrollbar-color: var(--premium-gold) rgba(255, 255, 255, 0.1);
}

.tier-rewards::-webkit-scrollbar {
    height: 6px;
}

.tier-rewards::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.tier-rewards::-webkit-scrollbar-thumb {
    background: var(--premium-gold);
    border-radius: 3px;
}

.tier-rewards::-webkit-scrollbar-thumb:hover {
    background: #ffed4e;
}

.reward-slot {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    width: 120px;
    height: 120px;
    background: var(--card-bg);
    border: 2px solid var(--starlight-border);
    border-radius: 8px;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    flex-shrink: 0;
}

.reward-slot:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.reward-slot.available {
    border-color: var(--claim-button);
    box-shadow: 0 0 15px rgba(255, 68, 68, 0.3);
}

.reward-slot.claimed {
    border-color: #4caf50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(69, 160, 73, 0.1));
}

.reward-slot.locked {
    opacity: 0.5;
    cursor: not-allowed;
}

.reward-slot.premium-locked {
    border-color: var(--premium-gold);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 237, 78, 0.05));
}

.reward-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.reward-amount {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    text-align: center;
    line-height: 1.2;
}

.reward-item-name {
    font-size: 10px;
    color: var(--text-gray);
    text-align: center;
    margin-top: 4px;
}

/* Claim indicator */
.claim-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.claim-indicator.available {
    background: var(--claim-button);
    color: white;
    animation: pulse-claim 2s infinite;
}

.claim-indicator.claimed {
    background: #4caf50;
    color: white;
}

.claim-indicator.locked {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-gray);
}

.claim-indicator.premium-required {
    background: var(--premium-gold);
    color: #1a1a1a;
}

.claim-indicator.loading {
    background: var(--primary-color);
    color: white;
    animation: pulse-loading 1s infinite;
}

@keyframes pulse-claim {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes pulse-loading {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 0.7; }
}

/* Level progression line */
.progression-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        #4caf50 0%,
        #4caf50 var(--progress-width, 0%),
        rgba(255, 255, 255, 0.2) var(--progress-width, 0%),
        rgba(255, 255, 255, 0.2) 100%
    );
    z-index: -1;
    border-radius: 2px;
}

/* Premium upgrade button */
.premium-upgrade {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    color: #1a1a1a;
    border: none;
    border-radius: 25px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.premium-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.premium-upgrade i {
    font-size: 18px;
}

/* Notification */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.notification.error {
    background: linear-gradient(135deg, #f44336, #da190b);
}

/* Battle Pass Specific Styles */
.battle-pass-title {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--premium-gold), #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.battle-pass-subtitle {
    color: var(--text-gray);
    font-size: 14px;
    margin-bottom: 20px;
}

.current-level-indicator {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--premium-gold);
    color: #1a1a1a;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    white-space: nowrap;
}

.reward-tooltip {
    position: absolute;
    bottom: 130px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.reward-slot:hover .reward-tooltip {
    opacity: 1;
}

/* Scrollbar styling for horizontal scroll */
.tier-rewards::-webkit-scrollbar {
    height: 6px;
}

.tier-rewards::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.tier-rewards::-webkit-scrollbar-thumb {
    background: var(--premium-gold);
    border-radius: 3px;
}

.tier-rewards::-webkit-scrollbar-thumb:hover {
    background: #ffed4e;
}

/* Responsive design */
@media (max-width: 1024px) {
    .battle-pass-wrapper {
        flex-direction: column;
    }

    .gift-character {
        width: 100%;
        height: 150px;
        flex-direction: row;
        justify-content: center;
        gap: 20px;
    }

    .gift-character-icon {
        font-size: 60px;
        margin-bottom: 0;
    }

    .battle-pass-container {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .top-nav {
        padding: 15px 20px;
    }

    .page-title {
        font-size: 22px;
    }

    .starlight-content {
        padding: 15px;
    }

    .starlight-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .level-progress {
        min-width: auto;
        width: 100%;
    }

    .gift-character {
        height: 120px;
        padding: 15px;
    }

    .gift-character-icon {
        font-size: 50px;
    }

    .gift-character-text {
        font-size: 12px;
    }

    .reward-slot {
        min-width: 100px;
        width: 100px;
        height: 100px;
    }

    .level-number-item {
        min-width: 100px;
        width: 100px;
        height: 35px;
        font-size: 14px;
    }

    .reward-icon {
        font-size: 24px;
    }

    .reward-amount {
        font-size: 10px;
    }

    .tier-header {
        padding: 8px 15px;
        flex-direction: column;
        gap: 10px;
    }

    .tier-header-left,
    .tier-header-right {
        justify-content: center;
    }

    .tier-label {
        font-size: 12px;
        padding: 4px 12px;
    }
}

@media (max-width: 480px) {
    .reward-slot {
        width: 80px;
        height: 80px;
    }

    .level-number-item {
        width: 80px;
        height: 30px;
        font-size: 12px;
    }

    .reward-icon {
        font-size: 20px;
    }

    .reward-amount {
        font-size: 9px;
    }
}
