/* Bag Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 50%, rgba(15, 52, 96, 0.9) 100%),
                url('../assets/backcharacter.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    color: white;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Backdrop overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
    pointer-events: none;
    z-index: -1;
}

#game-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Main Bag Container */
.bag-container {
    flex: 1;
    display: flex;
    padding: 20px;
    gap: 20px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Left Sidebar */
.bag-sidebar {
    flex: 0 0 280px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: fit-content;
}

.sidebar-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #00bcd4;
    margin-bottom: 5px;
}

.sidebar-subtitle {
    font-size: 0.9rem;
    color: #999;
}

/* Category List */
.category-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid transparent;
}

.category-item:hover {
    background: rgba(0, 188, 212, 0.1);
    border-color: rgba(0, 188, 212, 0.3);
    transform: translateX(5px);
}

.category-item.active {
    background: rgba(0, 188, 212, 0.2);
    border-color: #00bcd4;
    color: #00bcd4;
}

.category-icon {
    font-size: 1.2rem;
    margin-right: 12px;
    min-width: 24px;
}

.category-name {
    flex: 1;
    font-weight: 500;
}

.category-count {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    min-width: 24px;
    text-align: center;
}

.category-item.active .category-count {
    background: rgba(0, 188, 212, 0.3);
    color: white;
}

/* Right Content */
.bag-content {
    flex: 1;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
}

.content-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.content-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #00bcd4;
    margin-bottom: 5px;
}

.content-subtitle {
    font-size: 0.9rem;
    color: #999;
}

/* Items Grid */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
}

/* Custom scrollbar */
.items-grid::-webkit-scrollbar {
    width: 6px;
}

.items-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.items-grid::-webkit-scrollbar-thumb {
    background: rgba(0, 188, 212, 0.5);
    border-radius: 3px;
}

.items-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 188, 212, 0.7);
}

/* Item Cards */
.item-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 180px;
}

.item-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.item-rarity-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    border: 2px solid;
    pointer-events: none;
}

.item-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 10px;
}

.item-name {
    font-size: 1rem;
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
    line-height: 1.2;
}

.item-quantity {
    font-size: 0.9rem;
    color: #ffa726;
    margin-bottom: 5px;
    font-weight: bold;
}

.item-rarity {
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Item Actions */
.item-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.item-card:hover .item-actions {
    opacity: 1;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.use-btn {
    background: linear-gradient(45deg, #4caf50, #388e3c);
    color: white;
}

.use-btn:hover {
    background: linear-gradient(45deg, #388e3c, #2e7d32);
    transform: scale(1.1);
}

.sell-btn {
    background: linear-gradient(45deg, #ff9800, #f57c00);
    color: white;
}

.sell-btn:hover {
    background: linear-gradient(45deg, #f57c00, #ef6c00);
    transform: scale(1.1);
}

/* No Items State */
.no-items {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.no-items-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-items-text {
    font-size: 1.2rem;
    color: #ccc;
    margin-bottom: 10px;
}

.no-items-subtitle {
    font-size: 0.9rem;
    color: #999;
}

/* Item Details Modal */
.item-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.item-modal.active {
    display: flex;
}

.modal-content {
    background: rgba(20, 25, 40, 0.95);
    border-radius: 15px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #00bcd4;
    margin: 0;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: #ccc;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
}

.modal-actions {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Item Detail Content */
.item-detail-content {
    color: white;
}

.item-detail-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.item-detail-icon {
    font-size: 3rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.item-detail-info {
    flex: 1;
}

.item-detail-name {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.item-detail-type {
    color: #ffa726;
    font-size: 0.9rem;
    margin-bottom: 3px;
    text-transform: capitalize;
}

.item-detail-rarity {
    font-size: 0.9rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.item-detail-quantity {
    color: #4caf50;
    font-weight: bold;
}

.item-detail-description,
.item-detail-usage,
.item-detail-obtain,
.item-detail-sell {
    margin-bottom: 20px;
}

.item-detail-description h4,
.item-detail-usage h4,
.item-detail-obtain h4,
.item-detail-sell h4 {
    color: #00bcd4;
    margin-bottom: 10px;
    font-size: 1rem;
}

.item-detail-description p,
.item-detail-usage p,
.item-detail-sell p {
    line-height: 1.6;
    color: #ddd;
}

.item-detail-obtain ul {
    list-style: none;
    padding: 0;
}

.item-detail-obtain li {
    padding: 5px 0;
    color: #ddd;
    position: relative;
    padding-left: 20px;
}

.item-detail-obtain li::before {
    content: '•';
    color: #00bcd4;
    position: absolute;
    left: 0;
}

/* Modal Action Buttons */
.modal-action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.modal-action-btn.use-btn {
    background: linear-gradient(45deg, #4caf50, #388e3c);
    color: white;
}

.modal-action-btn.use-btn:hover {
    background: linear-gradient(45deg, #388e3c, #2e7d32);
    transform: translateY(-2px);
}

.modal-action-btn.sell-btn {
    background: linear-gradient(45deg, #ff9800, #f57c00);
    color: white;
}

.modal-action-btn.sell-btn:hover {
    background: linear-gradient(45deg, #f57c00, #ef6c00);
    transform: translateY(-2px);
}

.modal-action-btn.cancel-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-action-btn.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(45deg, #4caf50, #388e3c);
}

.notification.error {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

.notification.warning {
    background: linear-gradient(45deg, #ff9800, #f57c00);
}

.notification.info {
    background: linear-gradient(45deg, #2196f3, #1976d2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .bag-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .bag-sidebar {
        flex: none;
    }
    
    .category-list {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .category-item {
        flex: 1;
        min-width: 150px;
    }
}

@media (max-width: 768px) {
    .bag-container {
        padding: 15px;
    }
    
    .items-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .item-card {
        min-height: 150px;
        padding: 12px;
    }
    
    .item-icon {
        font-size: 2rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
}

@media (max-width: 480px) {
    .items-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .category-list {
        flex-direction: column;
    }
    
    .category-item {
        min-width: auto;
    }
}
