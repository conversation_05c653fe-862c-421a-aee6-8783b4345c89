<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/resource_manager.php';

class StarlightManager {
    
    /**
     * Initialize default starlight progress for a new user
     */
    public static function initializeUserStarlight($user_id) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("INSERT INTO user_starlight_progress (user_id, current_level, current_exp, exp_to_next_level) VALUES (?, 1, 0, 1000)");
        $stmt->bind_param("i", $user_id);

        $success = $stmt->execute();
        $stmt->close();
        $conn->close();

        return $success;
    }
    
    /**
     * Get user starlight progress
     */
    public static function getUserStarlightProgress($user_id) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("SELECT * FROM user_starlight_progress WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $progress = $result->fetch_assoc();
            // Decode JSON arrays
            $progress['claimed_free_rewards'] = json_decode($progress['claimed_free_rewards'], true) ?: [];
            $progress['claimed_premium_rewards'] = json_decode($progress['claimed_premium_rewards'], true) ?: [];
        } else {
            // If no progress found, initialize with defaults
            self::initializeUserStarlight($user_id);
            $progress = [
                'user_id' => $user_id,
                'current_level' => 1,
                'current_exp' => 0,
                'exp_to_next_level' => 1000,
                'claimed_free_rewards' => [],
                'claimed_premium_rewards' => []
            ];
        }

        $stmt->close();
        $conn->close();

        return $progress;
    }
    
    /**
     * Get all starlight rewards
     */
    public static function getAllStarlightRewards() {
        $conn = getDatabaseConnection();

        $sql = "SELECT * FROM starlight_rewards WHERE is_active = TRUE ORDER BY level ASC, tier ASC";
        $result = $conn->query($sql);

        $rewards = [];
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $rewards[] = $row;
            }
        }

        $conn->close();
        return $rewards;
    }
    
    /**
     * Get starlight rewards by level
     */
    public static function getRewardsByLevel($level) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("SELECT * FROM starlight_rewards WHERE level = ? AND is_active = TRUE ORDER BY tier ASC");
        $stmt->bind_param("i", $level);
        $stmt->execute();
        $result = $stmt->get_result();

        $rewards = [];
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $rewards[] = $row;
            }
        }

        $stmt->close();
        $conn->close();
        return $rewards;
    }
    
    /**
     * Check if user is premium
     */
    public static function isUserPremium($user_id) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("SELECT is_premium, premium_expires_at FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            $is_premium = $user['is_premium'];
            $expires_at = $user['premium_expires_at'];
            
            // Check if premium has expired
            if ($is_premium && $expires_at && strtotime($expires_at) < time()) {
                // Premium has expired, update user status
                self::updatePremiumStatus($user_id, false, null);
                $is_premium = false;
            }
        } else {
            $is_premium = false;
        }

        $stmt->close();
        $conn->close();

        return $is_premium;
    }
    
    /**
     * Update user premium status
     */
    public static function updatePremiumStatus($user_id, $is_premium, $expires_at = null) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("UPDATE users SET is_premium = ?, premium_expires_at = ? WHERE id = ?");
        $stmt->bind_param("isi", $is_premium, $expires_at, $user_id);

        $success = $stmt->execute();
        $stmt->close();
        $conn->close();

        return $success;
    }
    
    /**
     * Add experience points to user
     */
    public static function addExperience($user_id, $exp_amount) {
        $progress = self::getUserStarlightProgress($user_id);
        $new_exp = $progress['current_exp'] + $exp_amount;
        $current_level = $progress['current_level'];
        $exp_to_next = $progress['exp_to_next_level'];
        
        // Check for level ups
        while ($new_exp >= $exp_to_next) {
            $new_exp -= $exp_to_next;
            $current_level++;
            $exp_to_next = self::calculateExpForLevel($current_level);
        }
        
        return self::updateStarlightProgress($user_id, $current_level, $new_exp, $exp_to_next);
    }
    
    /**
     * Calculate experience required for a level
     */
    public static function calculateExpForLevel($level) {
        // Progressive experience requirement: base 1000 + (level * 200)
        return 1000 + (($level - 1) * 200);
    }
    
    /**
     * Update starlight progress
     */
    public static function updateStarlightProgress($user_id, $level, $exp, $exp_to_next) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("UPDATE user_starlight_progress SET current_level = ?, current_exp = ?, exp_to_next_level = ? WHERE user_id = ?");
        $stmt->bind_param("iiii", $level, $exp, $exp_to_next, $user_id);

        $success = $stmt->execute();
        $stmt->close();
        $conn->close();

        return $success;
    }
    
    /**
     * Claim starlight reward
     */
    public static function claimReward($user_id, $level, $tier) {
        $progress = self::getUserStarlightProgress($user_id);
        
        // Check if user has reached the required level
        if ($progress['current_level'] < $level) {
            return ['success' => false, 'message' => 'Level not reached yet'];
        }
        
        // Check if reward is already claimed
        $claimed_rewards = ($tier === 'free') ? $progress['claimed_free_rewards'] : $progress['claimed_premium_rewards'];
        if (in_array($level, $claimed_rewards)) {
            return ['success' => false, 'message' => 'Reward already claimed'];
        }
        
        // Check premium access for premium rewards
        if ($tier === 'premium' && !self::isUserPremium($user_id)) {
            return ['success' => false, 'message' => 'Premium access required'];
        }
        
        // Get reward details
        $rewards = self::getRewardsByLevel($level);
        $reward = null;
        foreach ($rewards as $r) {
            if ($r['tier'] === $tier) {
                $reward = $r;
                break;
            }
        }
        
        if (!$reward) {
            return ['success' => false, 'message' => 'Reward not found'];
        }
        
        // Give reward to user
        $reward_given = false;
        switch ($reward['reward_type']) {
            case 'coins':
                $reward_given = ResourceManager::updateCoins($user_id, $reward['reward_amount'], 'add');
                break;
            case 'diamonds':
                $reward_given = ResourceManager::updateDiamonds($user_id, $reward['reward_amount'], 'add');
                break;
            case 'energy':
                $reward_given = ResourceManager::updateEnergy($user_id, $reward['reward_amount'], 'add');
                break;
            case 'item':
                // For now, just give coins equivalent
                $reward_given = ResourceManager::updateCoins($user_id, $reward['reward_amount'], 'add');
                break;
        }
        
        if (!$reward_given) {
            return ['success' => false, 'message' => 'Failed to give reward'];
        }
        
        // Mark reward as claimed
        $claimed_rewards[] = $level;
        $column = ($tier === 'free') ? 'claimed_free_rewards' : 'claimed_premium_rewards';
        
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("UPDATE user_starlight_progress SET $column = ? WHERE user_id = ?");
        $claimed_json = json_encode($claimed_rewards);
        $stmt->bind_param("si", $claimed_json, $user_id);
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        if (!$success) {
            return ['success' => false, 'message' => 'Failed to update progress'];
        }
        
        return [
            'success' => true,
            'message' => 'Reward claimed successfully',
            'reward' => $reward
        ];
    }
    
    /**
     * Initialize default starlight rewards
     */
    public static function initializeDefaultRewards() {
        $conn = getDatabaseConnection();
        
        // Check if rewards already exist
        $result = $conn->query("SELECT COUNT(*) as count FROM starlight_rewards");
        $row = $result->fetch_assoc();
        if ($row['count'] > 0) {
            $conn->close();
            return true; // Already initialized
        }
        
        // Default rewards for levels 1-10
        $default_rewards = [
            // Level 1
            ['level' => 1, 'tier' => 'free', 'reward_type' => 'coins', 'reward_amount' => 5000],
            ['level' => 1, 'tier' => 'premium', 'reward_type' => 'diamonds', 'reward_amount' => 100],
            
            // Level 2
            ['level' => 2, 'tier' => 'free', 'reward_type' => 'energy', 'reward_amount' => 20],
            ['level' => 2, 'tier' => 'premium', 'reward_type' => 'coins', 'reward_amount' => 15000],
            
            // Level 3
            ['level' => 3, 'tier' => 'free', 'reward_type' => 'diamonds', 'reward_amount' => 50],
            ['level' => 3, 'tier' => 'premium', 'reward_type' => 'energy', 'reward_amount' => 50],
            
            // Level 4
            ['level' => 4, 'tier' => 'free', 'reward_type' => 'coins', 'reward_amount' => 10000],
            ['level' => 4, 'tier' => 'premium', 'reward_type' => 'diamonds', 'reward_amount' => 200],
            
            // Level 5
            ['level' => 5, 'tier' => 'free', 'reward_type' => 'energy', 'reward_amount' => 30],
            ['level' => 5, 'tier' => 'premium', 'reward_type' => 'item', 'reward_amount' => 50000, 'reward_item_name' => 'Premium Box', 'reward_item_description' => 'Contains rare items'],
            
            // Level 6
            ['level' => 6, 'tier' => 'free', 'reward_type' => 'diamonds', 'reward_amount' => 75],
            ['level' => 6, 'tier' => 'premium', 'reward_type' => 'coins', 'reward_amount' => 25000],
            
            // Level 7
            ['level' => 7, 'tier' => 'free', 'reward_type' => 'coins', 'reward_amount' => 15000],
            ['level' => 7, 'tier' => 'premium', 'reward_type' => 'energy', 'reward_amount' => 75],
            
            // Level 8
            ['level' => 8, 'tier' => 'free', 'reward_type' => 'energy', 'reward_amount' => 40],
            ['level' => 8, 'tier' => 'premium', 'reward_type' => 'diamonds', 'reward_amount' => 300],
            
            // Level 9
            ['level' => 9, 'tier' => 'free', 'reward_type' => 'diamonds', 'reward_amount' => 100],
            ['level' => 9, 'tier' => 'premium', 'reward_type' => 'coins', 'reward_amount' => 40000],
            
            // Level 10
            ['level' => 10, 'tier' => 'free', 'reward_type' => 'item', 'reward_amount' => 25000, 'reward_item_name' => 'Starlight Box', 'reward_item_description' => 'Special starlight reward'],
            ['level' => 10, 'tier' => 'premium', 'reward_type' => 'item', 'reward_amount' => 100000, 'reward_item_name' => 'Premium Starlight Box', 'reward_item_description' => 'Exclusive premium reward']
        ];
        
        $stmt = $conn->prepare("INSERT INTO starlight_rewards (level, tier, reward_type, reward_amount, reward_item_name, reward_item_description) VALUES (?, ?, ?, ?, ?, ?)");

        foreach ($default_rewards as $reward) {
            $level = $reward['level'];
            $tier = $reward['tier'];
            $reward_type = $reward['reward_type'];
            $reward_amount = $reward['reward_amount'];
            $reward_item_name = $reward['reward_item_name'] ?? null;
            $reward_item_description = $reward['reward_item_description'] ?? null;

            $stmt->bind_param("ississ",
                $level,
                $tier,
                $reward_type,
                $reward_amount,
                $reward_item_name,
                $reward_item_description
            );
            $stmt->execute();
        }
        
        $stmt->close();
        $conn->close();
        
        return true;
    }
}
?>
