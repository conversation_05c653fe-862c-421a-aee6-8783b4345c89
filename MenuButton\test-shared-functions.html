<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Shared Functions</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .test-resources {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #357abd;
        }
        #notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <h1>Shared Functions Test Page</h1>
    
    <div class="test-section">
        <h2>Resource Display with + Buttons</h2>
        <div class="test-resources">
            <div class="resource-item">
                <span class="resource-icon">💎</span>
                <span id="diamonds-count">1000</span>
                <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
            </div>
            <div class="resource-item">
                <span class="resource-icon">🪙</span>
                <span id="coins-count">5000</span>
                <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
            </div>
            <div class="resource-item">
                <span class="resource-icon">⚡</span>
                <span id="energy-count">40</span>
                <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
            </div>
            <div class="resource-item">
                <span class="resource-icon">🎫</span>
                <span id="standard-tickets">3</span>
                <div class="plus-btn" onclick="goToStore('tickets')" title="Buy Tickets">+</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Function Tests</h2>
        
        <h3>Resource Update Test</h3>
        <button onclick="testUpdateResources()">Update Resources</button>
        <button onclick="testUpdateTickets()">Update Tickets</button>
        
        <h3>Notification Tests</h3>
        <button onclick="showNotification('Success message!', 'success')">Success Notification</button>
        <button onclick="showNotification('Error message!', 'error')">Error Notification</button>
        <button onclick="showNotification('Warning message!', 'warning')">Warning Notification</button>
        <button onclick="showNotification('Info message!', 'info')">Info Notification</button>
        
        <h3>Utility Tests</h3>
        <button onclick="testFormatNumber()">Test Format Number</button>
        <button onclick="testAnimateScale()">Test Scale Animation</button>
        <button onclick="testButtonLoading()">Test Button Loading</button>
        
        <h3>API Test</h3>
        <button onclick="testApiRequest()">Test API Request</button>
    </div>

    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <!-- Include shared functions -->
    <script src="shared-functions.js"></script>

    <script>
        // Test functions
        function testUpdateResources() {
            updateResourceDisplay({
                coins: Math.floor(Math.random() * 10000),
                diamonds: Math.floor(Math.random() * 1000),
                energy: Math.floor(Math.random() * 100)
            });
            showNotification('Resources updated!', 'success');
        }

        function testUpdateTickets() {
            updateTicketDisplay('standard', Math.floor(Math.random() * 10));
            showNotification('Tickets updated!', 'success');
        }

        function testFormatNumber() {
            const num = Math.floor(Math.random() * 1000000);
            const formatted = formatNumber(num);
            showNotification(`${num} formatted as: ${formatted}`, 'info');
        }

        function testAnimateScale() {
            const button = event.target;
            animateScale(button, 300);
            showNotification('Scale animation applied!', 'info');
        }

        async function testButtonLoading() {
            const button = event.target;
            const originalText = setButtonLoading(button, 'Testing...');
            
            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            removeButtonLoading(button, originalText);
            showNotification('Button loading test completed!', 'success');
        }

        async function testApiRequest() {
            try {
                // Test with a simple GET request to check if API is accessible
                const result = await apiRequest('../api/resources.php');
                showNotification('API request successful!', 'success');
                console.log('API Response:', result);
            } catch (error) {
                showNotification('API request failed (expected if not logged in)', 'warning');
                console.log('API Error:', error);
            }
        }

        // Override goToStore for test page
        function goToStore(resourceType) {
            showNotification(`Would navigate to store for: ${resourceType}`, 'info');
            console.log(`goToStore called with: ${resourceType}`);
            
            // Add animation effect
            if (event && event.target) {
                animateScale(event.target, 200);
            }
        }

        // Log when shared functions are loaded
        console.log('Shared functions test page loaded');
        showNotification('Test page loaded successfully!', 'success');
    </script>
</body>
</html>
