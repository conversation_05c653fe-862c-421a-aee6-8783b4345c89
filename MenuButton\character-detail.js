/**
 * Character Detail Page JavaScript
 * Handles tab navigation and character interactions
 */

// ===== TAB NAVIGATION =====

/**
 * Show specific tab and hide others
 * @param {string} tabName - Name of the tab to show
 */
function showTab(tabName) {
    // Hide all tab panels
    const tabPanels = document.querySelectorAll('.tab-panel');
    tabPanels.forEach(panel => {
        panel.classList.remove('active');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab panel
    const selectedPanel = document.getElementById(`${tabName}-tab`);
    if (selectedPanel) {
        selectedPanel.classList.add('active');
    }

    // Add active class to clicked button
    const clickedButton = event.target;
    if (clickedButton) {
        clickedButton.classList.add('active');
    }

    // Add animation effect
    animateTabTransition(selectedPanel);
}

/**
 * Animate tab transition
 * @param {HTMLElement} panel - Tab panel element
 */
function animateTabTransition(panel) {
    if (!panel) return;
    
    panel.style.opacity = '0';
    panel.style.transform = 'translateY(10px)';
    
    setTimeout(() => {
        panel.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        panel.style.opacity = '1';
        panel.style.transform = 'translateY(0)';
    }, 50);
}

// ===== CHARACTER ACTIONS =====

/**
 * Load upgrade information for character
 * @param {number} characterId - ID of the character
 */
async function loadUpgradeInfo(characterId) {
    try {
        const response = await fetch(`../api/character_upgrade.php?action=get_upgrade_info&character_id=${characterId}`);
        const result = await response.json();

        if (result.success) {
            return result;
        } else {
            console.error('Failed to load upgrade info:', result.message);
            return null;
        }
    } catch (error) {
        console.error('Error loading upgrade info:', error);
        return null;
    }
}

/**
 * Handle character upgrade action
 * @param {number} characterId - ID of the character to upgrade
 */
async function upgradeCharacter(characterId) {
    const button = event.target.closest('.action-btn');
    const originalText = setButtonLoading(button, 'Upgrading...');

    try {
        // Load current upgrade info
        const upgradeInfo = await loadUpgradeInfo(characterId);

        if (!upgradeInfo || !upgradeInfo.upgrade_requirements.can_upgrade) {
            removeButtonLoading(button, originalText);
            showNotification('Cannot upgrade character at this time', 'error');
            return;
        }

        const character = upgradeInfo.character;
        const requirements = upgradeInfo.upgrade_requirements;

        // Show confirmation dialog
        const confirmMessage = `Upgrade ${character.name} from Level ${character.current_level} to Level ${character.next_level}?\n\nThis will use ${requirements.required_logs} Experience Log(s).\nYou have: ${requirements.owned_logs}`;

        if (!confirm(confirmMessage)) {
            removeButtonLoading(button, originalText);
            return;
        }

        // Make API call to upgrade character
        const response = await fetch('../api/character_upgrade.php?action=upgrade_character', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                character_id: characterId
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');

            // Reload page after successful upgrade to show new stats
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            removeButtonLoading(button, originalText);
            showNotification(result.message || 'Failed to upgrade character', 'error');
        }

    } catch (error) {
        console.error('Error upgrading character:', error);
        removeButtonLoading(button, originalText);
        showNotification('Network error occurred', 'error');
    }
}

// Equipment and battle functions removed - focusing on upgrade functionality only

// ===== INTERACTIVE EFFECTS =====

/**
 * Add hover effects to skill items
 */
function initializeSkillEffects() {
    const skillItems = document.querySelectorAll('.skill-item');
    
    skillItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            // Removed: this.style.transform = 'translateY(-3px) scale(1.02)';
            this.style.boxShadow = '0 8px 25px rgba(0, 188, 212, 0.2)';
        });
        
        item.addEventListener('mouseleave', function() {
            // Removed: this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}

/**
 * Add hover effects to stat items
 */
function initializeStatEffects() {
    const statItems = document.querySelectorAll('.stat-item');
    
    statItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(0, 188, 212, 0.1)';
            this.style.borderColor = 'rgba(0, 188, 212, 0.3)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 255, 255, 0.05)';
            this.style.borderColor = 'rgba(255, 255, 255, 0.1)';
        });
    });
}

/**
 * Add click effects to equipment items
 */
function initializeEquipmentEffects() {
    const equipmentItems = document.querySelectorAll('.equipment-item');
    
    equipmentItems.forEach(item => {
        item.addEventListener('click', function() {
            animateScale(this, 200);
            showNotification('Equipment details feature coming soon!', 'info');
        });
        
        item.style.cursor = 'pointer';
        
        item.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(255, 152, 0, 0.1)';
            this.style.borderColor = 'rgba(255, 152, 0, 0.3)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 255, 255, 0.05)';
            this.style.borderColor = 'rgba(255, 255, 255, 0.1)';
        });
    });
}

/**
 * Add effects to constellation bonuses
 */
function initializeConstellationEffects() {
    const bonusItems = document.querySelectorAll('.bonus-item');
    
    bonusItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(76, 175, 80, 0.2)';
            this.style.transform = 'translateX(5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(76, 175, 80, 0.1)';
            this.style.transform = 'translateX(0)';
        });
    });
}

// ===== KEYBOARD NAVIGATION =====

/**
 * Handle keyboard navigation for tabs
 */
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const activeTab = document.querySelector('.tab-btn.active');
        
        if (!activeTab) return;
        
        let currentIndex = Array.from(tabButtons).indexOf(activeTab);
        let newIndex = currentIndex;
        
        switch(e.key) {
            case 'ArrowLeft':
                newIndex = currentIndex > 0 ? currentIndex - 1 : tabButtons.length - 1;
                break;
            case 'ArrowRight':
                newIndex = currentIndex < tabButtons.length - 1 ? currentIndex + 1 : 0;
                break;
            default:
                return;
        }
        
        e.preventDefault();
        tabButtons[newIndex].click();
        tabButtons[newIndex].focus();
    });
}

// ===== PARTICLE EFFECTS =====

/**
 * Create floating particles for element theme
 */
function createElementParticles() {
    const container = document.querySelector('.character-detail-container');
    if (!container) return;
    
    // Get element from character data (if available)
    const elementBadge = document.querySelector('.element-badge');
    let elementType = 'default';
    
    if (elementBadge) {
        const elementText = elementBadge.textContent.toLowerCase();
        if (elementText.includes('fire')) elementType = 'fire';
        else if (elementText.includes('ice')) elementType = 'ice';
        else if (elementText.includes('lightning')) elementType = 'lightning';
    }
    
    // Create particles based on element
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            createParticle(container, elementType);
        }, i * 200);
    }
    
    // Continue creating particles periodically
    setInterval(() => {
        createParticle(container, elementType);
    }, 3000);
}

/**
 * Create individual particle
 * @param {HTMLElement} container - Container element
 * @param {string} elementType - Element type for particle styling
 */
function createParticle(container, elementType) {
    const particle = document.createElement('div');
    particle.className = 'element-particle';
    
    // Set particle appearance based on element
    let particleChar = '✨';
    let particleColor = '#ffffff';
    
    switch(elementType) {
        case 'fire':
            particleChar = Math.random() > 0.5 ? '🔥' : '✨';
            particleColor = '#ff5722';
            break;
        case 'ice':
            particleChar = Math.random() > 0.5 ? '❄️' : '✨';
            particleColor = '#00bcd4';
            break;
        case 'lightning':
            particleChar = Math.random() > 0.5 ? '⚡' : '✨';
            particleColor = '#ffeb3b';
            break;
    }
    
    particle.textContent = particleChar;
    particle.style.cssText = `
        position: fixed;
        font-size: ${Math.random() * 10 + 10}px;
        color: ${particleColor};
        pointer-events: none;
        z-index: 1000;
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight + 20}px;
        opacity: 0.7;
        animation: floatUp ${Math.random() * 3 + 4}s linear forwards;
    `;
    
    container.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 7000);
}

// Add CSS for particle animation
const style = document.createElement('style');
style.textContent = `
    @keyframes floatUp {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 0.7;
        }
        50% {
            opacity: 1;
        }
        100% {
            transform: translateY(-${window.innerHeight + 100}px) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// ===== INITIALIZATION =====

/**
 * Initialize all character detail page functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize interactive effects
    initializeSkillEffects();
    initializeStatEffects();
    initializeEquipmentEffects();
    initializeConstellationEffects();
    
    // Initialize keyboard navigation
    initializeKeyboardNavigation();
    
    // Create element particles
    createElementParticles();
    
    // Add focus effects to action buttons
    const actionButtons = document.querySelectorAll('.action-btn:not(.disabled)');
    actionButtons.forEach(button => {
        button.addEventListener('focus', function() {
            this.style.outline = '2px solid #00bcd4';
            this.style.outlineOffset = '2px';
        });
        
        button.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });
    
    // Add smooth scrolling for tab content
    const tabContent = document.querySelector('.tab-content');
    if (tabContent) {
        tabContent.style.scrollBehavior = 'smooth';
    }
    
    console.log('Character detail page initialized successfully');
});

// ===== EXPORT FOR MODULE USAGE =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showTab,
        upgradeCharacter
    };
}

