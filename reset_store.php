<?php
// Reset store script - clears existing items and reinitializes
require_once 'config/database.php';
require_once 'includes/store_manager.php';

echo "Resetting store...\n";

$conn = getDatabaseConnection();

// Clear existing store items
$conn->query("DELETE FROM store_items");
echo "Cleared existing store items.\n";

// Clear existing purchases (optional - comment out if you want to keep purchase history)
$conn->query("DELETE FROM user_purchases");
echo "Cleared existing purchases.\n";

$conn->close();

// Initialize new store items
StoreManager::initializeDefaultStoreItems();

echo "Store reset complete! New items initialized with diamond pricing system.\n";
?>
