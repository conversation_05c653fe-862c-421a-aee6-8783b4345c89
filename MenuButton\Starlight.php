<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include required managers
require_once '../includes/resource_manager.php';
require_once '../includes/starlight_manager.php';

// Get username and user ID
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Initialize starlight rewards if not exists
StarlightManager::initializeDefaultRewards();

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Get user starlight progress
$starlight_progress = StarlightManager::getUserStarlightProgress($user_id);

// Get all starlight rewards
$all_rewards = StarlightManager::getAllStarlightRewards();

// Check if user is premium
$is_premium = StarlightManager::isUserPremium($user_id);

// Calculate progress percentage
$progress_percentage = $starlight_progress['exp_to_next_level'] > 0 ?
    ($starlight_progress['current_exp'] / $starlight_progress['exp_to_next_level']) * 100 : 0;

// Group rewards by level
$rewards_by_level = [];
foreach ($all_rewards as $reward) {
    $level = $reward['level'];
    if (!isset($rewards_by_level[$level])) {
        $rewards_by_level[$level] = [];
    }
    $rewards_by_level[$level][] = $reward;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Starlight</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="starlight.css">
</head>
<body>
    <!-- Animated Gaming Background -->
    <div class="gaming-background">
        <!-- Floating Particles -->
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>

        <!-- Neon Orbs -->
        <div class="neon-orb"></div>
        <div class="neon-orb"></div>
        <div class="neon-orb"></div>
    </div>

    <div class="starlight-container">
        <!-- Top navigation bar -->
        <div class="top-nav">
            <div class="top-left">
                <a href="../menu/home.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title">Starlight</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main content area -->
        <div class="starlight-content">
            <!-- Battle Pass Header -->
            <div class="starlight-header">
                <div class="starlight-info">
                    <div class="starlight-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="starlight-details">
                        <h2 class="battle-pass-title">Battle Pass</h2>
                        <p class="battle-pass-subtitle">Collect amazing rewards as you progress through levels</p>
                    </div>
                </div>

                <div class="level-progress">
                    <div class="level-info">
                        <span class="current-level">Level <?php echo $starlight_progress['current_level']; ?></span>
                        <span class="exp-text"><?php echo $starlight_progress['current_exp']; ?> / <?php echo $starlight_progress['exp_to_next_level']; ?> EXP</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo $progress_percentage; ?>%"></div>
                    </div>
                </div>

                <div class="premium-status <?php echo $is_premium ? 'premium' : 'free'; ?>">
                    <i class="fas fa-<?php echo $is_premium ? 'crown' : 'user'; ?>"></i>
                    <span><?php echo $is_premium ? 'Premium Active' : 'Free Player'; ?></span>
                    <?php if (!$is_premium): ?>
                        <a href="../MenuButton/store.php" class="premium-upgrade">
                            <i class="fas fa-crown"></i>
                            Upgrade to Premium
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Battle Pass Wrapper -->
            <div class="battle-pass-wrapper">
                <!-- Gift Character -->
                <div class="gift-character">
                    <div class="gift-character-icon">🎁</div>
                    <div>
                        <div class="gift-character-text">Battle Pass Rewards</div>
                        <div class="gift-character-subtitle">Claim amazing rewards as you level up!</div>
                    </div>
                </div>

                <!-- Battle Pass Container -->
                <div class="battle-pass-container">
                <!-- Level Numbers Row -->
                <div class="level-numbers">
                    <?php for ($level = 1; $level <= 10; $level++): ?>
                        <?php
                        $level_unlocked = $starlight_progress['current_level'] >= $level;
                        $is_current = $starlight_progress['current_level'] == $level;
                        $is_completed = $starlight_progress['current_level'] > $level;
                        $level_class = '';
                        if ($is_current) $level_class = 'current';
                        else if ($is_completed) $level_class = 'completed';
                        ?>
                        <div class="level-number-item <?php echo $level_class; ?>">
                            <?php echo $level; ?>
                            <?php if ($is_current): ?>
                                <div class="current-level-indicator">Current</div>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                </div>

                <!-- Free Tier Row -->
                <div class="tier-row">
                    <div class="tier-header">
                        <div class="tier-header-left">
                            <div class="tier-label free">Free</div>
                            <span>Available to all players</span>
                        </div>
                    </div>
                    <div class="tier-rewards">
                        <?php for ($level = 1; $level <= 10; $level++): ?>
                            <?php
                            $level_rewards = $rewards_by_level[$level] ?? [];
                            $free_reward = null;

                            foreach ($level_rewards as $reward) {
                                if ($reward['tier'] === 'free') {
                                    $free_reward = $reward;
                                    break;
                                }
                            }

                            $level_unlocked = $starlight_progress['current_level'] >= $level;
                            $free_claimed = in_array($level, $starlight_progress['claimed_free_rewards']);

                            $slot_class = '';
                            $indicator_class = '';
                            $indicator_icon = '';

                            if ($free_claimed) {
                                $slot_class = 'claimed';
                                $indicator_class = 'claimed';
                                $indicator_icon = '✓';
                            } else if ($level_unlocked && $free_reward) {
                                $slot_class = 'available';
                                $indicator_class = 'available';
                                $indicator_icon = '!';
                            } else {
                                $slot_class = 'locked';
                                $indicator_class = 'locked';
                                $indicator_icon = '🔒';
                            }
                            ?>
                            <div class="reward-slot <?php echo $slot_class; ?>"
                                 onclick="<?php echo ($level_unlocked && !$free_claimed && $free_reward) ? "claimReward({$free_reward['id']}, 'free', {$level})" : ''; ?>">

                                <?php if ($free_reward): ?>
                                    <div class="reward-icon">
                                        <?php
                                        switch($free_reward['reward_type']) {
                                            case 'coins': echo '🪙'; break;
                                            case 'diamonds': echo '💎'; break;
                                            case 'energy': echo '⚡'; break;
                                            case 'item': echo '📦'; break;
                                            default: echo '🎁'; break;
                                        }
                                        ?>
                                    </div>
                                    <div class="reward-amount">
                                        <?php
                                        if ($free_reward['reward_type'] === 'item') {
                                            echo htmlspecialchars($free_reward['reward_item_name']);
                                        } else {
                                            echo number_format($free_reward['reward_amount']);
                                        }
                                        ?>
                                    </div>
                                    <div class="reward-tooltip">
                                        Level <?php echo $level; ?> Free Reward
                                    </div>
                                <?php else: ?>
                                    <div class="reward-icon">❌</div>
                                    <div class="reward-amount">No Reward</div>
                                <?php endif; ?>

                                <div class="claim-indicator <?php echo $indicator_class; ?>">
                                    <?php echo $indicator_icon; ?>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>

                <!-- Premium Tier Row -->
                <div class="tier-row">
                    <div class="tier-header">
                        <div class="tier-header-left">
                            <div class="tier-label premium">Premium</div>
                            <span>Exclusive rewards for premium members</span>
                        </div>
                        <div class="tier-header-right">
                            <?php if (!$is_premium): ?>
                                <a href="../MenuButton/store.php" class="premium-upgrade">
                                    <i class="fas fa-crown"></i>
                                    Get Premium
                                </a>
                            <?php else: ?>
                                <span style="color: var(--premium-gold); font-weight: 600;">
                                    <i class="fas fa-crown"></i> Premium Active
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="tier-rewards">
                        <?php for ($level = 1; $level <= 10; $level++): ?>
                            <?php
                            $level_rewards = $rewards_by_level[$level] ?? [];
                            $premium_reward = null;

                            foreach ($level_rewards as $reward) {
                                if ($reward['tier'] === 'premium') {
                                    $premium_reward = $reward;
                                    break;
                                }
                            }

                            $level_unlocked = $starlight_progress['current_level'] >= $level;
                            $premium_claimed = in_array($level, $starlight_progress['claimed_premium_rewards']);

                            $slot_class = '';
                            $indicator_class = '';
                            $indicator_icon = '';

                            if ($premium_claimed) {
                                $slot_class = 'claimed';
                                $indicator_class = 'claimed';
                                $indicator_icon = '✓';
                            } else if ($level_unlocked && $is_premium && $premium_reward) {
                                $slot_class = 'available';
                                $indicator_class = 'available';
                                $indicator_icon = '!';
                            } else if ($level_unlocked && !$is_premium && $premium_reward) {
                                $slot_class = 'premium-locked';
                                $indicator_class = 'premium-required';
                                $indicator_icon = '👑';
                            } else {
                                $slot_class = 'locked';
                                $indicator_class = 'locked';
                                $indicator_icon = '🔒';
                            }
                            ?>
                            <div class="reward-slot <?php echo $slot_class; ?>"
                                 onclick="<?php
                                    if ($level_unlocked && !$premium_claimed && $premium_reward) {
                                        if ($is_premium) {
                                            echo "claimReward({$premium_reward['id']}, 'premium', {$level})";
                                        } else {
                                            echo "showPremiumRequired()";
                                        }
                                    }
                                 ?>">

                                <?php if ($premium_reward): ?>
                                    <div class="reward-icon">
                                        <?php
                                        switch($premium_reward['reward_type']) {
                                            case 'coins': echo '🪙'; break;
                                            case 'diamonds': echo '💎'; break;
                                            case 'energy': echo '⚡'; break;
                                            case 'item': echo '📦'; break;
                                            default: echo '🎁'; break;
                                        }
                                        ?>
                                    </div>
                                    <div class="reward-amount">
                                        <?php
                                        if ($premium_reward['reward_type'] === 'item') {
                                            echo htmlspecialchars($premium_reward['reward_item_name']);
                                        } else {
                                            echo number_format($premium_reward['reward_amount']);
                                        }
                                        ?>
                                    </div>
                                    <div class="reward-tooltip">
                                        Level <?php echo $level; ?> Premium Reward
                                    </div>
                                <?php else: ?>
                                    <div class="reward-icon">❌</div>
                                    <div class="reward-amount">No Reward</div>
                                <?php endif; ?>

                                <div class="claim-indicator <?php echo $indicator_class; ?>">
                                    <?php echo $indicator_icon; ?>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
                </div> <!-- Close battle-pass-container -->
            </div> <!-- Close battle-pass-wrapper -->
        </div>
    </div>

    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <script>
        // Global variables
        let isClaimingReward = false;

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Show premium required message
        function showPremiumRequired() {
            showNotification('Premium access required to claim this reward!', 'error');
        }

        // Claim starlight reward (updated for battle pass layout)
        async function claimReward(rewardId, tier, level) {
            if (isClaimingReward) return;

            // Find the reward slot for this level and tier
            const tierRows = document.querySelectorAll('.tier-row');
            let rewardSlot = null;

            tierRows.forEach(row => {
                const tierLabel = row.querySelector('.tier-label');
                if (tierLabel && tierLabel.textContent.toLowerCase().includes(tier.toLowerCase())) {
                    const slots = row.querySelectorAll('.reward-slot');
                    if (slots[level - 1]) {
                        rewardSlot = slots[level - 1];
                    }
                }
            });

            if (!rewardSlot) return;

            const indicator = rewardSlot.querySelector('.claim-indicator');
            const originalIndicator = indicator.innerHTML;
            isClaimingReward = true;

            // Show loading state
            indicator.innerHTML = '⏳';
            indicator.className = 'claim-indicator loading';

            try {
                const response = await fetch('../api/starlight.php?action=claim', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        reward_id: rewardId,
                        level: level,
                        tier: tier
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Update reward slot state
                    rewardSlot.classList.remove('available', 'premium-locked');
                    rewardSlot.classList.add('claimed');
                    indicator.innerHTML = '✓';
                    indicator.className = 'claim-indicator claimed';

                    // Show success notification
                    let rewardText = 'Reward claimed!';
                    if (result.reward) {
                        const reward = result.reward;
                        if (reward.reward_type === 'item') {
                            rewardText = `Claimed: ${reward.reward_item_name}`;
                        } else {
                            rewardText = `Claimed: ${reward.reward_amount.toLocaleString()} ${reward.reward_type}`;
                        }
                    }
                    showNotification(rewardText, 'success');

                    // Update resource display if applicable
                    if (result.reward && result.reward.reward_type !== 'item') {
                        const currentResources = {
                            coins: parseInt(document.getElementById('coins-count').textContent.replace(/,/g, '')),
                            diamonds: parseInt(document.getElementById('diamonds-count').textContent.replace(/,/g, '')),
                            energy: parseInt(document.getElementById('energy-count').textContent)
                        };

                        switch (result.reward.reward_type) {
                            case 'coins':
                                currentResources.coins += result.reward.reward_amount;
                                break;
                            case 'diamonds':
                                currentResources.diamonds += result.reward.reward_amount;
                                break;
                            case 'energy':
                                currentResources.energy += result.reward.reward_amount;
                                break;
                        }

                        updateResourceDisplay(currentResources);
                    }

                    // Remove click handler
                    rewardSlot.onclick = null;

                } else {
                    showNotification(result.message || 'Failed to claim reward', 'error');
                    indicator.innerHTML = originalIndicator;
                    indicator.className = 'claim-indicator available';
                }

            } catch (error) {
                console.error('Error claiming reward:', error);
                showNotification('Network error occurred', 'error');
                indicator.innerHTML = originalIndicator;
                indicator.className = 'claim-indicator available';
            } finally {
                isClaimingReward = false;
            }
        }

        // Interactive Background Effects
        function createInteractiveParticles() {
            const background = document.querySelector('.gaming-background');

            // Mouse move effect
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // Move orbs slightly based on mouse position
                const orbs = document.querySelectorAll('.neon-orb');
                orbs.forEach((orb, index) => {
                    const speed = (index + 1) * 0.5;
                    const x = mouseX * speed;
                    const y = mouseY * speed;
                    orb.style.transform = `translate(${x}px, ${y}px)`;
                });
            });

            // Click effect - create temporary burst
            document.addEventListener('click', function(e) {
                createClickBurst(e.clientX, e.clientY);
            });
        }

        function createClickBurst(x, y) {
            const burst = document.createElement('div');
            burst.style.position = 'fixed';
            burst.style.left = x + 'px';
            burst.style.top = y + 'px';
            burst.style.width = '20px';
            burst.style.height = '20px';
            burst.style.background = 'radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, transparent 70%)';
            burst.style.borderRadius = '50%';
            burst.style.pointerEvents = 'none';
            burst.style.zIndex = '1000';
            burst.style.animation = 'burstEffect 0.6s ease-out forwards';

            document.body.appendChild(burst);

            setTimeout(() => {
                document.body.removeChild(burst);
            }, 600);
        }

        // Add dynamic particles
        function addDynamicParticles() {
            const background = document.querySelector('.gaming-background');

            setInterval(() => {
                if (document.querySelectorAll('.dynamic-particle').length < 15) {
                    const particle = document.createElement('div');
                    particle.className = 'dynamic-particle';
                    particle.style.position = 'absolute';
                    particle.style.width = Math.random() * 4 + 2 + 'px';
                    particle.style.height = particle.style.width;
                    particle.style.background = `rgba(${Math.random() * 255}, ${Math.random() * 255}, 255, 0.6)`;
                    particle.style.borderRadius = '50%';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = '100%';
                    particle.style.pointerEvents = 'none';
                    particle.style.animation = 'floatUp 8s linear forwards';

                    background.appendChild(particle);

                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 8000);
                }
            }, 2000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes burstEffect {
                0% { transform: scale(1); opacity: 1; }
                100% { transform: scale(3); opacity: 0; }
            }

            @keyframes floatUp {
                0% { transform: translateY(0px); opacity: 0.7; }
                100% { transform: translateY(-100vh); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize interactive background
            createInteractiveParticles();
            addDynamicParticles();

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>

    <!-- Include shared functions -->
    <script src="shared-functions.js"></script>
</body>
</html>