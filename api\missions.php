<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit();
}

// Include mission manager
require_once '../includes/mission_manager.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get request method and action
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

$user_id = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                // Get all missions for user
                $missions = MissionManager::getUserMissions($user_id);
                echo json_encode([
                    'success' => true,
                    'missions' => $missions
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if ($action === 'claim') {
                // Claim single mission reward
                $mission_id = $input['mission_id'] ?? 0;
                
                if (!$mission_id) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Mission ID required']);
                    break;
                }
                
                $result = MissionManager::claimMissionReward($user_id, $mission_id);
                echo json_encode($result);
                
            } elseif ($action === 'claim_all') {
                // Claim all available rewards
                $result = MissionManager::claimAllRewards($user_id);
                echo json_encode($result);
                
            } elseif ($action === 'update_progress') {
                // Update mission progress (for testing or game events)
                $mission_id = $input['mission_id'] ?? 0;
                $progress = $input['progress'] ?? 1;
                
                if (!$mission_id) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Mission ID required']);
                    break;
                }
                
                $result = MissionManager::updateMissionProgress($user_id, $mission_id, $progress);
                echo json_encode([
                    'success' => $result,
                    'message' => $result ? 'Progress updated' : 'Failed to update progress'
                ]);
                
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
