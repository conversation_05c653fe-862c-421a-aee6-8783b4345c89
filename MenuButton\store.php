<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include required managers
require_once '../includes/resource_manager.php';
require_once '../includes/store_manager.php';

// Get username and user ID
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Get current category from query parameter
$current_category = $_GET['category'] ?? 'featured';

// Get store items based on category
if ($current_category === 'featured') {
    $store_items = StoreManager::getStoreItems(null, true);
} else {
    $store_items = StoreManager::getStoreItems($current_category);
}

// Categories for navigation
$categories = [
    'featured' => ['name' => 'Featured', 'icon' => '⭐', 'badge' => 'HOT'],
    'diamonds' => ['name' => 'Diamonds', 'icon' => '💎', 'badge' => 'USD'],
    'energy' => ['name' => 'Energy', 'icon' => '⚡', 'badge' => '💎'],
    'characters' => ['name' => 'Characters', 'icon' => '👥', 'badge' => '💎'],
    'items' => ['name' => 'Items', 'icon' => '🎒', 'badge' => '💎'],
    'bundles' => ['name' => 'Bundles', 'icon' => '🎁', 'badge' => 'SAVE']
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Store</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="store.css">
</head>
<body>
    <!-- Animated Gaming Background -->
    <div class="gaming-background">
        <!-- Floating Particles -->
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>

        <!-- Neon Orbs -->
        <div class="neon-orb"></div>
        <div class="neon-orb"></div>
        <div class="neon-orb"></div>
    </div>

    <div class="store-container">
        <!-- Top navigation bar -->
        <div class="top-nav">
            <div class="top-left">
                <a href="../menu/home.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title">MINOS SYSTEM - SUPPLY OFFICE</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main content area -->
        <div class="store-content">
            <!-- Sidebar navigation -->
            <div class="store-sidebar">
                <div class="sidebar-title">Categories</div>
                <div class="category-nav">
                    <?php foreach ($categories as $key => $category): ?>
                        <a href="?category=<?php echo $key; ?>"
                           class="category-item <?php echo $current_category === $key ? 'active' : ''; ?>">
                            <span class="category-icon"><?php echo $category['icon']; ?></span>
                            <span class="category-name"><?php echo $category['name']; ?></span>
                            <?php if ($category['badge']): ?>
                                <span class="category-badge"><?php echo $category['badge']; ?></span>
                            <?php endif; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Main store area -->
            <div class="store-main">
                <!-- Store header -->
                <div class="store-header">
                    <div class="store-title">
                        <?php echo $categories[$current_category]['name']; ?> Items
                    </div>
                    <div class="timer-display">
                        <i class="fas fa-clock timer-icon"></i>
                        <span class="timer-text">Ends in 16d</span>
                    </div>
                </div>

                <!-- Store items grid -->
                <div class="store-items" id="store-items">
                    <?php if (empty($store_items)): ?>
                        <div class="loading">
                            <i class="fas fa-box-open" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                            <p>No items available in this category.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($store_items as $item): ?>
                            <div class="store-item <?php echo $item['is_featured'] ? 'featured' : ''; ?>"
                                 data-item-id="<?php echo $item['id']; ?>">
                                <div class="item-image">
                                    <img src="<?php echo $item['image_url'] ?: '../assets/store/default_item.png'; ?>"
                                         alt="<?php echo htmlspecialchars($item['name']); ?>"
                                         onerror="this.src='../assets/store/default_item.png'">
                                </div>
                                <div class="item-details">
                                    <div class="item-name"><?php echo htmlspecialchars($item['name']); ?></div>
                                    <div class="item-description"><?php echo htmlspecialchars($item['description']); ?></div>

                                    <div class="item-rewards">
                                        <?php if ($item['reward_diamonds'] > 0): ?>
                                            <div class="reward-item">
                                                <span class="reward-icon">💎</span>
                                                <span class="reward-amount"><?php echo number_format($item['reward_diamonds']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($item['reward_coins'] > 0): ?>
                                            <div class="reward-item">
                                                <span class="reward-icon">🪙</span>
                                                <span class="reward-amount"><?php echo number_format($item['reward_coins']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($item['reward_energy'] > 0): ?>
                                            <div class="reward-item">
                                                <span class="reward-icon">⚡</span>
                                                <span class="reward-amount"><?php echo $item['reward_energy']; ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($item['bonus_percentage'] > 0): ?>
                                            <div class="bonus-badge">+<?php echo $item['bonus_percentage']; ?>% Bonus</div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="item-price">
                                        <div class="price-display">
                                            <?php if ($item['price_usd'] > 0): ?>
                                                <!-- Real Money Purchase -->
                                                <span class="current-price">$<?php echo number_format($item['price_usd'], 2); ?></span>
                                                <?php if ($item['original_price_usd'] && $item['discount_percentage'] > 0): ?>
                                                    <span class="original-price">$<?php echo number_format($item['original_price_usd'], 2); ?></span>
                                                    <span class="discount-badge">-<?php echo $item['discount_percentage']; ?>%</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <!-- Diamond Purchase -->
                                                <span class="current-price diamond-price">💎 <?php echo number_format($item['price_diamonds']); ?></span>
                                                <?php if ($item['original_price_diamonds'] > 0 && $item['discount_percentage'] > 0): ?>
                                                    <span class="original-price">💎 <?php echo number_format($item['original_price_diamonds']); ?></span>
                                                    <span class="discount-badge">-<?php echo $item['discount_percentage']; ?>%</span>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <button class="buy-button" onclick="purchaseItem(<?php echo $item['id']; ?>)"
                                            data-price-type="<?php echo $item['price_usd'] > 0 ? 'usd' : 'diamonds'; ?>"
                                            data-price-amount="<?php echo $item['price_usd'] > 0 ? $item['price_usd'] : $item['price_diamonds']; ?>">
                                        <?php if ($item['price_usd'] > 0): ?>
                                            <i class="fas fa-credit-card"></i> Buy with Money
                                        <?php else: ?>
                                            <i class="fas fa-gem"></i> Buy with Diamonds
                                        <?php endif; ?>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <!-- Purchase Modal -->
    <div id="purchase-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Confirm Purchase</h2>
                <span class="close" onclick="closePurchaseModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="purchase-details"></div>
                <div class="payment-methods">
                    <!-- Payment methods will be dynamically populated -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentItem = null;

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Navigate to store page (refresh current page with resource filter)
        function goToStore(resourceType) {
            // Add a small animation effect
            if (event && event.target) {
                event.target.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    event.target.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        event.target.style.transform = '';
                        // Navigate to store page with the specific resource type
                        window.location.href = `store.php?resource=${resourceType}`;
                    }, 100);
                }, 100);
            } else {
                // Fallback without animation
                window.location.href = `store.php?resource=${resourceType}`;
            }
        }

        // Purchase item
        async function purchaseItem(itemId) {
            try {
                // Get item details
                const response = await fetch(`../api/store.php?action=get_item&item_id=${itemId}`);
                const result = await response.json();

                if (result.success) {
                    currentItem = result.item;
                    showPurchaseModal(result.item);
                } else {
                    showNotification(result.message || 'Failed to load item details', 'error');
                }
            } catch (error) {
                console.error('Error loading item:', error);
                showNotification('Network error occurred', 'error');
            }
        }

        // Show purchase modal
        function showPurchaseModal(item) {
            const modal = document.getElementById('purchase-modal');
            const detailsDiv = document.getElementById('purchase-details');

            let rewardsHtml = '';
            if (item.reward_diamonds > 0) rewardsHtml += `<div class="reward">💎 ${parseInt(item.reward_diamonds).toLocaleString()} Diamonds</div>`;
            if (item.reward_coins > 0) rewardsHtml += `<div class="reward">🪙 ${parseInt(item.reward_coins).toLocaleString()} Coins</div>`;
            if (item.reward_energy > 0) rewardsHtml += `<div class="reward">⚡ ${item.reward_energy} Energy</div>`;

            let priceHtml = '';
            let paymentMethodsHtml = '';

            if (parseFloat(item.price_usd) > 0) {
                // Real money purchase
                priceHtml = `<div class="purchase-price">$${parseFloat(item.price_usd).toFixed(2)}</div>`;
                paymentMethodsHtml = `
                    <h3>Select Payment Method:</h3>
                    <div class="payment-options">
                        <button class="payment-btn" data-method="paypal">
                            <i class="fab fa-paypal"></i> PayPal
                        </button>
                        <button class="payment-btn" data-method="stripe">
                            <i class="fas fa-credit-card"></i> Credit Card
                        </button>
                        <button class="payment-btn" data-method="demo">
                            <i class="fas fa-play"></i> Demo Purchase
                        </button>
                    </div>
                `;
            } else {
                // Diamond purchase
                const userDiamonds = parseInt(document.getElementById('diamonds-count').textContent.replace(/,/g, ''));
                const itemCost = parseInt(item.price_diamonds);
                const canAfford = userDiamonds >= itemCost;

                priceHtml = `<div class="purchase-price diamond-price">💎 ${parseInt(item.price_diamonds).toLocaleString()}</div>`;
                if (!canAfford) {
                    priceHtml += `<div class="insufficient-funds">Insufficient diamonds! You need ${(itemCost - userDiamonds).toLocaleString()} more.</div>`;
                }

                paymentMethodsHtml = `
                    <h3>Purchase with Diamonds:</h3>
                    <div class="payment-options">
                        <button class="payment-btn diamond-purchase-btn" data-method="diamonds" ${!canAfford ? 'disabled' : ''}>
                            <i class="fas fa-gem"></i> ${canAfford ? 'Purchase Now' : 'Insufficient Diamonds'}
                        </button>
                    </div>
                `;
            }

            detailsDiv.innerHTML = `
                <div class="purchase-item">
                    <h3>${item.name}</h3>
                    <p>${item.description}</p>
                    <div class="purchase-rewards">${rewardsHtml}</div>
                    ${priceHtml}
                </div>
            `;

            document.querySelector('.payment-methods').innerHTML = paymentMethodsHtml;

            // Re-attach event listeners for new buttons
            document.querySelectorAll('.payment-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const method = this.dataset.method;
                    processPayment(method);
                });
            });

            modal.style.display = 'block';
        }

        // Close purchase modal
        function closePurchaseModal() {
            document.getElementById('purchase-modal').style.display = 'none';
            currentItem = null;
        }

        // Process payment
        async function processPayment(paymentMethod) {
            if (!currentItem) return;

            const button = document.querySelector(`[data-method="${paymentMethod}"]`);
            const originalText = button.innerHTML;

            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

            try {
                const response = await fetch('../api/store.php?action=purchase', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        item_id: currentItem.id,
                        payment_method: paymentMethod
                    })
                });

                const result = await response.json();

                if (result.success) {
                    closePurchaseModal();

                    // Show success notification
                    let rewardText = `Purchase successful! You received: ${result.item_name}`;
                    if (result.rewards) {
                        const rewards = [];
                        if (result.rewards.diamonds) rewards.push(`${result.rewards.diamonds.toLocaleString()} diamonds`);
                        if (result.rewards.coins) rewards.push(`${result.rewards.coins.toLocaleString()} coins`);
                        if (result.rewards.energy) rewards.push(`${result.rewards.energy} energy`);
                        if (rewards.length > 0) {
                            rewardText += ` - ${rewards.join(', ')}`;
                        }
                    }
                    showNotification(rewardText, 'success');

                    // Update resource display
                    if (result.rewards) {
                        const currentResources = {
                            coins: parseInt(document.getElementById('coins-count').textContent.replace(/,/g, '')),
                            diamonds: parseInt(document.getElementById('diamonds-count').textContent.replace(/,/g, '')),
                            energy: parseInt(document.getElementById('energy-count').textContent)
                        };

                        if (result.rewards.coins) currentResources.coins += result.rewards.coins;
                        if (result.rewards.diamonds) currentResources.diamonds += result.rewards.diamonds;
                        if (result.rewards.energy) currentResources.energy += result.rewards.energy;

                        updateResourceDisplay(currentResources);
                    }

                } else {
                    showNotification(result.message || 'Purchase failed', 'error');
                }

            } catch (error) {
                console.error('Error processing payment:', error);
                showNotification('Network error occurred', 'error');
            } finally {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }

        // Interactive Background Effects
        function createInteractiveParticles() {
            const background = document.querySelector('.gaming-background');

            // Mouse move effect
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // Move orbs slightly based on mouse position
                const orbs = document.querySelectorAll('.neon-orb');
                orbs.forEach((orb, index) => {
                    const speed = (index + 1) * 0.5;
                    const x = mouseX * speed;
                    const y = mouseY * speed;
                    orb.style.transform = `translate(${x}px, ${y}px)`;
                });
            });

            // Click effect - create temporary burst
            document.addEventListener('click', function(e) {
                createClickBurst(e.clientX, e.clientY);
            });
        }

        function createClickBurst(x, y) {
            const burst = document.createElement('div');
            burst.style.position = 'fixed';
            burst.style.left = x + 'px';
            burst.style.top = y + 'px';
            burst.style.width = '20px';
            burst.style.height = '20px';
            burst.style.background = 'radial-gradient(circle, rgba(255, 20, 147, 0.8) 0%, transparent 70%)';
            burst.style.borderRadius = '50%';
            burst.style.pointerEvents = 'none';
            burst.style.zIndex = '1000';
            burst.style.animation = 'burstEffect 0.6s ease-out forwards';

            document.body.appendChild(burst);

            setTimeout(() => {
                document.body.removeChild(burst);
            }, 600);
        }

        // Add dynamic particles
        function addDynamicParticles() {
            const background = document.querySelector('.gaming-background');

            setInterval(() => {
                if (document.querySelectorAll('.dynamic-particle').length < 15) {
                    const particle = document.createElement('div');
                    particle.className = 'dynamic-particle';
                    particle.style.position = 'absolute';
                    particle.style.width = Math.random() * 4 + 2 + 'px';
                    particle.style.height = particle.style.width;
                    particle.style.background = `rgba(${Math.random() * 255}, ${Math.random() * 255}, 255, 0.6)`;
                    particle.style.borderRadius = '50%';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = '100%';
                    particle.style.pointerEvents = 'none';
                    particle.style.animation = 'floatUp 8s linear forwards';

                    background.appendChild(particle);

                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 8000);
                }
            }, 2000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize interactive background
            createInteractiveParticles();
            addDynamicParticles();

            // Add payment method click handlers
            document.querySelectorAll('.payment-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const method = this.dataset.method;
                    processPayment(method);
                });
            });

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('purchase-modal');
                if (event.target === modal) {
                    closePurchaseModal();
                }
            });
        });
    </script>

    <!-- Include shared functions -->
    <script src="shared-functions.js"></script>

    <style>
        /* Modal styles */
        .modal {
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: var(--store-bg);
            border: 2px solid var(--store-border);
            border-radius: 15px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            backdrop-filter: blur(10px);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--store-border);
        }

        .modal-header h2 {
            color: var(--text-light);
            margin: 0;
        }

        .close {
            color: var(--text-gray);
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: var(--text-light);
        }

        .purchase-item h3 {
            color: var(--featured-gold);
            margin-bottom: 10px;
        }

        .purchase-item p {
            color: var(--text-gray);
            margin-bottom: 15px;
        }

        .purchase-rewards {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .purchase-rewards .reward {
            background: rgba(0, 0, 0, 0.3);
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            color: var(--text-light);
        }

        .purchase-price {
            font-size: 24px;
            font-weight: bold;
            color: var(--featured-gold);
            text-align: center;
            margin-bottom: 20px;
        }

        .payment-methods h3 {
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .payment-options {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .payment-btn {
            flex: 1;
            min-width: 120px;
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 184, 255, 0.3);
        }

        .payment-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Interactive Effects */
        @keyframes burstEffect {
            0% { transform: scale(0) rotate(0deg); opacity: 1; }
            100% { transform: scale(4) rotate(360deg); opacity: 0; }
        }

        @keyframes floatUp {
            0% { transform: translateY(0) rotate(0deg); opacity: 0.8; }
            100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
        }

        /* Enhanced Gaming Vibes */
        .store-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(90deg, transparent 0%, rgba(138, 43, 226, 0.1) 50%, transparent 100%),
                linear-gradient(0deg, transparent 0%, rgba(255, 20, 147, 0.1) 50%, transparent 100%);
            pointer-events: none;
            z-index: 1;
            animation: scanLines 3s ease-in-out infinite alternate;
        }

        @keyframes scanLines {
            0% { opacity: 0.3; transform: translateX(-100%); }
            100% { opacity: 0.7; transform: translateX(100%); }
        }

        /* Enhance existing elements with gaming glow */
        .store-item:hover {
            box-shadow:
                0 10px 30px rgba(0, 184, 255, 0.2),
                0 0 20px rgba(138, 43, 226, 0.3),
                inset 0 0 20px rgba(255, 20, 147, 0.1);
        }

        .buy-button:hover {
            box-shadow:
                0 8px 25px rgba(255, 68, 68, 0.4),
                0 0 15px rgba(255, 20, 147, 0.5);
        }

        .top-nav {
            backdrop-filter: blur(15px);
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 2px solid rgba(138, 43, 226, 0.5);
            box-shadow: 0 4px 20px rgba(138, 43, 226, 0.2);
        }

        .store-sidebar {
            backdrop-filter: blur(15px);
            background: rgba(20, 25, 35, 0.9);
            border-right: 2px solid rgba(138, 43, 226, 0.3);
            box-shadow: 4px 0 20px rgba(138, 43, 226, 0.1);
        }
    </style>
</body>
</html>