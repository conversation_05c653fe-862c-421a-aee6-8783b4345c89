<?php
// Test page for mission system
session_start();

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    echo "Please log in first. <a href='register/login.php'>Login</a>";
    exit();
}

require_once 'includes/mission_manager.php';
require_once 'includes/resource_manager.php';

$user_id = $_SESSION['user_id'];

echo "<h1>Mission System Test</h1>";
echo "<p>User: " . htmlspecialchars($_SESSION['username']) . " (ID: $user_id)</p>";

// Initialize missions
MissionManager::initializeDefaultMissions();
echo "<p>✓ Missions initialized</p>";

// Get user resources
$resources = ResourceManager::getUserResources($user_id);
echo "<h2>Current Resources:</h2>";
echo "<ul>";
echo "<li>Coins: " . number_format($resources['coins']) . "</li>";
echo "<li>Diamonds: " . number_format($resources['diamonds']) . "</li>";
echo "<li>Energy: " . $resources['energy'] . "</li>";
echo "</ul>";

// Get user missions
$missions = MissionManager::getUserMissions($user_id);
echo "<h2>Missions (" . count($missions) . " total):</h2>";

if (empty($missions)) {
    echo "<p>No missions found.</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Title</th><th>Progress</th><th>Completed</th><th>Claimed</th><th>Rewards</th><th>Actions</th></tr>";
    
    foreach ($missions as $mission) {
        echo "<tr>";
        echo "<td>" . $mission['id'] . "</td>";
        echo "<td>" . htmlspecialchars($mission['title']) . "</td>";
        echo "<td>" . $mission['current_progress'] . "/" . $mission['target_value'] . "</td>";
        echo "<td>" . ($mission['is_completed'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($mission['is_claimed'] ? 'Yes' : 'No') . "</td>";
        
        $rewards = [];
        if ($mission['reward_coins'] > 0) $rewards[] = $mission['reward_coins'] . " coins";
        if ($mission['reward_diamonds'] > 0) $rewards[] = $mission['reward_diamonds'] . " diamonds";
        if ($mission['reward_energy'] > 0) $rewards[] = $mission['reward_energy'] . " energy";
        echo "<td>" . implode(", ", $rewards) . "</td>";
        
        echo "<td>";
        if (!$mission['is_completed']) {
            echo "<a href='?action=progress&mission_id=" . $mission['id'] . "'>Add Progress</a>";
        } elseif (!$mission['is_claimed']) {
            echo "<a href='?action=claim&mission_id=" . $mission['id'] . "'>Claim Reward</a>";
        } else {
            echo "Claimed";
        }
        echo "</td>";
        
        echo "</tr>";
    }
    echo "</table>";
}

// Handle actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $mission_id = (int)$_GET['mission_id'];
    
    if ($action === 'progress') {
        $result = MissionManager::updateMissionProgress($user_id, $mission_id, 1);
        echo "<p>Progress updated: " . ($result ? "Success" : "Failed") . "</p>";
        echo "<script>window.location.href = 'test_missions.php';</script>";
        
    } elseif ($action === 'claim') {
        $result = MissionManager::claimMissionReward($user_id, $mission_id);
        echo "<p>Claim result: " . ($result['success'] ? "Success - " . $result['message'] : "Failed - " . $result['message']) . "</p>";
        echo "<script>window.location.href = 'test_missions.php';</script>";
    }
}

echo "<h2>Quick Actions:</h2>";
echo "<ul>";
echo "<li><a href='?action=claim_all'>Claim All Available Rewards</a></li>";
echo "<li><a href='MenuButton/Mission.php'>Go to Mission Page</a></li>";
echo "<li><a href='menu/home.php'>Go to Home</a></li>";
echo "</ul>";

// Handle claim all
if (isset($_GET['action']) && $_GET['action'] === 'claim_all') {
    $result = MissionManager::claimAllRewards($user_id);
    echo "<p>Claim All Result: " . ($result['success'] ? "Success" : "No rewards to claim") . "</p>";
    if ($result['success']) {
        echo "<p>Claimed " . $result['claimed_count'] . " missions</p>";
        if (!empty($result['total_rewards'])) {
            echo "<p>Total rewards: ";
            $rewards = [];
            if ($result['total_rewards']['coins'] > 0) $rewards[] = $result['total_rewards']['coins'] . " coins";
            if ($result['total_rewards']['diamonds'] > 0) $rewards[] = $result['total_rewards']['diamonds'] . " diamonds";
            if ($result['total_rewards']['energy'] > 0) $rewards[] = $result['total_rewards']['energy'] . " energy";
            echo implode(", ", $rewards) . "</p>";
        }
    }
    echo "<script>setTimeout(() => window.location.href = 'test_missions.php', 2000);</script>";
}
?>
