<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please log in first. <a href='register/login.php'>Login here</a>";
    exit;
}

require_once 'includes/mail_manager.php';

$user_id = $_SESSION['user_id'];

echo "<h2>Testing Mail System</h2>";

// Send test mails
echo "<h3>Sending test mails...</h3>";

// Test mail 1: Welcome message
$mail1 = MailManager::sendMail(
    $user_id,
    "Welcome to JILBOOBS WORLD!",
    "Welcome to our amazing world! We're excited to have you join our adventure. Explore, discover, and enjoy your journey with us!",
    "JILBOOBS Team",
    "system"
);

// Test mail 2: Reward mail with attachment
$attachment_data = [
    'coins' => 1000,
    'diamonds' => 100,
    'energy' => 20
];

$mail2 = MailManager::sendMail(
    $user_id,
    "Daily Login Bonus!",
    "Thank you for logging in today! Here's your daily bonus to help you on your adventure. Don't forget to claim your rewards!",
    "System",
    "reward",
    $attachment_data
);

// Test mail 3: Event notification
$mail3 = MailManager::sendMail(
    $user_id,
    "Special Event: Double XP Weekend!",
    "This weekend only! Earn double experience points in all story chapters. Don't miss this limited-time opportunity to level up faster!",
    "Event Manager",
    "event"
);

// Test mail 4: System notification
$mail4 = MailManager::sendMail(
    $user_id,
    "System Maintenance Notice",
    "We will be performing scheduled maintenance on our servers tomorrow from 2:00 AM to 4:00 AM UTC. During this time, the game may be temporarily unavailable. Thank you for your patience!",
    "System Administrator",
    "notification"
);

echo "<p>✓ Test mail 1 sent: " . ($mail1 ? "Success" : "Failed") . "</p>";
echo "<p>✓ Test mail 2 sent: " . ($mail2 ? "Success" : "Failed") . "</p>";
echo "<p>✓ Test mail 3 sent: " . ($mail3 ? "Success" : "Failed") . "</p>";
echo "<p>✓ Test mail 4 sent: " . ($mail4 ? "Success" : "Failed") . "</p>";

// Get unread count
$unread_count = MailManager::getUnreadCount($user_id);
echo "<h3>Current unread mail count: {$unread_count}</h3>";

// Get all mail
$all_mail = MailManager::getUserMail($user_id);
echo "<h3>All mail for user:</h3>";
echo "<ul>";
foreach ($all_mail as $mail) {
    $status = $mail['is_read'] ? "Read" : "Unread";
    $attachment = $mail['has_attachment'] ? " (Has Attachment)" : "";
    echo "<li><strong>{$mail['subject']}</strong> - {$status}{$attachment} - {$mail['created_at']}</li>";
}
echo "</ul>";

echo "<p><a href='menu/home.php' style='color: #00b8ff; text-decoration: none; font-weight: bold;'>→ Go to Home Page to test the mail system</a></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Mail System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2, h3 {
            color: #333;
        }
        p {
            margin: 10px 0;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
</body>
</html>
