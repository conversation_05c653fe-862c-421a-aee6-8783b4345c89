<?php
/**
 * Migration Script: Remove trophies column from user_resources table
 * Run this file once to remove the trophies column
 */

require_once 'config/database.php';

echo "<h2>Removing trophies column from user_resources table...</h2>";

try {
    $conn = getDatabaseConnection();
    
    // Check if trophies column exists
    $check_sql = "SHOW COLUMNS FROM user_resources LIKE 'trophies'";
    $result = $conn->query($check_sql);
    
    if ($result->num_rows > 0) {
        // Column exists, remove it
        $sql = "ALTER TABLE user_resources DROP COLUMN trophies";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color: green;'>✓ Trophies column removed successfully!</p>";
        } else {
            echo "<p style='color: red;'>✗ Error removing trophies column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ Trophies column does not exist (already removed or never existed).</p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Remove Trophies Column</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2 { color: #333; }
        p { margin: 10px 0; }
    </style>
</head>
<body>
    <a href="menu/home.php">← Back to Game</a>
</body>
</html>
