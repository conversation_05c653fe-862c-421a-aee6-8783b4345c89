/* Store Page - Honkai Star Rail Style CSS */

:root {
    --primary-color: #00b8ff;
    --secondary-color: #ff4d7e;
    --dark-bg: #1a1a1a;
    --card-bg: rgba(40, 44, 52, 0.85);
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --accent-orange: #ff6b00;
    --store-bg: rgba(20, 25, 35, 0.95);
    --store-border: rgba(74, 144, 226, 0.3);
    --buy-button: #ff4444;
    --buy-button-hover: #ff6666;
    --featured-gold: #ffd700;
    --discount-red: #ff3333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-light);
    overflow-x: hidden;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.store-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Animated Gaming Background */
.gaming-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, #1a0033, #330066, #4d0080, #6600cc, #8533ff);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

.gaming-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 255, 0.2) 0%, transparent 50%);
    animation: pulseGlow 4s ease-in-out infinite alternate;
}

.gaming-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%);
    background-size: 20px 20px;
    animation: gridMove 20s linear infinite;
}

/* Floating Particles */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { width: 4px; height: 4px; top: 10%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 6px; height: 6px; top: 20%; left: 80%; animation-delay: 1s; }
.particle:nth-child(3) { width: 3px; height: 3px; top: 60%; left: 20%; animation-delay: 2s; }
.particle:nth-child(4) { width: 5px; height: 5px; top: 80%; left: 70%; animation-delay: 3s; }
.particle:nth-child(5) { width: 4px; height: 4px; top: 30%; left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { width: 7px; height: 7px; top: 70%; left: 90%; animation-delay: 5s; }

/* Neon Orbs */
.neon-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    pointer-events: none;
    animation: orbFloat 8s ease-in-out infinite;
}

.neon-orb:nth-child(1) {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(138, 43, 226, 0.4) 0%, transparent 70%);
    top: 15%;
    left: 85%;
    animation-delay: 0s;
}

.neon-orb:nth-child(2) {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, transparent 70%);
    top: 70%;
    left: 10%;
    animation-delay: 2s;
}

.neon-orb:nth-child(3) {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(0, 191, 255, 0.4) 0%, transparent 70%);
    top: 40%;
    left: 70%;
    animation-delay: 4s;
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes pulseGlow {
    0% { opacity: 0.5; transform: scale(1); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

@keyframes orbFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
    33% { transform: translateY(-30px) scale(1.1); opacity: 0.8; }
    66% { transform: translateY(15px) scale(0.9); opacity: 0.7; }
}

/* Top navigation bar */
.top-nav {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    z-index: 100;
    border-bottom: 2px solid var(--store-border);
}

.top-left {
    display: flex;
    align-items: center;
    gap: 15px;
}



.page-title {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 184, 255, 0.5);
}

.top-right {
    display: flex;
    gap: 20px;
    align-items: center;
}

.resource {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 0, 0.5);
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: bold;
}

.resource-icon {
    font-size: 16px;
}

/* Main content area */
.store-content {
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
}

/* Sidebar navigation */
.store-sidebar {
    width: 250px;
    background: var(--store-bg);
    border-right: 2px solid var(--store-border);
    backdrop-filter: blur(10px);
    padding: 20px;
    overflow-y: auto;
}

.sidebar-title {
    font-size: 20px;
    font-weight: bold;
    color: var(--featured-gold);
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.category-nav {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.category-item {
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
    text-decoration: none;
}

.category-item:hover {
    border-color: var(--primary-color);
    background: rgba(0, 184, 255, 0.1);
    transform: translateX(5px);
}

.category-item.active {
    border-color: var(--featured-gold);
    background: rgba(255, 215, 0, 0.1);
    color: var(--featured-gold);
}

.category-icon {
    font-size: 20px;
    width: 30px;
    text-align: center;
}

.category-name {
    font-weight: bold;
}

.category-badge {
    margin-left: auto;
    background: var(--buy-button);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

/* Main store area */
.store-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.store-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--store-bg);
    border: 2px solid var(--store-border);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.store-title {
    font-size: 28px;
    font-weight: bold;
    color: var(--text-light);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.timer-display {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 215, 0, 0.1);
    padding: 10px 20px;
    border-radius: 10px;
    border: 2px solid var(--featured-gold);
}

.timer-icon {
    color: var(--featured-gold);
    font-size: 20px;
}

.timer-text {
    color: var(--featured-gold);
    font-weight: bold;
}

/* Store items grid */
.store-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.store-item {
    background: var(--store-bg);
    border: 2px solid var(--store-border);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    backdrop-filter: blur(10px);
}

.store-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 184, 255, 0.2);
    transform: translateY(-5px);
}

.store-item.featured {
    border-color: var(--featured-gold);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.store-item.featured::before {
    content: "FEATURED";
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--featured-gold);
    color: #000;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.item-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #2a2a3e, #1a1a2e);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.item-image img {
    width: 80%;
    height: 80%;
    object-fit: contain;
    filter: drop-shadow(0 0 20px rgba(0, 184, 255, 0.3));
}

.item-image::after {
    content: "🎁";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 48px;
    opacity: 0.3;
    pointer-events: none;
}

.item-image::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.item-details {
    padding: 20px;
}

.item-name {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-light);
    margin-bottom: 8px;
}

.item-description {
    font-size: 14px;
    color: var(--text-gray);
    margin-bottom: 15px;
    line-height: 1.4;
}

.item-rewards {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.reward-item {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.reward-icon {
    font-size: 14px;
}

.reward-amount {
    font-size: 12px;
    font-weight: bold;
    color: var(--text-light);
}

.item-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.price-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-price {
    font-size: 24px;
    font-weight: bold;
    color: var(--featured-gold);
}

.diamond-price {
    color: var(--primary-color) !important;
    text-shadow: 0 0 10px rgba(0, 184, 255, 0.5);
}

.insufficient-funds {
    color: var(--buy-button);
    font-size: 14px;
    margin-top: 5px;
    text-align: center;
}

.diamond-purchase-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.diamond-purchase-btn:hover:not(:disabled) {
    box-shadow: 0 5px 15px rgba(0, 184, 255, 0.3) !important;
}

.original-price {
    font-size: 16px;
    color: var(--text-gray);
    text-decoration: line-through;
}

.discount-badge {
    background: var(--discount-red);
    color: white;
    padding: 3px 8px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: bold;
}

.bonus-badge {
    background: var(--completed-green);
    color: white;
    padding: 3px 8px;
    border-radius: 5px;
    font-size: 12px;
    font-weight: bold;
}

.buy-button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, var(--buy-button), var(--buy-button-hover));
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.buy-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 68, 68, 0.4);
}

.buy-button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading and notification styles */
.loading {
    text-align: center;
    padding: 40px;
    color: var(--text-gray);
}

.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, var(--completed-green), #00cc66);
}

.notification.error {
    background: linear-gradient(135deg, var(--buy-button), var(--buy-button-hover));
}

/* Responsive design */
@media (max-width: 768px) {
    .store-content {
        flex-direction: column;
    }
    
    .store-sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
    }
    
    .category-nav {
        flex-direction: row;
        overflow-x: auto;
        gap: 5px;
    }
    
    .category-item {
        min-width: 120px;
        padding: 10px 15px;
    }
    
    .store-items {
        grid-template-columns: 1fr;
    }
    
    .store-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}
