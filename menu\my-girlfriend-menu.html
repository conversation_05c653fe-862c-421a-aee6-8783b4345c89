
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Girlfriend - Visual Novel</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;700&display=swap');

    :root {
      --primary-color: #ff4d7e;
      --primary-dark: #d81b60;
      --secondary-color: #f06292;
      --text-light: #fff5f8;
      --text-dark: #2d2d3a;
      --bg-dark: #2c1e30;
      --bg-light: #fff0f5;
      --accent: #ffb6c1;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Montserrat', sans-serif;
      background-color: var(--bg-dark);
      color: var(--text-light);
      overflow: hidden;
      height: 100vh;
    }

    #menu-container {
      width: 100%;
      height: 100vh;
      background-color: var(--bg-dark);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      position: relative;
      padding: 3rem 0;
    }

    /* Even bigger centered image container */
    .image-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 1100px;
      height: 450px; /* 16:9 aspect ratio */
      background: linear-gradient(rgba(0, 0, 0, 0.074), rgba(0, 0, 0, 0.293)),
        url('../assets/Main-menuback.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 15px;
      box-shadow: 0 50px 150px rgba(197, 124, 229, 0.507);
      z-index: 1;
      opacity: 0.45; /* Added transparency to the entire image container */
    }

    /* Animated particles overlay */
    #menu-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('../assets/particles.png');
      background-size: cover;
      opacity: 0.15;
      animation: floatingParticles 60s linear infinite;
    }

    @keyframes floatingParticles {
      0% {
        background-position: 0 0;
      }

      100% {
        background-position: 100% 100%;
      }
    }


    /* Title section at top */
    .title-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      z-index: 2;
      margin-top: -1rem;
    }

    /* Button section at bottom */
    .button-section {
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 2;
      margin-bottom: 2rem;
    }

    @keyframes borderGlow {
      0% {
        background-position: 0% 0%;
      }

      50% {
        background-position: 100% 100%;
      }

      100% {
        background-position: 0% 0%;
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(30px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .game-title {
      font-family: 'Playfair Display', serif;
      font-size: 5rem;
      margin-bottom: 2rem;
      color: var(--text-light);
      text-shadow: 0 0 15px rgba(255, 107, 107, 0.7),
        0 0 30px rgba(0, 0, 0, 0.5);
      letter-spacing: 3px;
      line-height: 1.2;
      position: relative;
      display: inline-block;
      animation: titlePulse 3s infinite ease-in-out;
      text-align: center;
    }

    @keyframes titlePulse {
      0% {
        text-shadow: 0 0 15px rgba(255, 107, 107, 0.7), 0 0 30px rgba(0, 0, 0, 0.5);
      }

      50% {
        text-shadow: 0 0 25px rgba(255, 107, 107, 0.9), 0 0 40px rgba(0, 0, 0, 0.7);
      }

      100% {
        text-shadow: 0 0 15px rgba(255, 107, 107, 0.7), 0 0 30px rgba(0, 0, 0, 0.5);
      }
    }

    .game-title::after {
      content: '';
      position: absolute;
      width: 80%;
      height: 3px;
      background: linear-gradient(to right, transparent, var(--accent), transparent);
      bottom: -10px;
      left: 10%;
      border-radius: 50%;
    }

    .game-subtitle {
      font-family: 'Montserrat', sans-serif;
      font-size: 1.8rem;
      font-weight: 400;
      margin-bottom: 3rem;
      color: var(--accent);
      text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      letter-spacing: 2px;
      opacity: 0;
      animation: fadeInDelayed 1.5s ease-out forwards;
      animation-delay: 0.5s;
      text-align: center;
    }

    @keyframes fadeInDelayed {
      from {
        opacity: 0;
        transform: translateY(10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .button-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
      width: 100%;
    }



    .menu-button {
      padding: 1.5rem 3rem;
      font-size: 1.8rem;
      background: linear-gradient(45deg, rgba(218, 124, 124, 0.2), rgba(218, 124, 124, 0.2));
      color: white;
      border: none;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      font-weight: 600;
      letter-spacing: 2px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 1px rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;
      opacity: 0;
      animation: buttonAppear 0.5s ease-out forwards;
      text-transform: uppercase;
      /* Remove border-radius and add clip-path */
      clip-path: polygon(
        0% 20%,
        10% 0%,
        90% 0%,
        100% 20%,
        100% 80%,
        90% 100%,
        10% 100%,
        0% 80%
      );
    }

    /* Add button glow effect */
    .menu-button::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.2) 50%, 
        rgba(255, 255, 255, 0) 100%);
      z-index: 1;
      transform: translateX(-100%);
      transition: transform 0.6s;
    }

    .menu-button:hover::after {
      transform: translateX(100%);
    }

    /* Add button border effect */
    .menu-button::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      right: 2px;
      bottom: 2px;
      background: transparent;
      z-index: -1;
      clip-path: polygon(
        0% 20%, 
        10% 0%, 
        90% 0%, 
        100% 20%, 
        100% 80%, 
        90% 100%, 
        10% 100%, 
        0% 80%
      );
      border: 2px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    @keyframes buttonAppear {
      from {
        opacity: 0;
        transform: translateX(-30px);
      }

      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .menu-button:hover {
      transform: translateY(-7px) scale(1.03);
      box-shadow: 0 15px 25px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 107, 107, 0.4);
      letter-spacing: 3px;
      background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
    }

    .menu-button:active {
      transform: translateY(2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    }

    /* Floating hearts animation */
    .floating-hearts {
      position: absolute;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .heart {
      position: absolute;
      width: 20px;
      height: 20px;
      background: url('../assets/heart.png');
      background-size: contain;
      opacity: 0.6;
      animation: floatHeart 15s linear infinite;
    }

    @keyframes floatHeart {
      0% {
        transform: translateY(100vh) scale(0.5) rotate(0deg);
        opacity: 0;
      }

      10% {
        opacity: 0.6;
      }

      90% {
        opacity: 0.6;
      }

      100% {
        transform: translateY(-100px) scale(1.2) rotate(360deg);
        opacity: 0;
      }
    }

    /* Social media icons */
    .social-icons {
      position: absolute;
      bottom: 20px;
      right: 20px;
      display: flex;
      gap: 15px;
    }

    .social-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(5px);
    }

    .social-icon:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.2);
    }
    
    /* Character image - hidden for centered layout */
    .character-image {
      display: none;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .game-title {
        font-size: 2.8rem;
      }

      .game-subtitle {
        font-size: 1.3rem;
      }

      .menu-button {
        font-size: 1.2rem;
        padding: 1rem 2rem;
      }

      .social-icons {
        bottom: 15px;
        right: 15px;
      }

      .social-icon {
        width: 35px;
        height: 35px;
      }
    }
  </style>
</head>

<body>
  <div id="menu-container">
    <!-- Floating hearts animation -->
    <div class="floating-hearts" id="hearts-container"></div>

    <!-- Small centered image container -->
    <div class="image-container"></div>

    <!-- Title section at top -->
    <div class="title-section">
      <h1 class="game-title">JILBOOBS WORLD</h1>
      <h2 class="game-subtitle">Novel Visual Romantik</h2>
    </div>

    <!-- Button section at bottom -->
    <div class="button-section">
      <button class="menu-button" onclick="window.location.href='../register/login.php'">
        <span>Start Game</span>
      </button>
    </div>

    <div class="social-icons">
      <div class="social-icon" onclick="window.open('https://x.com')">
        <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      </div>
      <div class="social-icon" onclick="window.open('https://instagram.com')">
        <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
        </svg>
      </div>
      <div class="social-icon" onclick="window.open('https://tiktok.com')">
        <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
          <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-1-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"/>
        </svg>
      </div>
      <div class="social-icon" onclick="window.open('https://reddit.com')">
        <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
          <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.************* 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"/>
        </svg>
      </div>
    </div>
  </div>

  <script>
    // Create floating hearts
    document.addEventListener('DOMContentLoaded', function () {
      const heartsContainer = document.getElementById('hearts-container');
      const heartCount = 15;

      for (let i = 0; i < heartCount; i++) {
        const heart = document.createElement('div');
        heart.classList.add('heart');

        // Random position
        const left = Math.random() * 100;
        heart.style.left = `${left}%`;

        // Random size
        const size = Math.random() * 15 + 10;
        heart.style.width = `${size}px`;
        heart.style.height = `${size}px`;

        // Random delay
        const delay = Math.random() * 15;
        heart.style.animationDelay = `${delay}s`;

        // Random duration
        const duration = Math.random() * 10 + 10;
        heart.style.animationDuration = `${duration}s`;

        heartsContainer.appendChild(heart);
      }
    });
  </script>
</body>

</html>












