<?php
// Database configuration
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'visual_novel_db'
];

// Create database connection
function getDatabaseConnection() {
    global $db_config;
    
    $conn = new mysqli(
        $db_config['host'],
        $db_config['username'],
        $db_config['password'],
        $db_config['database']
    );
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    return $conn;
}

// Initialize database tables if they don't exist
function initializeDatabaseTables() {
    $conn = getDatabaseConnection();
    
    // Create users table if not exists
    $sql_users = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        is_premium BOOLEAN DEFAULT FALSE,
        premium_expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    // Create user_progress table if not exists
    $sql_progress = "CREATE TABLE IF NOT EXISTS user_progress (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        chapter_id VARCHAR(10) NOT NULL,
        completed BOOLEAN DEFAULT FALSE,
        completion_date TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_chapter (user_id, chapter_id)
    )";
    
    // Create user_resources table if not exists
    $sql_resources = "CREATE TABLE IF NOT EXISTS user_resources (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        coins INT DEFAULT 1000,
        diamonds INT DEFAULT 1000,
        energy INT DEFAULT 40,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_resources (user_id)
    )";

    // Create missions table if not exists
    $sql_missions = "CREATE TABLE IF NOT EXISTS missions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        mission_type ENUM('daily', 'weekly', 'story', 'achievement') DEFAULT 'daily',
        target_value INT DEFAULT 1,
        reward_coins INT DEFAULT 0,
        reward_diamonds INT DEFAULT 0,
        reward_energy INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    // Create user_missions table if not exists
    $sql_user_missions = "CREATE TABLE IF NOT EXISTS user_missions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        mission_id INT NOT NULL,
        current_progress INT DEFAULT 0,
        is_completed BOOLEAN DEFAULT FALSE,
        is_claimed BOOLEAN DEFAULT FALSE,
        completed_at TIMESTAMP NULL,
        claimed_at TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (mission_id) REFERENCES missions(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_mission (user_id, mission_id)
    )";

    // Execute table creation queries
    if (!$conn->query($sql_users)) {
        die("Error creating users table: " . $conn->error);
    }

    if (!$conn->query($sql_progress)) {
        die("Error creating user_progress table: " . $conn->error);
    }

    if (!$conn->query($sql_resources)) {
        die("Error creating user_resources table: " . $conn->error);
    }

    if (!$conn->query($sql_missions)) {
        die("Error creating missions table: " . $conn->error);
    }

    if (!$conn->query($sql_user_missions)) {
        die("Error creating user_missions table: " . $conn->error);
    }

    // Create store_items table if not exists
    $sql_store_items = "CREATE TABLE IF NOT EXISTS store_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category ENUM('diamonds', 'energy', 'characters', 'items', 'bundles') NOT NULL,
        item_type VARCHAR(100),
        price_usd DECIMAL(10,2) DEFAULT 0,
        price_diamonds INT DEFAULT 0,
        original_price_usd DECIMAL(10,2) NULL,
        original_price_diamonds INT DEFAULT 0,
        discount_percentage INT DEFAULT 0,
        reward_diamonds INT DEFAULT 0,
        reward_coins INT DEFAULT 0,
        reward_energy INT DEFAULT 0,
        bonus_percentage INT DEFAULT 0,
        purchase_limit INT DEFAULT 0,
        time_limited BOOLEAN DEFAULT FALSE,
        expires_at TIMESTAMP NULL,
        is_featured BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        image_url VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    // Create user_purchases table if not exists
    $sql_user_purchases = "CREATE TABLE IF NOT EXISTS user_purchases (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        store_item_id INT NOT NULL,
        transaction_id VARCHAR(255) UNIQUE,
        payment_method VARCHAR(50),
        amount_usd DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
        payment_data JSON,
        purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (store_item_id) REFERENCES store_items(id) ON DELETE CASCADE
    )";

    if (!$conn->query($sql_store_items)) {
        die("Error creating store_items table: " . $conn->error);
    }

    if (!$conn->query($sql_user_purchases)) {
        die("Error creating user_purchases table: " . $conn->error);
    }

    // Create starlight_rewards table if not exists
    $sql_starlight_rewards = "CREATE TABLE IF NOT EXISTS starlight_rewards (
        id INT AUTO_INCREMENT PRIMARY KEY,
        level INT NOT NULL,
        tier ENUM('free', 'premium') NOT NULL,
        reward_type ENUM('coins', 'diamonds', 'energy', 'item') NOT NULL,
        reward_amount INT DEFAULT 0,
        reward_item_name VARCHAR(255) NULL,
        reward_item_description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_level_tier (level, tier)
    )";

    // Create user_starlight_progress table if not exists
    $sql_user_starlight_progress = "CREATE TABLE IF NOT EXISTS user_starlight_progress (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        current_level INT DEFAULT 1,
        current_exp INT DEFAULT 0,
        exp_to_next_level INT DEFAULT 1000,
        claimed_free_rewards JSON DEFAULT '[]',
        claimed_premium_rewards JSON DEFAULT '[]',
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_starlight (user_id)
    )";

    if (!$conn->query($sql_starlight_rewards)) {
        die("Error creating starlight_rewards table: " . $conn->error);
    }

    if (!$conn->query($sql_user_starlight_progress)) {
        die("Error creating user_starlight_progress table: " . $conn->error);
    }

    // Create mail_messages table if not exists
    $sql_mail_messages = "CREATE TABLE IF NOT EXISTS mail_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        sender_name VARCHAR(255) DEFAULT 'System',
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        mail_type ENUM('system', 'reward', 'notification', 'event') DEFAULT 'system',
        is_read BOOLEAN DEFAULT FALSE,
        is_archived BOOLEAN DEFAULT FALSE,
        has_attachment BOOLEAN DEFAULT FALSE,
        attachment_data JSON NULL,
        expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_mail (user_id, is_archived, created_at)
    )";

    if (!$conn->query($sql_mail_messages)) {
        die("Error creating mail_messages table: " . $conn->error);
    }

    $conn->close();
}

// Call initialization
initializeDatabaseTables();
?>
