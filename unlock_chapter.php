<?php
// Start session
session_start();

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get chapter ID from POST data
$chapter_id = $_POST['chapter_id'] ?? '';
if (empty($chapter_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Chapter ID is required']);
    exit;
}

// Get user ID from session
$user_id = $_SESSION['user_id'] ?? null;
if (!$user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Database connection
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'visual_novel_db';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Get the next chapter ID based on current chapter
$next_chapter_id = '';
switch ($chapter_id) {
    case '01-01':
        $next_chapter_id = '01-02';
        break;
    case '01-02':
        $next_chapter_id = '01-03';
        break;
    case '01-03':
        $next_chapter_id = '01-04';
        break;
    case '01-04':
        $next_chapter_id = '01-05';
        break;
    case '01-05':
        $next_chapter_id = '01-06';
        break;
    case '01-06':
        $next_chapter_id = '02-01';
        break;
    case '02-01':
        $next_chapter_id = '02-02';
        break;
    default:
        // No next chapter
        break;
}

// Mark current chapter as completed
$stmt = $conn->prepare("INSERT INTO user_progress (user_id, chapter_id, completed, completion_date) 
                        VALUES (?, ?, TRUE, NOW()) 
                        ON DUPLICATE KEY UPDATE completed = TRUE, completion_date = NOW()");
$stmt->bind_param("is", $user_id, $chapter_id);
$stmt->execute();

// If there's a next chapter, unlock it
if (!empty($next_chapter_id)) {
    $stmt = $conn->prepare("INSERT INTO user_progress (user_id, chapter_id, completed) 
                            VALUES (?, ?, TRUE) 
                            ON DUPLICATE KEY UPDATE completed = TRUE");
    $stmt->bind_param("is", $user_id, $next_chapter_id);
    $stmt->execute();
}

echo json_encode(['success' => true, 'message' => 'Chapter completed successfully', 'next_chapter' => $next_chapter_id]);

$stmt->close();
$conn->close();
?>

