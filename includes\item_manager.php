<?php
/**
 * Item Manager Class
 * Handles item data operations from JSON database and user inventory
 */

class ItemManager {
    private $itemsFile;
    private $itemsData;

    public function __construct() {
        $this->itemsFile = __DIR__ . '/../data/items.json';
        $this->loadItems();
    }

    /**
     * Load items from JSON file
     */
    private function loadItems() {
        if (file_exists($this->itemsFile)) {
            $jsonData = file_get_contents($this->itemsFile);
            $this->itemsData = json_decode($jsonData, true);
        } else {
            $this->itemsData = ['items' => [], 'categories' => [], 'rarities' => []];
        }
    }

    /**
     * Get all items
     * @return array Array of all items
     */
    public function getAllItems() {
        return $this->itemsData['items'] ?? [];
    }

    /**
     * Get item by ID
     * @param int $id Item ID
     * @return array|null Item data or null if not found
     */
    public function getItemById($id) {
        $items = $this->getAllItems();
        foreach ($items as $item) {
            if ($item['id'] == $id) {
                return $item;
            }
        }
        return null;
    }

    /**
     * Get items by type
     * @param string $type Item type (material, consumable, equipment, key)
     * @return array Array of items with specified type
     */
    public function getItemsByType($type) {
        $items = $this->getAllItems();
        return array_filter($items, function($item) use ($type) {
            return $item['type'] === $type;
        });
    }

    /**
     * Get items by category
     * @param string $category Item category
     * @return array Array of items with specified category
     */
    public function getItemsByCategory($category) {
        $items = $this->getAllItems();
        return array_filter($items, function($item) use ($category) {
            return $item['category'] === $category;
        });
    }

    /**
     * Get items by rarity
     * @param string $rarity Item rarity (common, uncommon, rare, epic, legendary)
     * @return array Array of items with specified rarity
     */
    public function getItemsByRarity($rarity) {
        $items = $this->getAllItems();
        return array_filter($items, function($item) use ($rarity) {
            return $item['rarity'] === $rarity;
        });
    }

    /**
     * Get all categories
     * @return array Array of item categories
     */
    public function getCategories() {
        return $this->itemsData['categories'] ?? [];
    }

    /**
     * Get all rarities
     * @return array Array of item rarities
     */
    public function getRarities() {
        return $this->itemsData['rarities'] ?? [];
    }

    /**
     * Get rarity color for UI display
     * @param string $rarity Rarity level
     * @return string CSS color value
     */
    public function getRarityColor($rarity) {
        $rarities = $this->getRarities();
        return $rarities[$rarity]['color'] ?? '#9e9e9e';
    }

    /**
     * Get rarity border color for UI display
     * @param string $rarity Rarity level
     * @return string CSS color value
     */
    public function getRarityBorderColor($rarity) {
        $rarities = $this->getRarities();
        return $rarities[$rarity]['border_color'] ?? '#757575';
    }

    /**
     * Get user inventory (placeholder - would connect to database in real implementation)
     * @param int $userId User ID
     * @return array User inventory with item quantities
     */
    public function getUserInventory($userId) {
        // For now, return sample inventory with the Experience Log
        // In a real implementation, this would query a database
        return [
            1 => 5,  // Experience Log x5
            2 => 2,  // Skill Enhancement Crystal x2
            3 => 10, // Health Potion x10
            4 => 3,  // Mana Elixir x3
            5 => 1,  // Fire Essence x1
            6 => 1,  // Ice Shard x1
            7 => 0   // Lightning Core x0 (not owned)
        ];
    }

    /**
     * Get user inventory with item details
     * @param int $userId User ID
     * @return array Array of inventory items with full details
     */
    public function getUserInventoryWithDetails($userId) {
        $inventory = $this->getUserInventory($userId);
        $inventoryWithDetails = [];

        foreach ($inventory as $itemId => $quantity) {
            if ($quantity > 0) {
                $item = $this->getItemById($itemId);
                if ($item) {
                    $item['quantity'] = $quantity;
                    $inventoryWithDetails[] = $item;
                }
            }
        }

        return $inventoryWithDetails;
    }

    /**
     * Use item (placeholder for future implementation)
     * @param int $userId User ID
     * @param int $itemId Item ID
     * @param int $quantity Quantity to use
     * @param array $target Target for item usage (character, etc.)
     * @return array Result of item usage
     */
    public function useItem($userId, $itemId, $quantity = 1, $target = null) {
        $item = $this->getItemById($itemId);
        if (!$item) {
            return ['success' => false, 'message' => 'Item not found'];
        }

        $inventory = $this->getUserInventory($userId);
        if (!isset($inventory[$itemId]) || $inventory[$itemId] < $quantity) {
            return ['success' => false, 'message' => 'Insufficient quantity'];
        }

        // Placeholder for item usage logic
        switch ($item['usage']['type']) {
            case 'character_upgrade':
                return [
                    'success' => true,
                    'message' => "Used {$item['name']} to grant {$item['usage']['value']} experience points",
                    'effect' => $item['usage']
                ];
            
            case 'heal':
                return [
                    'success' => true,
                    'message' => "Used {$item['name']} to restore {$item['usage']['value']} health",
                    'effect' => $item['usage']
                ];
            
            default:
                return [
                    'success' => true,
                    'message' => "Used {$item['name']}",
                    'effect' => $item['usage']
                ];
        }
    }

    /**
     * Search items by name or description
     * @param string $searchTerm Search term
     * @return array Array of matching items
     */
    public function searchItems($searchTerm) {
        $items = $this->getAllItems();
        return array_filter($items, function($item) use ($searchTerm) {
            return stripos($item['name'], $searchTerm) !== false ||
                   stripos($item['description'], $searchTerm) !== false ||
                   in_array(strtolower($searchTerm), array_map('strtolower', $item['tags'] ?? []));
        });
    }

    /**
     * Get items that can be used on a specific target
     * @param string $targetType Target type (character, fire_character, etc.)
     * @return array Array of usable items
     */
    public function getUsableItems($targetType) {
        $items = $this->getAllItems();
        return array_filter($items, function($item) use ($targetType) {
            return isset($item['usage']['target']) && 
                   ($item['usage']['target'] === $targetType || $item['usage']['target'] === 'character');
        });
    }

    /**
     * Get item sell value
     * @param int $itemId Item ID
     * @param int $quantity Quantity to sell
     * @return int Total sell value
     */
    public function getItemSellValue($itemId, $quantity = 1) {
        $item = $this->getItemById($itemId);
        if (!$item || !$item['sellable']) {
            return 0;
        }
        return $item['sell_price'] * $quantity;
    }

    /**
     * Format item description for display
     * @param array $item Item data
     * @param bool $showLongDescription Whether to show long description
     * @return string Formatted description
     */
    public function formatItemDescription($item, $showLongDescription = false) {
        $description = $showLongDescription ? 
            ($item['long_description'] ?? $item['description']) : 
            $item['description'];
        
        // Add usage information
        if (isset($item['usage'])) {
            $description .= "\n\nUsage: " . $item['usage']['effect'];
            if (isset($item['usage']['value'])) {
                $description .= " (" . $item['usage']['value'] . ")";
            }
        }

        return $description;
    }

    /**
     * Get item rarity display name
     * @param string $rarity Rarity key
     * @return string Rarity display name
     */
    public function getRarityName($rarity) {
        $rarities = $this->getRarities();
        return $rarities[$rarity]['name'] ?? ucfirst($rarity);
    }

    /**
     * Get category display information
     * @param string $category Category key
     * @return array Category information
     */
    public function getCategoryInfo($category) {
        $categories = $this->getCategories();
        return $categories[$category] ?? [
            'name' => ucfirst($category),
            'description' => '',
            'icon' => '📦'
        ];
    }
}
?>
