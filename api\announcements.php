<?php
session_start();
require_once '../config/database.php';
require_once '../includes/announcement_manager.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_announcements':
            $category = $_GET['category'] ?? null;
            $announcements = AnnouncementManager::getAnnouncementsWithReadStatus($user_id, $category);
            echo json_encode(['success' => true, 'announcements' => $announcements]);
            break;
            
        case 'get_unread_count':
            $count = AnnouncementManager::getUnreadCount($user_id);
            echo json_encode(['success' => true, 'unread_count' => $count]);
            break;
            
        case 'mark_as_read':
            $input = json_decode(file_get_contents('php://input'), true);
            $announcement_id = $input['announcement_id'] ?? 0;
            
            if ($announcement_id) {
                $success = AnnouncementManager::markAsRead($user_id, $announcement_id);
                echo json_encode(['success' => $success]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid announcement ID']);
            }
            break;
            
        case 'mark_all_as_read':
            $category = $_GET['category'] ?? null;
            $announcements = AnnouncementManager::getActiveAnnouncements($category);
            $success = true;
            
            foreach ($announcements as $announcement) {
                if (!AnnouncementManager::hasUserRead($user_id, $announcement['id'])) {
                    $result = AnnouncementManager::markAsRead($user_id, $announcement['id']);
                    if (!$result) {
                        $success = false;
                    }
                }
            }
            
            echo json_encode(['success' => $success]);
            break;
            
        case 'get_categories':
            $categories = [
                'System Announcement',
                'Event Announcement', 
                'Dis Info'
            ];
            echo json_encode(['success' => true, 'categories' => $categories]);
            break;
            
        case 'get_announcement_details':
            $announcement_id = $_GET['id'] ?? 0;
            if ($announcement_id) {
                $announcement = AnnouncementManager::getAnnouncementById($announcement_id);
                if ($announcement) {
                    // Mark as read when viewing details
                    AnnouncementManager::markAsRead($user_id, $announcement_id);
                    echo json_encode(['success' => true, 'announcement' => $announcement]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Announcement not found']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'Invalid announcement ID']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Announcements API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
?>
