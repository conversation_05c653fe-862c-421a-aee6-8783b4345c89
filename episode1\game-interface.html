<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> <PERSON> - Visual Novel</title>
  <link rel="stylesheet" href="mediastyle.css">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;700&family=Poppins:wght@300;400;500;600&display=swap');
    
    :root {
      --primary-color: #ff6b6b;
      --primary-dark: #c83349;
      --secondary-color: #4ecdc4;
      --text-light: #f7f7f7;
      --text-dark: #2d3436;
      --bg-dark: #1e272e;
      --bg-light: #f5f5f5;
      --accent: #ffd166;
      --gradient-overlay: linear-gradient(135deg, rgba(200,51,73,0.3), rgba(78,205,196,0.3));
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Montserrat', sans-serif;
      background-color: var(--bg-dark);
      color: var(--text-light);
      overflow: hidden;
    }
    
    #game-container {
      position: relative;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
    }
    
    /* Background with gradient overlay */
    #background {
      position: absolute;
      width: 110%;
      height: 110%;
      top: -5%;
      left: -5%;
      background-image: url('../assets/place/ruangtamu.png');
      background-size: cover;
      background-position: center;
      filter: brightness(0.9);
      transition: transform 0.5s ease-out;
    }
    
    #background::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(234, 230, 230, 0.2), rgba(0,0,0,0.6));
    }
    
    #character {
      position: absolute;
      bottom: -20%;        /* Further lowered position (was 0%) */
      left: 50%;
      transform: translateX(-50%);
      height: 130%;  
      width: 50%;      /* Keeping the increased size */
      transition: opacity 0.8s ease, transform 0.5s ease;
    }

    #character-left {
      position: absolute;
      bottom: -20%;        /* Further lowered position (was 0%) */
      left: 20%;
      transform: translateX(-50%);
      height: 130%;  
      width: 50%;      /* Keeping the increased size */
      transition: opacity 0.8s ease, transform 0.5s ease;
    }

    #character-right {
      position: absolute;
      bottom: -20%;        /* Further lowered position (was 0%) */
      left: 83%;
      transform: translateX(-50%);
      height: 130%;  
      width: 50%;      /* Keeping the increased size */
      transition: opacity 0.8s ease, transform 0.5s ease;
    }
    
    /* Title screen */
    #title-screen {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: var(--gradient-overlay);
      z-index: 10;
      backdrop-filter: blur(3px);
    }
    
    #title-content {
      padding: 3rem 4rem;
      border-radius: 20px;
      text-align: center;
      box-shadow: 3px 1px 5px 3px rgba(235, 168, 227, 0.5);
      border: 2px solid transparent;
      background-clip: padding-box;
      animation: borderColorShift 3s infinite alternate, fadeIn 1s ease-out;
      max-width: 600px;
      width: 90%;
    }
    
    @keyframes borderColorShift {
      0% { 
        border-color: rgba(235, 168, 227, 0.7);
        box-shadow: 3px 1px 5px 3px rgba(235, 168, 227, 0.5); 
      }
      50% { 
        border-color: rgba(168, 235, 227, 0.7);
        box-shadow: 3px 1px 5px 3px rgba(168, 235, 227, 0.5); 
      }
      100% { 
        border-color: rgba(227, 168, 235, 0.7);
        box-shadow: 3px 1px 5px 3px rgba(227, 168, 235, 0.5); 
      }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    #title-content h1 {
      font-family: 'Playfair Display', serif;
      font-size: 3.5rem;
      margin-bottom: 0.5rem;
      background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      text-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    #title-content h2 {
      font-family: 'Poppins', sans-serif;
      font-size: 1.5rem;
      font-weight: 400;
      margin-bottom: 2.5rem;
      color: var(--accent);
      letter-spacing: 1px;
    }
    
    .language-selector {
      margin-bottom: 2rem;
    }
    
    #language-select {
      background: rgba(255,255,255,0.1);
      color: var(--text-light);
      padding: 0.8rem 1.5rem;
      border: 1px solid rgba(255,255,255,0.2);
      border-radius: 30px;
      font-family: 'Montserrat', sans-serif;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 180px;
      text-align: center;
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 1rem center;
      background-size: 1em;
    }
    
    #language-select:hover {
      background-color: rgba(255,255,255,0.2);
      border-color: rgba(255,255,255,0.3);
    }
    
    .progress-indicator {
      margin-bottom: 2.5rem;
    }
    
    .progress-bar {
      height: 8px;
      background: rgba(255,255,255,0.1);
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 0.5rem;
    }
    
    .progress-fill {
      height: 100%;
      width: 0%;
      background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
      border-radius: 4px;
      transition: width 0.5s ease;
    }
    
    .progress-text {
      font-size: 0.9rem;
      color: rgba(255,255,255,0.7);
      font-family: 'Montserrat', sans-serif;
    }
    
    .button-container {
      margin-bottom: 2rem;
    }

    /* Futuristic Romantic Button Style */
#start-button, #back-button, #continue-mission {
  position: relative;
  padding: 1rem 3rem;
  font-size: 1.4rem;
  background: rgba(70, 0, 70, 0.7);
  color: #FF9CC0;
  border: none;
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
  overflow: hidden;
  transition: all 0.3s ease;
  font-family: 'Montserrat', sans-serif;
  
  /* Clip path for futuristic shape */
  clip-path: polygon(
    0% 20%, 
    10px 0%, 
    calc(100% - 10px) 0%, 
    100% 20%, 
    100% 80%, 
    calc(100% - 10px) 100%, 
    10px 100%, 
    0% 80%
  );
  
  /* Border effect */
  box-shadow: 
    0 0 0 2px rgba(255, 105, 180, 0.3),
    0 0 0 4px rgba(255, 105, 180, 0.1),
    0 5px 20px rgba(255, 105, 180, 0.5);
  
  /* Text glow */
  text-shadow: 0 0 10px rgba(255, 182, 193, 0.8);
}

/* Inner border effect */
#start-button::before, #back-button::before, #continue-mission::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: transparent;
  clip-path: polygon(
    0% 20%, 
    8px 2px, 
    calc(100% - 8px) 2px, 
    100% 20%, 
    100% 80%, 
    calc(100% - 8px) calc(100% - 2px), 
    8px calc(100% - 2px), 
    0% 80%
  );
  border: 1px solid rgba(255, 105, 180, 0.5);
  z-index: 1;
  pointer-events: none;
}

/* Glow effect */
#start-button::after, #back-button::after, #continue-mission::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(255, 105, 180, 0) 0%, 
    rgba(255, 105, 180, 0.3) 50%, 
    rgba(255, 105, 180, 0) 100%);
  z-index: 2;
  transform: translateX(-100%);
  transition: transform 0.6s;
}

/* Hover state */
#start-button:hover, #back-button:hover, #continue-mission:hover {
  background: rgba(90, 0, 90, 0.8);
  color: #FFD6E7;
  box-shadow: 
    0 0 0 2px rgba(255, 105, 180, 0.5),
    0 0 0 4px rgba(255, 105, 180, 0.2),
    0 10px 30px rgba(255, 105, 180, 0.7);
}

/* Active state */
#start-button:active, #back-button:active, #continue-mission:active {
  transform: translateY(2px);
  box-shadow: 
    0 0 0 2px rgba(255, 105, 180, 0.5),
    0 0 0 4px rgba(255, 105, 180, 0.2),
    0 3px 10px rgba(255, 105, 180, 0.4);
}

#continue-mission {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
}

#continue-mission:active {
  transform: translateX(-50%) translateY(2px);
}

/* Grey-themed Back Button */
#back-button {
  background: rgba(50, 50, 70, 0.7);
  color: #D0D0D0;
  box-shadow: 
    0 0 0 2px rgba(180, 180, 200, 0.3),
    0 0 0 4px rgba(180, 180, 200, 0.1),
    0 5px 20px rgba(180, 180, 200, 0.5);
  text-shadow: 0 0 10px rgba(200, 200, 220, 0.8);
}

#back-button::before {
  border: 1px solid rgba(180, 180, 200, 0.5);
}

#back-button::after {
  background: linear-gradient(45deg, 
    rgba(180, 180, 200, 0) 0%, 
    rgba(180, 180, 200, 0.3) 50%, 
    rgba(180, 180, 200, 0) 100%);
}

#back-button:hover {
  background: rgba(60, 60, 80, 0.8);
  color: #F0F0F0;
  box-shadow: 
    0 0 0 2px rgba(180, 180, 200, 0.5),
    0 0 0 4px rgba(180, 180, 200, 0.2),
    0 10px 30px rgba(180, 180, 200, 0.7);
}

#back-button:active {
  box-shadow: 
    0 0 0 2px rgba(180, 180, 200, 0.5),
    0 0 0 4px rgba(180, 180, 200, 0.2),
    0 3px 10px rgba(180, 180, 200, 0.4);
}
    
    .episode-info {
      display: flex;
      justify-content: center;
      gap: 2rem;
      color: rgba(255,255,255,0.7);
      font-size: 0.9rem;
    }
    
    .episode-duration, .episode-difficulty {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .fa-star, .fa-star-o, .fa-clock-o {
      color: var(--accent);
    }
    
    #dialog-box {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      width: 95%;
      max-width: 1200px;
      background: linear-gradient(to bottom, rgba(179, 165, 194, 0.697), rgba(225, 152, 200, 0.851));
      color: #2f2fe3;
      font-weight: 500;
      display: flex;
      border-radius: 5px;
      flex-direction: column;
      cursor: pointer;
      transition: all 0.3s ease;
      border-image: linear-gradient(to right, pink, rgba(221, 50, 169, 0.7)) 1;
      border-style: solid;
      border-width: 2px;
      overflow: hidden;
      padding: 5px 0;
      min-height: 13rem; /* Increased height for 2 lines of text */
      backdrop-filter: blur(3px);
      -webkit-backdrop-filter: blur(3px);
    }

    /* Style1 Dialog Box - Center positioned, low height, full width */
    #style1-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 120px;
      background: rgba(0, 0, 0, 0.85);
      color: #ffffff;
      display: none;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      z-index: 100;
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      padding: 0 20px;
      box-sizing: border-box;
    }

    #style1-box.active {
      display: flex;
    }

    #style1-text {
      text-align: center;
      font-size: 1.8rem;
      font-style: italic;
      color: #ffffff;
      max-width: 90%;
      line-height: 1.6;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    #style1-next-indicator {
      position: absolute;
      bottom: 15px;
      right: 30px;
      color: #ffffff;
      animation: bounce 1s infinite;
      font-size: 1.8rem;
      display: none;
    }

    /* Responsive Design for Style1 - Mobile First */

    /* Extra Small Devices (phones, 320px and up) */
    @media (max-width: 480px) {
      #style1-box {
        height: 100px;
        padding: 0 15px;
        width: 95%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      #style1-text {
        font-size: 1.2rem;
        line-height: 1.4;
        max-width: 95%;
      }

      #style1-next-indicator {
        font-size: 1.4rem;
        bottom: 10px;
        right: 15px;
      }
    }

    /* Small Devices (landscape phones, 481px and up) */
    @media (min-width: 481px) and (max-width: 768px) {
      #style1-box {
        height: 110px;
        padding: 0 20px;
      }

      #style1-text {
        font-size: 1.5rem;
        line-height: 1.5;
        max-width: 92%;
      }

      #style1-next-indicator {
        font-size: 1.6rem;
        bottom: 12px;
        right: 20px;
      }
    }

    /* Medium Devices (tablets, 769px and up) */
    @media (min-width: 769px) and (max-width: 1024px) {
      #style1-box {
        height: 115px;
        padding: 0 25px;
      }

      #style1-text {
        font-size: 1.6rem;
        line-height: 1.5;
        max-width: 88%;
      }

      #style1-next-indicator {
        font-size: 1.7rem;
        bottom: 13px;
        right: 25px;
      }
    }

    /* Large Devices (desktops, 1025px and up) */
    @media (min-width: 1025px) {
      #style1-box {
        height: 120px;
        padding: 0 30px;
      }

      #style1-text {
        font-size: 1.8rem;
        line-height: 1.6;
        max-width: 85%;
      }

      #style1-next-indicator {
        font-size: 1.8rem;
        bottom: 15px;
        right: 30px;
      }
    }

    /* Portrait Orientation Adjustments */
    @media (orientation: portrait) and (max-width: 768px) {
      #style1-box {
        height: 90px;
      }

      #style1-text {
        font-size: 1.1rem;
        line-height: 1.3;
      }
    }

    /* Landscape Orientation Adjustments */
    @media (orientation: landscape) and (max-height: 500px) {
      #style1-box {
        height: 80px;
        top: 45%;
      }

      #style1-text {
        font-size: 1.0rem;
        line-height: 1.2;
      }

      #style1-next-indicator {
        font-size: 1.2rem;
        bottom: 8px;
        right: 15px;
      }
    }
    
    #dialog-box::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    #character-name-container {
      position: relative;
      margin-left: 1rem;
      margin-top: 0.2rem;
    }
    
    #character-name {
      padding: 0.2rem 1rem;
      font-weight: 600;
      color: rgb(65, 39, 73);
      font-size: 1.3rem;
      background: linear-gradient(to right, rgba(124, 12, 77, 0.2), rgb(252, 40, 131,0.1));
      display: inline-block;
      border-radius: 3px;
      box-shadow: 0 7px 17px rgba(85, 42, 77, 0.7);
    }
    
    #dialog-text {
      padding: 1rem 2.5rem 2rem 2.5rem;
      line-height: 1.8;
      font-size: 1.5rem;
      color: var(--text-dark);
      min-height: 80px;
    }
    
    #next-indicator {
      position: absolute;
      bottom: 15px;
      right: 30px;
      color: var(--primary-dark);
      animation: bounce 1s infinite;
      font-size: 1.8rem;
    }
    
    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }
    
    /* Choices */
    #choices {
      position: absolute;
      font-size: 1rem;
      bottom: 40%;
      left: 75%;
      transform: translateX(-50%);
      width: 45%;
      max-width: 800px;
      display: none;
      flex-direction: column;
      gap: 1rem;
      padding: 1rem;
      border-radius: 20px;
      z-index: 9999;
      box-shadow: 0 0 30px rgba(52, 189, 180, 0.5);
      backdrop-filter: blur(1px);
      -webkit-backdrop-filter: blur(1px);
    }
    
    .choice-btn {
      padding: 1rem 1.3rem;
      background: linear-gradient(to right, rgba(61, 12, 124, 0.7), rgba(252, 40, 195, 0.5));
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      text-align: left;
      font-size: 1rem;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      font-family: 'Montserrat', sans-serif;
      font-weight: 350;
      position: relative;
      overflow: hidden;
    }
    
    .choice-btn::after {
      content: '→';
      position: absolute;
      right: 20px;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .choice-btn:hover {
      transform: translateX(10px);
      padding-right: 2.5rem;
    }
    
    .choice-btn:hover::after {
      opacity: 1;
    }
    
    /* Controls menu - split into left and right sides */
    #controls-menu {
      position: absolute;
      top: 25px;
      right: 25px;
      display: flex;
      gap: 12px;
      z-index: 50;
    }

    #controls-left {
      position: absolute;
      top: 25px;
      left: 25px;
      display: flex;
      gap: 12px;
      z-index: 50;
    }

    .menu-btn {
      padding: 0.8rem 1.2rem;
      background: transparent;
      color: white;
      border: 1px solid rgba(255,255,255,0.2);
      border-radius: 10px;
      cursor: pointer;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      box-shadow: 0 3px 8px rgba(0,0,0,0.3);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      font-family: 'Montserrat', sans-serif;
      font-weight: 500;
    }

    .menu-btn:hover {
      border-color: rgba(255,255,255,0.4);
      box-shadow: 0 5px 12px rgba(136, 185, 197, 0.4);
    }

    .menu-btn.log-btn:hover {
      box-shadow:0 5px 12px rgba(78,205,196,0.8);
    }


    .menu-btn.exit-btn:hover {
      box-shadow: 0 5px 12px rgba(101, 191, 205, 0.4);
    }

    /* Tooltip for buttons */
    .menu-btn {
      position: relative;
    }

    .menu-btn::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: -30px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      font-size: 0.8rem;
      opacity: 0;
      pointer-events: none;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .menu-btn:hover::after {
      opacity: 1;
      bottom: -35px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
      #title-content h1 {
        font-size: 2.5rem;
      }
      
      #title-content h2 {
        font-size: 1.3rem;
      }
      
      #dialog-text {
        font-size: 1.2rem;
        padding: 0.8rem 2rem 1.8rem 2rem;
      }
      
      #character-name {
        font-size: 1.3rem;
      }
      
      .choice-btn {
        font-size: 1.2rem;
        padding: 1rem 1.5rem;
      }
      
      .menu-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
      }
      
      #back-button {
        font-size: 1.2rem;
        padding: 0.8rem 2.5rem;
      }
    }
    
    /* Dialog Log Styles */
    #dialog-log {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.95);
      width: 80%;
      max-width: 700px;
      height: 80%;
      max-height: 600px;
      background: rgba(30, 39, 46, 0.95);
      border-radius: 15px;
      z-index: 1000;
      display: none;
      opacity: 0;
      transition: all 0.3s ease;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      overflow: hidden;
    }
    
    #dialog-log.active {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    
    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      background: rgba(0, 0, 0, 0.2);
    }
    
    .log-header h2 {
      color: var(--primary-color);
      margin: 0;
      font-family: 'Playfair Display', serif;
      font-size: 1.8rem;
    }
    
    #close-log {
      background: none;
      border: none;
      color: white;
      font-size: 2rem;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }
    
    #close-log:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--primary-color);
    }
    
    .log-content {
      padding: 20px;
      height: calc(100% - 70px);
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: var(--primary-color) rgba(0, 0, 0, 0.2);
    }
    
    .log-content::-webkit-scrollbar {
      width: 8px;
    }
    
    .log-content::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
    
    .log-content::-webkit-scrollbar-thumb {
      background: var(--primary-color);
      border-radius: 4px;
    }
    
    .log-entry {
      margin-bottom: 20px;
      animation: fadeIn 0.3s ease-out;
    }
    
    .log-speaker {
      color: var(--accent);
      font-weight: 600;
      margin-bottom: 5px;
      font-size: 1.1rem;
    }
    
    .log-text {
      color: white;
      line-height: 1.5;
      background: rgba(0, 0, 0, 0.2);
      padding: 12px 15px;
      border-radius: 10px;
      border-top-left-radius: 0;
    }
    
    .log-text.narrator {
      border-radius: 10px;
      font-style: italic;
      color: rgba(255, 255, 255, 0.8);
    }
    
    .log-empty {
      color: rgba(255, 255, 255, 0.5);
      text-align: center;
      font-style: italic;
      margin-top: 40px;
    }
    
    /* Active state for menu buttons */
    .menu-btn.active {
      background: var(--primary-color);
      border-color: rgba(255, 255, 255, 0.4);
    }

    /* Auto button active state with animation symbol */
.menu-btn.auto-btn {
  position: relative;
  overflow: hidden;
}

.menu-btn.auto-btn.active {
  background: transparent;
}

.menu-btn.auto-btn.active::after {
  content: '⟳';  /* Unicode refresh/rotate symbol */
  position: absolute;
  right: 8px;
  font-size: 16px;
  display: inline-block;
}

/* Alternative dots animation for auto mode */
.menu-btn.auto-btn.active .auto-dots {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 10px;
  margin-left: 5px;
}

.menu-btn.auto-btn.active .auto-dots::before {
  content: '...';
  position: absolute;
  left: 0;
  top: -5px;
  animation: autoDots 1.5s infinite;
  letter-spacing: 1px;
}

@keyframes autoDots {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

    /* Exit Dialog Styles */
    #exit-dialog {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      opacity: 0;
      transition: opacity 0.3s ease;
      backdrop-filter: blur(5px);
    }

    #exit-dialog.active {
      opacity: 1;
    }

    .exit-dialog-content {
      background: rgba(30, 39, 46, 0.95);
      padding: 30px;
      border-radius: 15px;
      text-align: center;
      width: 90%;
      max-width: 400px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transform: translateY(20px);
      transition: transform 0.3s ease;
    }

    #exit-dialog.active .exit-dialog-content {
      transform: translateY(0);
    }

    .exit-dialog-content h2 {
      color: var(--primary-color);
      margin: 0 0 20px 0;
      font-family: 'Playfair Display', serif;
      font-size: 2rem;
    }

    .exit-dialog-content p {
      color: white;
      margin-bottom: 15px;
      font-size: 1.1rem;
    }

    .exit-dialog-content p.warning {
      color: #ff6b6b;
      font-weight: bold;
      margin-bottom: 25px;
    }

    .exit-buttons {
      display: flex;
      justify-content: center;
      gap: 15px;
    }

    .exit-buttons button {
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      font-size: 1.1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    #cancel-exit {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    #cancel-exit:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-3px);
    }

    #confirm-exit {
      background: var(--primary-dark);
      color: white;
    }

    #confirm-exit:hover {
      background: var(--primary-color);
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(200, 51, 73, 0.4);
    }

    /* Mission Complete Screen - Romantic Theme */
    #mission-complete {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(70,0,70,0.8), rgba(20,0,40,0.95));
      z-index: 1000;
      display: flex;
      flex-direction: column;
      opacity: 0;
      transition: opacity 0.5s ease;
      overflow: hidden;
    }

    #mission-complete.active {
      opacity: 1;
    }

    .mission-banner {
      position: absolute;
      top: 30px;
      left: 50px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .mission-badge {
      width: 40px;
      height: 40px;
      background-image: url('../assets/ui/mission_badge.png');
      background-size: contain;
      background-repeat: no-repeat;
      filter: drop-shadow(0 0 5px rgba(255, 182, 193, 0.7));
    }

    .mission-text {
      color: #FF9CC0;
      font-size: 1.2rem;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 1px;
      text-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
    }

    .success-container {
      margin-top: 80px;
      margin-left: 50px;
      width: 45%;
    }

    .success-title {
      font-family: 'Times New Roman', serif;
      font-size: 5rem;
      color: #FFD6E7;
      margin: 0;
      letter-spacing: 2px;
      font-weight: 400;
      text-shadow: 0 0 15px rgba(255, 105, 180, 0.6);
    }

    .episode-title {
      color: #FFF;
      font-size: 1.2rem;
      margin-top: 10px;
      margin-bottom: 20px;
      text-shadow: 0 0 8px rgba(255, 182, 193, 0.5);
    }

    .progress-bar {
      width: 100%;
      height: 5px;
      background: rgba(255,255,255,0.2);
      margin: 20px 0;
      position: relative;
      border-radius: 3px;
      overflow: hidden;
    }

    .progress-fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 0;
      background: linear-gradient(to right, #FF69B4, #FFB6C1);
      transition: width 1s ease;
      box-shadow: 0 0 10px rgba(255, 105, 180, 0.7);
    }

    .stats-container {
      display: flex;
      align-items: center;
      margin: 20px 0;
    }

    .level-display {
      width: 50px;
      height: 50px;
      background: rgba(70, 0, 70, 0.6);
      border: 2px solid #FF69B4;
      color: #FFF;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      font-weight: bold;
      margin-right: 20px;
      box-shadow: 0 0 15px rgba(255, 105, 180, 0.5);
      border-radius: 5px;
    }

    .exp-info {
      display: flex;
      flex-direction: column;
    }

    .exp-text {
      color: #FFF;
      font-size: 1.2rem;
    }

    .exp-gain {
      color: #FF9CC0;
      font-size: 1.1rem;
      font-weight: bold;
    }

    .rewards-section {
      margin-top: 30px;
    }

    .reward-header {
      display: flex;
      align-items: center;
      margin: 15px 0;
    }

    .red-dot {
      width: 8px;
      height: 8px;
      background: #FF69B4;
      border-radius: 50%;
      margin-right: 10px;
      box-shadow: 0 0 8px rgba(255, 105, 180, 0.8);
    }

    .character-icons {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .char-icon {
      width: 50px;
      height: 50px;
      background-color: rgba(70, 0, 70, 0.4);
      border-radius: 50%;
      border: 2px solid #FF9CC0;
      box-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
      transition: all 0.3s ease;
    }

    .char-icon:hover {
      transform: scale(1.1);
      border-color: #FF69B4;
      box-shadow: 0 0 15px rgba(255, 105, 180, 0.8);
    }

    .rewards-container {
      display: flex;
      gap: 20px;
      margin: 15px 0;
    }

    .reward-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      transition: all 0.3s ease;
    }

    .reward-item:hover {
      transform: translateY(-5px);
    }

    .reward-img {
      width: 60px;
      height: 60px;
      background-color: rgba(70, 0, 70, 0.4);
      border-radius: 5px;
      border: 2px solid #FF9CC0;
      box-shadow: 0 0 10px rgba(255, 105, 180, 0.5);
    }

    .reward-count {
      color: #FFF;
      font-size: 1.1rem;
      font-weight: bold;
      margin-top: 5px;
      text-shadow: 0 0 5px rgba(255, 105, 180, 0.5);
    }

    .reward-label {
      color: #FFB6C1;
      font-size: 0.8rem;
      background: rgba(70, 0, 70, 0.6);
      padding: 2px 8px;
      border-radius: 10px;
      margin-top: 5px;
      box-shadow: 0 0 8px rgba(255, 105, 180, 0.3);
    }

    .character-display {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 45%;
      height: 90%;
      background-image: url('../assets/qistina/qistina_full.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: bottom right;
      opacity: 0;
      transform: translateX(50px);
      transition: all 1s ease;
      filter: drop-shadow(0 0 20px rgba(255, 182, 193, 0.5));
    }

    .next-prompt {
      position: absolute;
      right: 100px;
      bottom: 100px;
      color: #FFD6E7;
      font-size: 1.2rem;
      text-shadow: 0 0 10px rgba(255, 105, 180, 0.6);
    }

  </style>
</head>
<body>
  <div id="game-container">
    <div id="background"></div>
    <img id="character"  alt="Character">
    <img id="character-left" alt="Character left">
    <img id="character-right" alt="Character right">
    
    <div id="title-screen">
      <div id="title-content">
        <h1>Cinta di Taman</h1>
        <h2>EPISOD 1.1 – Dia Muncul Dari Masa</h2>
        <div class="button-container" style="display: flex; gap: 20px; justify-content: center;">
          <button id="back-button" onclick="window.location.href='../menuchapters/story-chapters.php'">BALIK</button>
          <button id="start-button">MULA</button>
        </div>
      </div>
    </div>
    
    <div id="controls-menu" style="display: none;">
      <!-- Keep only the auto button, remove skip button -->
      <button class="menu-btn auto-btn">Auto</button>
    </div>
    
    <!-- Add a new controls-left for the log button and exit button -->
    <div id="controls-left" style="position: absolute; top: 25px; left: 25px; display: none; z-index: 50;">
      <button class="menu-btn log-btn">Log</button>
    </div>
    
    <div id="dialog-box">
      <div id="character-name-container">
        <div id="character-name">Salman</div>
      </div>
      <div id="dialog-text"></div>
      <div id="next-indicator">▼</div>
    </div>

    <!-- Style1 Box - Center positioned for internal thoughts -->
    <div id="style1-box">
      <div id="style1-text">Style1 text will appear here...</div>
      <div id="style1-next-indicator">▼</div>
    </div>
    
    <div id="choices" style="display: none;">
      <button class="choice-btn" data-choice="1">Choice 1</button>
      <button class="choice-btn" data-choice="2">Choice 2</button>
      <button class="choice-btn" data-choice="3">Choice 3</button>
    </div>
  </div>

  <script src="game-script.js"></script>
  <button id="debug-button" style="position: absolute; top: 10px; left: 10px; z-index: 9999; background: red; color: white; padding: 10px; display: none;">Debug</button>
  
  <script>
    document.getElementById('debug-button').addEventListener('click', function() {
      document.getElementById('choices').style.cssText = 'display: flex !important; opacity: 1 !important; z-index: 9999 !important;';
      currentDialogIndex = 9;
      showDialog(currentDialogIndex);
    });
    
    // Add subtle parallax effect
    document.addEventListener('mousemove', function(e) {
      const background = document.getElementById('background');
      const x = e.clientX / window.innerWidth;
      const y = e.clientY / window.innerHeight;
      
      background.style.transform = `translate(-${x * 10}px, -${y * 10}px)`;
    });
    
    // Add pulsing effect to start button
    setInterval(function() {
      const startButton = document.getElementById('start-button');
      startButton.style.boxShadow = '0 10px 25px rgba(0,0,0,0.5)';
      
      setTimeout(function() {
        startButton.style.transform = 'translateY(0)';
        startButton.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
      }, 700);
    }, 1500);
    
    // Update the startGame function to show both control panels and start the game
    document.getElementById('start-button').addEventListener('click', function() {
      document.getElementById('title-screen').style.display = 'none';
      document.getElementById('dialog-box').style.display = 'flex';
      document.getElementById('controls-menu').style.display = 'flex';
      document.getElementById('controls-left').style.display = 'flex';
      
      // Start the game by calling the startGame function from game-script.js
      if (typeof startGame === 'function') {
        startGame();
      } else {
        console.error('startGame function not found. Check if game-script.js is loaded properly.');
      }
    });
    
    // Toggle auto mode
    document.querySelector('.auto-btn').addEventListener('click', function() {
      this.classList.toggle('active');
      // Auto mode toggle logic will be handled in game-script.js
    });
    
    // Show log
    document.querySelector('.log-btn').addEventListener('click', function() {
      // Log display logic will be handled in game-script.js
    });
  </script>
</body>
</html>








