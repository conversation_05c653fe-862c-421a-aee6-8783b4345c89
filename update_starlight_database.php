<?php
/**
 * Starlight Database Update Script
 * Run this file once to set up the starlight system tables and update existing users
 */

require_once 'config/database.php';
require_once 'includes/starlight_manager.php';

echo "<h2>Setting up Starlight System...</h2>";

try {
    $conn = getDatabaseConnection();
    
    // Add premium columns to users table if they don't exist
    echo "<h3>Updating users table...</h3>";
    
    // Check if premium columns exist
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'is_premium'");
    if ($result->num_rows == 0) {
        $sql = "ALTER TABLE users ADD COLUMN is_premium BOOLEAN DEFAULT FALSE AFTER password";
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ Added is_premium column to users table</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to add is_premium column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>✓ is_premium column already exists</p>";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'premium_expires_at'");
    if ($result->num_rows == 0) {
        $sql = "ALTER TABLE users ADD COLUMN premium_expires_at TIMESTAMP NULL AFTER is_premium";
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ Added premium_expires_at column to users table</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to add premium_expires_at column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>✓ premium_expires_at column already exists</p>";
    }
    
    // Create starlight_rewards table
    echo "<h3>Creating starlight_rewards table...</h3>";
    $sql_starlight_rewards = "CREATE TABLE IF NOT EXISTS starlight_rewards (
        id INT AUTO_INCREMENT PRIMARY KEY,
        level INT NOT NULL,
        tier ENUM('free', 'premium') NOT NULL,
        reward_type ENUM('coins', 'diamonds', 'energy', 'item') NOT NULL,
        reward_amount INT DEFAULT 0,
        reward_item_name VARCHAR(255) NULL,
        reward_item_description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_level_tier (level, tier)
    )";
    
    if ($conn->query($sql_starlight_rewards)) {
        echo "<p style='color: green;'>✓ starlight_rewards table created successfully</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create starlight_rewards table: " . $conn->error . "</p>";
    }
    
    // Create user_starlight_progress table
    echo "<h3>Creating user_starlight_progress table...</h3>";
    $sql_user_starlight_progress = "CREATE TABLE IF NOT EXISTS user_starlight_progress (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        current_level INT DEFAULT 1,
        current_exp INT DEFAULT 0,
        exp_to_next_level INT DEFAULT 1000,
        claimed_free_rewards JSON DEFAULT '[]',
        claimed_premium_rewards JSON DEFAULT '[]',
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_starlight (user_id)
    )";
    
    if ($conn->query($sql_user_starlight_progress)) {
        echo "<p style='color: green;'>✓ user_starlight_progress table created successfully</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create user_starlight_progress table: " . $conn->error . "</p>";
    }
    
    // Initialize default starlight rewards
    echo "<h3>Initializing default starlight rewards...</h3>";
    if (StarlightManager::initializeDefaultRewards()) {
        echo "<p style='color: green;'>✓ Default starlight rewards initialized</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to initialize default starlight rewards</p>";
    }
    
    // Initialize starlight progress for existing users
    echo "<h3>Adding starlight progress to existing users:</h3>";
    
    // Get users who don't have starlight progress yet
    $sql = "SELECT u.id, u.username FROM users u 
            LEFT JOIN user_starlight_progress usp ON u.id = usp.user_id 
            WHERE usp.user_id IS NULL";
    
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $user_id = $row['id'];
            $username = $row['username'];
            
            if (StarlightManager::initializeUserStarlight($user_id)) {
                echo "<p style='color: blue;'>✓ Added starlight progress to user: {$username}</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to add starlight progress to user: {$username}</p>";
            }
        }
    } else {
        echo "<p>All existing users already have starlight progress configured.</p>";
    }
    
    // Give some users premium for testing (optional)
    echo "<h3>Setting up test premium users (optional):</h3>";
    echo "<p><em>You can manually set users as premium in the database or through the store system.</em></p>";
    
    $conn->close();
    
    echo "<h3>Starlight System Setup Complete!</h3>";
    echo "<p>Features added:</p>";
    echo "<ul>";
    echo "<li>Premium user system with expiration dates</li>";
    echo "<li>Starlight reward system with 10 levels</li>";
    echo "<li>Free and Premium reward tiers</li>";
    echo "<li>Experience and level progression</li>";
    echo "<li>Reward claiming system</li>";
    echo "</ul>";
    
    echo "<h4>Default Rewards Summary:</h4>";
    echo "<ul>";
    echo "<li>Level 1: Free (5,000 coins) | Premium (100 diamonds)</li>";
    echo "<li>Level 2: Free (20 energy) | Premium (15,000 coins)</li>";
    echo "<li>Level 3: Free (50 diamonds) | Premium (50 energy)</li>";
    echo "<li>Level 4: Free (10,000 coins) | Premium (200 diamonds)</li>";
    echo "<li>Level 5: Free (30 energy) | Premium (Premium Box)</li>";
    echo "<li>Level 6: Free (75 diamonds) | Premium (25,000 coins)</li>";
    echo "<li>Level 7: Free (15,000 coins) | Premium (75 energy)</li>";
    echo "<li>Level 8: Free (40 energy) | Premium (300 diamonds)</li>";
    echo "<li>Level 9: Free (100 diamonds) | Premium (40,000 coins)</li>";
    echo "<li>Level 10: Free (Starlight Box) | Premium (Premium Starlight Box)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Starlight System Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2, h3, h4 { color: #333; }
        p { margin: 10px 0; }
        ul { margin: 10px 0; }
        .nav-links { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc; }
        .nav-links a { 
            display: inline-block; 
            margin-right: 20px; 
            padding: 10px 20px; 
            background: #007cba; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
        }
        .nav-links a:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="nav-links">
        <a href="MenuButton/Starlight.php">→ Go to Starlight Page</a>
        <a href="menu/home.php">→ Back to Game</a>
        <a href="setup_database.php">→ Setup Main Database</a>
    </div>
</body>
</html>
