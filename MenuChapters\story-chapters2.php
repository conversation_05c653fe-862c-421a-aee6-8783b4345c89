<?php
// Start session to track user progress
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: register/login.php");
    exit;
}

// Database connection
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'visual_novel_db';

// Connect to database
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Get unlocked chapters for this user
$unlocked_chapters = [];
$sql = "SELECT chapter_id FROM user_progress WHERE user_id = ? AND completed = TRUE";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $unlocked_chapters[] = $row['chapter_id'];
}

// Define chapter 2 episodes
$chapters = [
    '2' => [
        ['id' => '02-01', 'title' => 'New Beginnings', 'thumbnail' => '../assets/episode7-thumbnail.png', 'url' => '../episode2/game-interface1.html'],
        ['id' => '02-02', 'title' => 'Meeting Friends', 'thumbnail' => '../assets/episode8-thumbnail.png', 'url' => '../episode2/game-interface2.html'],
        ['id' => '02-03', 'title' => 'The Confession', 'thumbnail' => '../assets/episode9-thumbnail.png', 'url' => '../episode2/game-interface3.html'],
        ['id' => '02-04', 'title' => 'First Date', 'thumbnail' => '../assets/episode10-thumbnail.png', 'url' => '../episode2/game-interface4.html']
    ]
];

// Function to check if chapter is unlocked
function isChapterUnlocked($chapter_id, $unlocked_chapters) {
    // First chapter of Chapter 2 is unlocked if user completed Chapter 1
    if ($chapter_id === '02-01' && in_array('01-06', $unlocked_chapters)) {
        return true;
    }
    
    // Check if chapter is in unlocked list
    return in_array($chapter_id, $unlocked_chapters);
}

// Current chapter is always 2 in this file
$currentChapter = 2;
$totalChapters = 2;
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Story Chapters - Chapter 2</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Montserrat', sans-serif;
      overflow: hidden;
      height: 100vh;
    }
    
    @keyframes roomAmbience {
      0% {
        background-position: center;
        filter: brightness(1);
      }
      25% {
        background-position: calc(50% + 5px) calc(50% - 5px);
        filter: brightness(1.02);
      }
      50% {
        background-position: calc(50% + 10px) calc(50% - 3px);
        filter: brightness(1.05);
      }
      75% {
        background-position: calc(50% + 5px) calc(50% + 5px);
        filter: brightness(1.02);
      }
      100% {
        background-position: center;
        filter: brightness(1);
      }
    }

    #story-container {
      position: relative;
      width: 100%;
      height: 100%;
      background-image: url('assets/city-background2.png');
      background-size: cover;
      background-position: center;
      animation: roomAmbience 7s ease-in-out infinite;
      transition: background-image 0.5s ease;
    }
    
    /* Top navigation */
    .top-nav {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      padding: 15px;
      background: rgba(0,0,0,0.2);
      backdrop-filter: blur(5px);
      color: white;
      z-index: 10;
    }
    
    .back-button {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #ff6b6b, #c83349);
      border-radius: 25px;
      padding: 8px 15px;
      cursor: pointer;
      box-shadow: 0 3px 10px rgba(0,0,0,0.3), inset 0 -2px 0 rgba(0,0,0,0.2);
      transition: all 0.2s ease;
      border: 2px solid rgba(255,255,255,0.1);
      overflow: hidden;
      position: relative;
    }
    
    .back-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: rgba(255,255,255,0.1);
      border-radius: 20px 20px 0 0;
    }
    
    .back-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.4), inset 0 -2px 0 rgba(0,0,0,0.2);
    }
    
    .back-button:active {
      transform: translateY(1px);
      box-shadow: 0 2px 5px rgba(0,0,0,0.2), inset 0 1px 0 rgba(0,0,0,0.2);
    }
    
    .back-icon {
      font-size: 18px;
      margin-right: 8px;
      text-shadow: 0 2px 3px rgba(0,0,0,0.3);
    }
    
    .back-text {
      font-weight: 600;
      letter-spacing: 1px;
      font-size: 14px;
      text-shadow: 0 2px 3px rgba(0,0,0,0.3);
    }
    
    /* Weather display */
    .weather-display {
      position: absolute;
      top: 80px;
      left: 20px;
      color: white;
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }
    
    .temperature {
      font-size: 60px;
      font-weight: 700;
      line-height: 1;
    }
    
    .month {
      font-size: 30px;
      font-weight: 500;
    }
    
    /* Chapter selection - redesigned to be simple horizontal cards */
    .chapter-selection {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 0;
      transition: opacity 0.5s ease;
    }

    .chapter-track {
      display: flex;
      padding: 30px 0;
      overflow-x: auto;
      width: 90%;
      justify-content: flex-start;
      padding-left: 20px;
      -ms-overflow-style: none;
      scrollbar-width: none;
      scroll-behavior: smooth;
    }

    .chapter-track::-webkit-scrollbar {
      display: none;
    }

    .chapter-card {
      width: 200px;
      height: 270px;
      margin: 0 15px;
      background: #fff;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .chapter-card:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    }

    .chapter-card.chapter-active:hover {
      transform: scale(1.1);
    }

    .chapter-card img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .chapter-number {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 12px;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }

    .chapter-locked {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
    }

    @keyframes lockPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    .lock-icon {
      width: 60px;
      height: 60px;
      background: url('assets/icons/lock.png');
      background-size: cover;
      animation: lockPulse 2s infinite;
    }

    .chapter-unlocked {
      position: absolute;
      bottom: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
      background: url('assets/icons/unlock.png');
      background-size: cover;
      z-index: 5;
    }

    .chapter-active {
      border: 3px solid #ff6b6b;
      transform: scale(1.1);
      z-index: 10;
    }
    
    /* Navigation arrows for chapter track */
    .chapter-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      background: rgba(255,255,255,0.3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      cursor: pointer;
      z-index: 20;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }
    
    .chapter-nav-left {
      left: 20px;
    }
    
    .chapter-nav-right {
      right: 20px;
    }
    
    /* Navigation buttons */
    .chapter-navigation {
      position: absolute;
      bottom: 30px;
      left: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
    
    .chapter-controls {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .nav-button {
      width: 50px;
      height: 50px;
      background: rgba(255,255,255,0.2);
      backdrop-filter: blur(5px);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 10px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    }
    
    .nav-button:hover {
      background: rgba(255,255,255,0.3);
      transform: scale(1.1);
    }
    
    .nav-button:active {
      transform: scale(0.95);
    }
    
    .nav-icon {
      color: white;
      font-size: 20px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .next-chapter {
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 8px 15px;
      border-radius: 5px;
      font-size: 14px;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;
      pointer-events: none;
    }
    
    .next-button:hover + .next-chapter,
    .next-button:hover ~ .next-chapter {
      opacity: 1;
      transform: translateY(0);
    }
    
    .chapter-button {
      background: linear-gradient(to bottom, #4a69bd, #1e3799);
      color: #fff;
      padding: 12px 35px;
      border-radius: 8px;
      font-size: 20px;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6px 0 #0c2461, 0 8px 15px rgba(0,0,0,0.4);
      min-width: 180px;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(255,255,255,0.2);
      text-shadow: 0 2px 3px rgba(0,0,0,0.5);
      transition: all 0.2s ease;
    }
    
    .chapter-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(to bottom, rgba(255,255,255,0.3), transparent);
      border-radius: 6px 6px 0 0;
    }
    
    .chapter-button::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: rgba(255,255,255,0.3);
    }
    
    .chapter-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 0 #0c2461, 0 12px 20px rgba(0,0,0,0.5);
    }
    
    .chapter-button:active {
      transform: translateY(4px);
      box-shadow: 0 2px 0 #0c2461, 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .chapter-number-display {
      position: relative;
      z-index: 2;
      display: inline-block;
      padding: 0 5px;
    }
    
    .chapter-number-display::before,
    .chapter-number-display::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 15px;
      height: 2px;
      background: rgba(255,255,255,0.7);
    }
    
    .chapter-number-display::before {
      left: -25px;
      transform: translateY(-50%);
    }
    
    .chapter-number-display::after {
      right: -25px;
      transform: translateY(-50%);
    }
    
    /* Chapter transition animation */
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .fade-in {
      animation: fadeIn 0.5s ease forwards;
    }
    
    .fade-out {
      opacity: 0;
      pointer-events: none;
    }
    
    /* User info and logout */
    .user-info {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .logout-button {
      background: rgba(255,255,255,0.2);
      color: white;
      padding: 5px 10px;
      border-radius: 5px;
      text-decoration: none;
      transition: all 0.3s ease;
    }
    
    .logout-button:hover {
      background: rgba(255,255,255,0.3);
    }
  </style>
</head>
<body>
  <div id="story-container">
    <!-- Top navigation -->
    <div class="top-nav">
      <div class="back-button" onclick="window.location.href='../menu/my-girlfriend-menu.html'">
        <div class="back-icon">←</div>
        <div class="back-text">BACK</div>
      </div>
      
      <!-- Add logout button -->
      <div class="user-info">
        <span>Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
        <a href="../register/logout.php" class="logout-button">Logout</a>
      </div>
    </div>
    
    <!-- Weather display -->
    <div class="weather-display">
      <div class="temperature">28°</div>
      <div class="month">June</div>
    </div>
    
    <!-- Chapter 2 selection -->
    <div id="chapter2-selection" class="chapter-selection">
      <div class="chapter-nav chapter-nav-left" onclick="scrollChapters('left')">←</div>
      <div class="chapter-track">
        <?php foreach ($chapters['2'] as $chapter): ?>
          <?php $isUnlocked = isChapterUnlocked($chapter['id'], $unlocked_chapters); ?>
          <div class="chapter-card <?php echo ($chapter['id'] === '02-01') ? 'chapter-active' : ''; ?>" 
               data-chapter-id="<?php echo $chapter['id']; ?>"
               data-chapter-url="<?php echo $chapter['url']; ?>"
               data-is-unlocked="<?php echo $isUnlocked ? 'true' : 'false'; ?>">
            <img src="<?php echo $chapter['thumbnail']; ?>" alt="<?php echo $chapter['title']; ?>">
            <div class="chapter-number"><?php echo $chapter['title']; ?></div>
            <?php if (!$isUnlocked): ?>
              <div class="chapter-locked">
                <div class="lock-icon"></div>
              </div>
            <?php else: ?>
              <div class="chapter-unlocked"></div>
            <?php endif; ?>
          </div>
        <?php endforeach; ?>
      </div>
      <div class="chapter-nav chapter-nav-right" onclick="scrollChapters('right')">→</div>
    </div>
    
    <!-- Chapter navigation -->
    <div class="chapter-navigation">
      <div class="chapter-controls">
        <div class="nav-button prev-button" onclick="changeChapter(1)">
          <div class="nav-icon">←</div>
        </div>
        <button class="chapter-button">
          <span class="chapter-number-display">CHAPTER <?php echo $currentChapter; ?></span>
        </button>
        <div class="nav-button next-button" style="visibility: hidden;">
          <div class="nav-icon">→</div>
        </div>
      </div>
      <div class="next-chapter">Next Chapter</div>
    </div>
  </div>

  <script>
    // Function to scroll chapter track
    function scrollChapters(direction, chapterNum = 2) {
      const track = document.querySelector(`#chapter${chapterNum}-selection .chapter-track`);
      const scrollAmount = 220; // Width of card + margin
      
      if (direction === 'left') {
        track.scrollBy({
          left: -scrollAmount,
          behavior: 'smooth'
        });
      } else {
        track.scrollBy({
          left: scrollAmount,
          behavior: 'smooth'
        });
      }
    }
    
    // Function to change chapter
    function changeChapter(chapterNum) {
      window.location.href = '../menuchapters/story-chapters.php';
    }
    
    // Add click event to chapter cards
    document.querySelectorAll('.chapter-card').forEach(card => {
      card.addEventListener('click', function() {
        const chapterId = this.dataset.chapterId;
        const chapterUrl = this.dataset.chapterUrl;
        const isUnlocked = this.dataset.isUnlocked === 'true';
        
        // Set as active
        document.querySelectorAll('.chapter-card').forEach(c => {
          c.classList.remove('chapter-active');
        });
        this.classList.add('chapter-active');
        
        // If unlocked, navigate to chapter
        if (isUnlocked) {
          window.location.href = chapterUrl;
        } else {
          alert(`Chapter ${chapterId} is locked. Complete previous chapters to unlock it.`);
        }
      });
    });
  </script>
</body>
</html>
