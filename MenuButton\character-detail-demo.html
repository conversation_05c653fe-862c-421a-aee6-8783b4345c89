<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Character Detail Demo</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="character-detail.css">
</head>
<body style="background-image: url('https://via.placeholder.com/1920x1080/1a1a2e/ffffff?text=Character+Background');">
    <div class="character-detail-container">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="nav-left">
                <a href="#" class="back-btn style-text">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title">Aria Stormwind</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count">1,250</span>
                    <div class="plus-btn" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count">45,680</span>
                    <div class="plus-btn" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count">120</span>
                    <div class="plus-btn" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="character-main">
            <!-- Left Panel - Character Image -->
            <div class="character-image-panel">
                <div class="character-portrait">
                    <!-- Character Name and Title Above Image -->
                    <div class="character-name-display">
                        <h2>Aria Stormwind</h2>
                        <h3 class="character-title-display">Lightning Mage</h3>
                    </div>

                    <img src="https://via.placeholder.com/300x400/4a90e2/ffffff?text=Character+Portrait" alt="Aria Stormwind">

                    <!-- Element Badge -->
                    <div class="element-badge" style="background: linear-gradient(45deg, #9c27b0, #673ab7);">
                        ⚡
                        <span>Lightning</span>
                    </div>

                    <!-- Rarity Stars -->
                    <div class="rarity-display">
                        <span class="star filled">★</span>
                        <span class="star filled">★</span>
                        <span class="star filled">★</span>
                        <span class="star filled">★</span>
                        <span class="star empty">★</span>
                    </div>

                    <!-- Character Level Below Stars -->
                    <div class="character-level-display">
                        <span class="level-text">Level 45</span>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Character Info -->
            <div class="character-info-panel">
                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" onclick="showTab('upgrade')">Upgrade</button>
                    <button class="tab-btn" onclick="showTab('skills')">Skills</button>
                    <button class="tab-btn" onclick="showTab('equipment')">Equipment</button>
                    <button class="tab-btn" onclick="showTab('constellation')">Constellation</button>
                    <button class="tab-btn" onclick="showTab('info')">Info</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Upgrade Tab -->
                    <div id="upgrade-tab" class="tab-panel active">
                        <div class="character-upgrade">
                            <h3>🔥 Character Upgrade</h3>
                            <div class="upgrade-header-info">
                                <span class="upgrade-subtitle">Level up your character to unlock new potential!</span>
                            </div>
                            <div class="upgrade-info">
                                <div class="level-progress">
                                    <div class="current-level">
                                        <span class="level-label">Current Level</span>
                                        <span class="level-number">45</span>
                                    </div>
                                    <div class="level-arrow">→</div>
                                    <div class="next-level">
                                        <span class="level-label">Next Level</span>
                                        <span class="level-number">46</span>
                                    </div>
                                </div>
                                
                                <div class="upgrade-requirements">
                                    <div class="requirement-item">
                                        <span class="requirement-icon">📜</span>
                                        <span class="requirement-name">Experience Logs</span>
                                        <span class="requirement-progress can-upgrade">15/10</span>
                                    </div>
                                </div>

                                <div class="upgrade-ready">
                                    <span>✅ Ready to upgrade!</span>
                                    <button class="upgrade-action-btn">
                                        <i class="fas fa-arrow-up"></i>
                                        Upgrade Character
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Upgrade Benefits -->
                        <div class="upgrade-benefits">
                            <h3>🎯 Upgrade Benefits</h3>
                            <div class="benefits-grid">
                                <div class="benefit-item">
                                    <span class="benefit-icon">⚔️</span>
                                    <span class="benefit-name">Attack</span>
                                    <span class="benefit-value">+25</span>
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">🛡️</span>
                                    <span class="benefit-name">Defense</span>
                                    <span class="benefit-value">+18</span>
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">❤️</span>
                                    <span class="benefit-name">Health</span>
                                    <span class="benefit-value">+120</span>
                                </div>
                                <div class="benefit-item">
                                    <span class="benefit-icon">⚡</span>
                                    <span class="benefit-name">Mana</span>
                                    <span class="benefit-value">+80</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills Tab -->
                    <div id="skills-tab" class="tab-panel">
                        <div class="skills-container">
                            <h3>Combat Skills</h3>
                            <div class="skill-item">
                                <div class="skill-icon">⚡</div>
                                <div class="skill-info">
                                    <div class="skill-name">Lightning Bolt</div>
                                    <div class="skill-type">Active</div>
                                    <div class="skill-description">Strikes enemy with powerful lightning, dealing high damage.</div>
                                    <div class="skill-damage">Damage: 250-300</div>
                                    <div class="skill-cooldown">Cooldown: 3 turns</div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <div class="skill-icon">🌩️</div>
                                <div class="skill-info">
                                    <div class="skill-name">Storm Shield</div>
                                    <div class="skill-type">Defensive</div>
                                    <div class="skill-description">Creates a protective barrier that absorbs damage.</div>
                                    <div class="skill-damage">Shield: 400 HP</div>
                                    <div class="skill-cooldown">Cooldown: 5 turns</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Equipment Tab -->
                    <div id="equipment-tab" class="tab-panel">
                        <div class="equipment-container">
                            <h3>Equipment</h3>
                            <div class="equipment-item">
                                <span class="equipment-type">Weapon:</span>
                                <span class="equipment-name">Storm Staff of Power</span>
                            </div>
                            <div class="equipment-item">
                                <span class="equipment-type">Armor:</span>
                                <span class="equipment-name">Lightning Mage Robes</span>
                            </div>
                            <div class="equipment-item">
                                <span class="equipment-type">Accessories:</span>
                                <div class="accessories-list">
                                    <div class="accessory-name">Thunder Ring</div>
                                    <div class="accessory-name">Storm Amulet</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Constellation Tab -->
                    <div id="constellation-tab" class="tab-panel">
                        <div class="constellation-container">
                            <h3>Constellation: Storm Caller</h3>
                            <div class="constellation-level">Level 3</div>
                            <div class="constellation-bonuses">
                                <h4>Active Bonuses:</h4>
                                <div class="bonus-item">Lightning damage increased by 15%</div>
                                <div class="bonus-item">Mana regeneration +20%</div>
                                <div class="bonus-item">Critical hit chance +10%</div>
                            </div>
                        </div>
                    </div>

                    <!-- Info Tab -->
                    <div id="info-tab" class="tab-panel">
                        <div class="character-info-section">
                            <div class="character-description">
                                <h3>📜 Description</h3>
                                <p>A powerful mage who commands the forces of lightning and storm. Born in the floating city of Nimbus, she learned to harness the power of thunder from the ancient Storm Masters.</p>
                            </div>

                            <div class="character-lore">
                                <h3>📚 Lore</h3>
                                <p>Legend speaks of Aria's ability to call down lightning from the heavens themselves. Her mastery over electrical forces makes her one of the most feared combatants in the realm.</p>
                            </div>

                            <div class="character-stats">
                                <h3>⚔️ Base Stats</h3>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-name">Attack</span>
                                        <span class="stat-value">485</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-name">Defense</span>
                                        <span class="stat-value">320</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-name">Health</span>
                                        <span class="stat-value">2,150</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-name">Mana</span>
                                        <span class="stat-value">1,680</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-name">Speed</span>
                                        <span class="stat-value">275</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-name">Critical</span>
                                        <span class="stat-value">18%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Element -->
    <div id="notification" class="notification"></div>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab panels
            const tabPanels = document.querySelectorAll('.tab-panel');
            tabPanels.forEach(panel => {
                panel.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-btn');
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab panel
            const selectedPanel = document.getElementById(`${tabName}-tab`);
            if (selectedPanel) {
                selectedPanel.classList.add('active');
            }

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Demo upgrade functionality
        document.querySelector('.upgrade-action-btn')?.addEventListener('click', function() {
            alert('Character upgraded successfully! (Demo)');
        });

        // Demo plus button functionality
        document.querySelectorAll('.plus-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Store opened! (Demo)');
            });
        });
    </script>
</body>
</html>
