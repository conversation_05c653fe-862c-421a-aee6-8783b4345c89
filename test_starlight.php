<?php
/**
 * Test Starlight System
 * This script allows testing the starlight system by adding experience and setting premium status
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: register/login.php");
    exit();
}

require_once 'includes/starlight_manager.php';
require_once 'includes/resource_manager.php';

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

$message = '';
$message_type = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_exp'])) {
        $exp_amount = intval($_POST['exp_amount']);
        if ($exp_amount > 0 && $exp_amount <= 10000) {
            if (StarlightManager::addExperience($user_id, $exp_amount)) {
                $message = "Added {$exp_amount} experience points!";
                $message_type = 'success';
            } else {
                $message = "Failed to add experience.";
                $message_type = 'error';
            }
        } else {
            $message = "Invalid experience amount (1-10000).";
            $message_type = 'error';
        }
    }
    
    if (isset($_POST['set_premium'])) {
        $is_premium = $_POST['premium_status'] === 'true';
        $expires_at = $is_premium ? date('Y-m-d H:i:s', strtotime('+30 days')) : null;
        
        if (StarlightManager::updatePremiumStatus($user_id, $is_premium, $expires_at)) {
            $message = $is_premium ? "Premium status activated for 30 days!" : "Premium status removed.";
            $message_type = 'success';
        } else {
            $message = "Failed to update premium status.";
            $message_type = 'error';
        }
    }
    
    if (isset($_POST['reset_progress'])) {
        // Reset starlight progress
        $conn = getDatabaseConnection();
        $stmt = $conn->prepare("UPDATE user_starlight_progress SET current_level = 1, current_exp = 0, exp_to_next_level = 1000, claimed_free_rewards = '[]', claimed_premium_rewards = '[]' WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        
        if ($stmt->execute()) {
            $message = "Starlight progress reset to level 1!";
            $message_type = 'success';
        } else {
            $message = "Failed to reset progress.";
            $message_type = 'error';
        }
        
        $stmt->close();
        $conn->close();
    }
}

// Get current user data
$progress = StarlightManager::getUserStarlightProgress($user_id);
$is_premium = StarlightManager::isUserPremium($user_id);
$resources = ResourceManager::getUserResources($user_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Starlight System</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(40, 44, 52, 0.9);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(74, 144, 226, 0.3);
        }
        h1, h2 { color: #ffd700; }
        .form-group {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, button {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        button {
            background: #ff4444;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover { background: #ff6666; }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #4caf50; color: white; }
        .error { background: #f44336; color: white; }
        .info-box {
            background: rgba(0, 184, 255, 0.1);
            border: 1px solid #00b8ff;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .nav-links {
            margin-top: 30px;
            text-align: center;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #00b8ff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .nav-links a:hover { background: #0099cc; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Starlight System Test Panel</h1>
        <p>Welcome, <strong><?php echo htmlspecialchars($username); ?></strong>!</p>
        
        <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>Current Status</h3>
            <p><strong>Level:</strong> <?php echo $progress['current_level']; ?></p>
            <p><strong>Experience:</strong> <?php echo $progress['current_exp']; ?> / <?php echo $progress['exp_to_next_level']; ?></p>
            <p><strong>Premium Status:</strong> <?php echo $is_premium ? '✅ Active' : '❌ Not Active'; ?></p>
            <p><strong>Free Rewards Claimed:</strong> <?php echo count($progress['claimed_free_rewards']); ?></p>
            <p><strong>Premium Rewards Claimed:</strong> <?php echo count($progress['claimed_premium_rewards']); ?></p>
            <p><strong>Resources:</strong> 💎 <?php echo number_format($resources['diamonds']); ?> | 🪙 <?php echo number_format($resources['coins']); ?> | ⚡ <?php echo $resources['energy']; ?></p>
        </div>
        
        <form method="POST">
            <div class="form-group">
                <h3>Add Experience Points</h3>
                <label for="exp_amount">Experience Amount (1-10000):</label>
                <input type="number" id="exp_amount" name="exp_amount" min="1" max="10000" value="1000" required>
                <button type="submit" name="add_exp">Add Experience</button>
                <p><em>Note: Each level requires progressively more EXP. Level 1→2 needs 1000 EXP, Level 2→3 needs 1200 EXP, etc.</em></p>
            </div>
        </form>
        
        <form method="POST">
            <div class="form-group">
                <h3>Premium Status</h3>
                <label for="premium_status">Premium Status:</label>
                <select id="premium_status" name="premium_status" required>
                    <option value="true" <?php echo $is_premium ? 'selected' : ''; ?>>Premium (30 days)</option>
                    <option value="false" <?php echo !$is_premium ? 'selected' : ''; ?>>Free Player</option>
                </select>
                <button type="submit" name="set_premium">Update Premium Status</button>
            </div>
        </form>
        
        <form method="POST" onsubmit="return confirm('Are you sure you want to reset all starlight progress? This cannot be undone.')">
            <div class="form-group">
                <h3>Reset Progress</h3>
                <p>Reset starlight progress back to level 1 and clear all claimed rewards.</p>
                <button type="submit" name="reset_progress" style="background: #ff9800;">Reset Starlight Progress</button>
            </div>
        </form>
        
        <div class="nav-links">
            <a href="MenuButton/Starlight.php">🌟 Go to Starlight Page</a>
            <a href="menu/home.php">🏠 Back to Game</a>
            <a href="MenuButton/store.php">🛒 Store (Buy Premium)</a>
        </div>
        
        <div class="info-box">
            <h3>How to Test</h3>
            <ol>
                <li><strong>Add Experience:</strong> Use the form above to add EXP and level up</li>
                <li><strong>Set Premium:</strong> Toggle premium status to test premium rewards</li>
                <li><strong>Visit Starlight Page:</strong> Go to the actual starlight page to claim rewards</li>
                <li><strong>Test Claiming:</strong> Try claiming both free and premium rewards</li>
                <li><strong>Reset if Needed:</strong> Reset progress to test from the beginning</li>
            </ol>
        </div>
    </div>
</body>
</html>
