/* Shared Styles for MenuButton Pages */

/* CSS Variables */
:root {
    --primary-color: #4a90e2;
    --secondary-color: #ff6b9d;
    --accent-gold: #ffd700;
    --dark-bg: #0a0e1a;
    --card-bg: rgba(20, 25, 40, 0.9);
    --text-light: #ffffff;
    --text-gray: #b0b8c8;
    --border-glow: rgba(74, 144, 226, 0.5);
    --event-bg: rgba(15, 20, 35, 0.95);
    --top-nav-bg: rgba(10, 14, 26, 0.95);
    --resource-bg: rgba(20, 25, 40, 0.9);
}

/* ===== SHARED TOP NAVIGATION STYLES ===== */

/* Main Top Navigation Container */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 25px;
    background: var(--top-nav-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-glow);
    position: relative;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Left side navigation */
.top-left, .nav-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Right side navigation */
.top-right, .nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Page Title Styling */
.page-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-light);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

/* Resource Display Items - Support both .resource and .resource-item for compatibility */
.resource, .resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--resource-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
    position: relative;
    transition: all 0.3s ease;
}

.resource:hover, .resource-item:hover {
    background: rgba(30, 35, 50, 0.9);
    border-color: var(--border-glow);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
}

/* Resource Icons */
.resource-icon {
    font-size: 16px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Plus Buttons for Resources */
.plus-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary-color), #357abd);
    border: 1px solid var(--accent-gold);
    border-radius: 50%;
    color: white;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 4px;
}

.plus-btn:hover {
    background: linear-gradient(135deg, var(--secondary-color), #e55a87);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.plus-btn:active {
    transform: scale(0.95);
}

/* Time Display (for story chapters) */
.day-display {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
    font-weight: 600;
}

.day-number, .month-name {
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.time-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    font-size: 12px;
}

.time-icon {
    font-size: 14px;
}

/* User Info Display */
.user-info {
    background: linear-gradient(to right, rgba(0,0,0,0.5), rgba(255,77,126,0.3));
    padding: 10px 20px;
    border-radius: 15px;
    border: 1px solid rgba(255,255,255,0.15);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.user-info span {
    font-weight: 600;
    letter-spacing: 0.5px;
    color: var(--text-light);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    margin-right: 15px;
}

/* Navigation Icons (for home page) */
.settings-icon, .notification-icon, .mail-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--resource-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-icon:hover, .notification-icon:hover, .mail-icon:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

/* Responsive Design for Top Navigation */
@media (max-width: 768px) {
    .top-nav {
        padding: 12px 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .top-left, .nav-left {
        gap: 15px;
    }

    .top-right, .nav-right {
        gap: 10px;
        flex-wrap: wrap;
    }

    .page-title {
        font-size: 16px;
    }

    .resource, .resource-item {
        font-size: 12px;
        padding: 6px 10px;
    }

    .resource-icon {
        font-size: 14px;
    }

    .plus-btn {
        width: 18px;
        height: 18px;
        font-size: 11px;
    }

    .settings-icon, .notification-icon, .mail-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

/* ===== UNIVERSAL BACK BUTTON STYLES ===== */
.back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--card-bg);
    border: 1px solid var(--border-glow);
    border-radius: 8px;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 16px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.back-btn:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
    border-color: var(--accent-gold);
}

.back-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* Alternative back button styles */
.back-btn.style-rounded {
    border-radius: 50%;
    width: 45px;
    height: 45px;
}

.back-btn.style-large {
    width: 50px;
    height: 50px;
    font-size: 18px;
}

.back-btn.style-text {
    background: rgba(74, 144, 226, 0.1);
    border: 2px solid var(--border-glow);
}

/* Icon styles for back button */
.back-btn i {
    font-size: inherit;
    transition: transform 0.3s ease;
}

.back-btn:hover i {
    transform: translateX(-2px);
}

/* Responsive adjustments for back button */
@media (max-width: 768px) {
    .back-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .back-btn.style-large {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .back-btn.style-rounded {
        width: 40px;
        height: 40px;
    }
}

/* ===== SHARED JAVASCRIPT FUNCTIONS ===== */
/*
Add this JavaScript to pages that use plus buttons:

function goToStore(resourceType) {
    // Add a small animation effect
    if (event && event.target) {
        event.target.style.transform = 'scale(0.8)';
        setTimeout(() => {
            event.target.style.transform = 'scale(1.1)';
            setTimeout(() => {
                event.target.style.transform = '';
                // Navigate to store page with the specific resource type
                window.location.href = `store.php?resource=${resourceType}`;
            }, 100);
        }, 100);
    } else {
        // Fallback without animation
        window.location.href = `store.php?resource=${resourceType}`;
    }
}
*/
