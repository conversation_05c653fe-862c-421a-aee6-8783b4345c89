:root {
    --primary-color: #00b8ff;
    --secondary-color: #ff4d7e;
    --dark-bg: #1a1a1a;
    --card-bg: rgba(40, 44, 52, 0.85);
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --accent-orange: #ff6b00;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--dark-bg);
    color: var(--text-light);
    overflow-x: hidden;
}

#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-image: url('../assets/backcharacter.png');
    background-size: cover;
    background-position: center;
}

/* Top navigation bar */
.top-nav {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 100;
}

.top-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.settings-icon, .notification-icon, .mail-icon, .calendar-icon, .bag-icon {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.mail-icon .notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--accent-orange);
    color: white;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    font-weight: bold;
    border: 2px solid var(--dark-bg);
}

.settings-icon:hover, .notification-icon:hover, .mail-icon:hover, .calendar-icon:hover, .bag-icon:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.top-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Resource Display Items - Support both .resource and .resource-item for compatibility */
.resource, .resource-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(30, 35, 50, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
    position: relative;
    transition: all 0.3s ease;
}

.resource:hover, .resource-item:hover {
    background: rgba(30, 35, 50, 0.9);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 184, 255, 0.2);
}

.resource-icon {
    font-size: 16px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Plus Buttons for Resources */
.plus-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--primary-color), #357abd);
    border: 1px solid var(--accent-orange);
    border-radius: 50%;
    color: white;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 4px;
}

.plus-btn:hover {
    background: linear-gradient(135deg, var(--secondary-color), #e55a87);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 184, 255, 0.4);
}

.plus-btn:active {
    transform: scale(0.95);
}

/* Character display */
.character-display {
    position: absolute;
    left: 50px;
    bottom: 0;
    height: 105%;
    z-index: 10;
}

/* Level indicator */
.level-indicator {
    position: absolute;
    left: 30px;
    top: 150px;
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 20;
}

.level-number {
    font-size: 24px;
    font-weight: bold;
}

.level-text {
    font-size: 12px;
    text-transform: uppercase;
}

/* Username display */
.username-display {
    position: absolute;
    left: 30px;
    top: 220px;
    background: rgba(0, 0, 0, 0.7);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 16px;
    z-index: 20;
}

/* Main menu cards */
.menu-cards {
    position: absolute;
    right: 30px;
    top: 100px;
    bottom: 30px;
    width: 60%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 20px;
    z-index: 20;
}

.menu-card {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    position: relative;
}

.menu-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.card-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.card-subtitle {
    font-size: 14px;
    color: var(--text-gray);
}

.notification-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--accent-orange);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
}

/* Bottom navigation */
.bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding: 10px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 100;
}

/* News banner */
.news-banner {
    position: absolute;
    left: 47px;
    bottom: 20px;
    width: 300px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    overflow: hidden;
    z-index: 20;
}

.news-header {
    background: var(--accent-orange);
    color: white;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: bold;
}

.news-content {
    padding: 10px;
}

.news-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.news-subtitle {
    font-size: 12px;
    color: var(--text-gray);
}

/* Settings Modal */
.settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.settings-modal.active {
    opacity: 1;
    visibility: visible;
}

.settings-content {
    background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(40, 44, 52, 0.9));
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-orange)) 1;
    border-radius: 15px;
    padding: 30px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transform: translateY(20px);
    transition: all 0.3s ease;
    color: white;
}

.settings-modal.active .settings-content {
    transform: translateY(0);
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.settings-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.close-settings {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-settings:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.profile-section {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.profile-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.profile-label {
    font-weight: 600;
    color: var(--text-gray);
}

.profile-value {
    font-weight: 700;
    color: white;
}

.logout-section {
    margin-top: 20px;
}

.logout-button {
    background: linear-gradient(45deg, #ff4d7e, var(--secondary-color));
    color: white;
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: inline-block;
    box-shadow: 0 4px 10px rgba(255, 77, 126, 0.3);
}

.logout-button:hover {
    background: linear-gradient(45deg, var(--secondary-color), #ff4d7e);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 77, 126, 0.5);
}

/* Logout confirmation modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(50,0,80,0.9));
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-orange)) 1;
    border-radius: 15px;
    padding: 25px;
    width: 90%;
    max-width: 350px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transform: translateY(20px);
    transition: all 0.3s ease;
    color: white;
}

.modal-overlay.active .modal-content {
    transform: translateY(0);
}

.modal-content h3 {
    margin-top: 0;
    font-size: 24px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 15px;
}

.modal-content p {
    margin-bottom: 25px;
    font-size: 16px;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.modal-buttons button {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.cancel-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.confirm-btn {
    background: linear-gradient(45deg, #ff4d7e, var(--secondary-color));
    color: white;
    box-shadow: 0 4px 10px rgba(255, 77, 126, 0.3);
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.confirm-btn:hover {
    background: linear-gradient(45deg, var(--secondary-color), #ff4d7e);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 77, 126, 0.5);
}

/* Mail Modal Styles */
.mail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1002;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mail-modal.active {
    opacity: 1;
    visibility: visible;
}

.mail-container {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.95), rgba(40, 44, 52, 0.95));
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) 1;
    border-radius: 15px;
    width: 90%;
    max-width: 900px;
    height: 80%;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.7);
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.mail-modal.active .mail-container {
    transform: translateY(0);
}

.mail-header {
    background: linear-gradient(135deg, rgba(0, 184, 255, 0.2), rgba(255, 77, 126, 0.2));
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mail-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mail-tabs {
    display: flex;
    gap: 10px;
}

.mail-tab {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
}

.mail-tab.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.mail-tab:hover {
    background: rgba(255, 255, 255, 0.2);
}

.close-mail {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-mail:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.mail-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.mail-sidebar {
    width: 200px;
    background: rgba(0, 0, 0, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
}

.mail-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.mail-viewer {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    position: relative;
}

.mail-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.mail-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
}

.mail-item.unread {
    border-left: 3px solid var(--accent-orange);
    background: rgba(255, 107, 0, 0.1);
}

.mail-item.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.mail-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.mail-sender {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 12px;
}

.mail-date {
    font-size: 10px;
    color: var(--text-gray);
}

.mail-subject {
    font-size: 13px;
    font-weight: 600;
    color: white;
    margin-bottom: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mail-preview {
    font-size: 11px;
    color: var(--text-gray);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.mail-attachment {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--accent-orange);
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

/* Mail Viewer Styles */
.mail-viewer-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.mail-viewer-subject {
    font-size: 20px;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
}

.mail-viewer-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: var(--text-gray);
}

.mail-viewer-sender {
    color: var(--primary-color);
    font-weight: 600;
}

.mail-viewer-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.mail-viewer-message {
    color: white;
    line-height: 1.6;
    font-size: 16px;
    margin-bottom: 20px;
}

.mail-viewer-attachment {
    background: rgba(255, 107, 0, 0.1);
    border: 1px solid var(--accent-orange);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.attachment-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    color: var(--accent-orange);
    font-weight: 600;
}

.attachment-rewards {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.reward-item {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    font-weight: 600;
}

.claim-attachment-btn {
    background: linear-gradient(135deg, var(--accent-orange), #ff8c42);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.claim-attachment-btn:hover {
    background: linear-gradient(135deg, #ff8c42, var(--accent-orange));
    transform: translateY(-2px);
}

.claim-attachment-btn:disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    transform: none;
}

.no-mail-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-gray);
    font-size: 18px;
    text-align: center;
}

.no-mail-selected .mail-icon-large {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.3;
}

.no-mail {
    text-align: center;
    padding: 40px;
    color: var(--text-gray);
    font-size: 16px;
}

.mail-actions {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 10px;
    justify-content: center;
}

.mail-action-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, var(--primary-color), #357abd);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
}

.mail-action-btn:hover {
    background: linear-gradient(135deg, var(--secondary-color), #e55a87);
    transform: translateY(-2px);
}

.mail-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}



/* Responsive Design */
@media (max-width: 768px) {
    .mail-container {
        width: 95%;
        height: 90%;
    }

    .mail-sidebar {
        width: 150px;
    }

    .mail-item {
        padding: 8px;
    }

    .mail-subject {
        font-size: 12px;
    }

    .mail-preview {
        font-size: 10px;
    }

    .mail-viewer-header {
        padding: 15px;
    }

    .mail-viewer-subject {
        font-size: 18px;
    }

    .mail-viewer-body {
        padding: 15px;
    }

    .mail-actions {
        padding: 10px 15px;
    }

    .mail-action-btn {
        padding: 6px 12px;
        font-size: 12px;
    }


}
