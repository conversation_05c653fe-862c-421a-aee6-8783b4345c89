/* Turn-Based RPG - Honkai Star Rail Style CSS */

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
}

canvas {
  display: block;
  width: 100vw !important;
  height: 100vh !important;
}

.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

/* Honkai Star Rail Style UI Layout */
.battle-ui {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  height: 100px;
  pointer-events: none;
  z-index: 100;
  display: flex;
  align-items: end;
  justify-content: space-between;
  padding: 0 20px;
}

/* Ultimate Buttons (Far Left) */
.ultimate-panel {
  display: flex;
  gap: 10px;
  pointer-events: auto;
  margin-left: 15px;
}

.ultimate-button {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border: 3px solid #4a90e2;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.ultimate-button:hover {
  transform: translateY(-5px);
  border-color: #ffaa00;
  box-shadow: 0 10px 20px rgba(255, 170, 0, 0.3);
}

.ultimate-button.ultimate-ready {
  border-color: #ff6600;
  box-shadow: 0 0 25px rgba(255, 102, 0, 0.7);
  animation: ultimateGlow 1.5s ease-in-out infinite;
}

.ultimate-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.portrait-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.character-hp-bar {
  position: absolute;
  bottom: 5px;
  left: 5px;
  right: 5px;
  height: 4px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
}

.hp-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444, #ff6666);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.character-energy-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: conic-gradient(#00ff88 0deg, rgba(0,255,136,0.2) 0deg);
  border: 2px solid rgba(0, 255, 136, 0.5);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
  font-weight: bold;
}

.energy-ready {
  background: linear-gradient(135deg, #ff6600, #ff9900) !important;
  border-color: #ff6600;
  box-shadow: 0 0 15px rgba(255, 102, 0, 0.7);
  animation: ultimateGlow 1s ease-in-out infinite;
}

/* Action Buttons (Far Right) */
.action-panel {
  display: flex;
  gap: 15px;
  pointer-events: auto;
}

.action-button {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #2a2a3e, #1a1a2e);
  border: 2px solid #4a90e2;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.action-button:hover {
  transform: translateY(-3px);
  border-color: #ffaa00;
  box-shadow: 0 8px 16px rgba(255, 170, 0, 0.3);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  border-color: #666;
}

.action-icon {
  font-size: 24px;
  margin-bottom: 2px;
}

.action-cost {
  font-size: 8px;
  color: #ffaa00;
}

.character-name {
  color: #fff;
  font-weight: bold;
  margin-bottom: 5px;
}

.health-bar, .energy-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  margin: 3px 0;
  overflow: hidden;
}

.health-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444, #ff6666);
  transition: width 0.3s ease;
}

.energy-fill {
  height: 100%;
  background: linear-gradient(90deg, #4444ff, #6666ff);
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-btn {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: linear-gradient(135deg, #357abd, #2968a3);
  transform: translateY(-2px);
}

.action-btn:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
}

/* Turn Order Display (Top Left) */
.turn-order-display {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #4a90e2;
  border-radius: 10px;
  padding: 15px;
  color: white;
  min-width: 200px;
  pointer-events: auto;
}

.turn-order-title {
  font-size: 14px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 10px;
  text-align: center;
}

.turn-order-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.turn-order-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.turn-order-item.active {
  background: rgba(74, 144, 226, 0.3);
  border: 1px solid #4a90e2;
}

.turn-order-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.turn-order-name {
  font-size: 12px;
  font-weight: bold;
  color: #fff;
}

.turn-indicator {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10px 20px;
  border-radius: 20px;
  border: 2px solid #4a90e2;
  font-weight: bold;
  display: none; /* Hide old indicator */
}

.damage-text {
  position: absolute;
  color: #ff4444;
  font-weight: bold;
  font-size: 24px;
  pointer-events: none;
  animation: damageFloat 1s ease-out forwards;
}

@keyframes damageFloat {
  0% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-50px); }
}

/* Game Results Container */
.game-results {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.5s ease-in;
}

.results-container {
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  border: 3px solid #4a90e2;
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.5s ease-out;
}

.results-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.victory-title {
  color: #00ff88;
  text-shadow: 0 0 20px #00ff88;
}

.defeat-title {
  color: #ff4444;
  text-shadow: 0 0 20px #ff4444;
}

.results-stats {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
  color: #fff;
  font-size: 16px;
}

.stat-label {
  color: #ccc;
}

.stat-value {
  font-weight: bold;
  color: #4a90e2;
}

.countdown-text {
  color: #fff;
  font-size: 18px;
  margin-top: 20px;
}

.countdown-number {
  color: #ffaa00;
  font-weight: bold;
  font-size: 24px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.victory-glow {
  animation: victoryGlow 2s ease-in-out infinite;
}

.defeat-shake {
  animation: defeatShake 0.5s ease-in-out;
}

@keyframes victoryGlow {
  0%, 100% { box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5); }
  50% { box-shadow: 0 20px 40px rgba(0, 255, 136, 0.3); }
}

@keyframes defeatShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes activePulse {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.5); }
  50% { box-shadow: 0 0 30px rgba(0, 255, 136, 0.8); }
}

@keyframes ultimateGlow {
  0%, 100% { box-shadow: 0 0 25px rgba(255, 102, 0, 0.7); }
  50% { box-shadow: 0 0 35px rgba(255, 102, 0, 1); }
}

/* Enemy Info Display (Hidden - using new right container) */
.enemy-info {
  display: none;
}

.enemy-name {
  font-size: 16px;
  font-weight: bold;
  color: #ff6666;
  margin-bottom: 8px;
}

.enemy-hp-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.enemy-hp-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.enemy-hp-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444, #ff6666);
  transition: width 0.3s ease;
}

/* Team Energy Display (Bottom - Next to Ultimate) */
.team-energy-display {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #00ff88;
  border-radius: 10px;
  padding: 15px;
  color: white;
  min-width: 120px;
  pointer-events: auto;
}

.energy-title {
  font-size: 14px;
  font-weight: bold;
  color: #00ff88;
  margin-bottom: 8px;
  text-align: center;
}

.energy-dots {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 8px;
}

.energy-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #00ff88;
  background: rgba(0, 255, 136, 0.2);
  transition: all 0.3s ease;
}

.energy-dot.active {
  background: linear-gradient(135deg, #00ff88, #00cc66);
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.energy-text {
  font-size: 12px;
  text-align: center;
  color: #ccc;
}

/* Top Right Buttons Container */
.top-right-buttons {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  pointer-events: auto;
}

/* Pause Button */
.pause-button {
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #4a90e2;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.pause-button:hover {
  border-color: #ffaa00;
  box-shadow: 0 5px 15px rgba(255, 170, 0, 0.3);
  transform: translateY(-2px);
}

/* Surrender Button */
.surrender-button {
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #ff4444;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.surrender-button:hover {
  border-color: #ff6666;
  box-shadow: 0 5px 15px rgba(255, 68, 68, 0.3);
  transform: translateY(-2px);
  background: rgba(255, 68, 68, 0.1);
}

/* Enemy Status Container (Next to Character Display) */
.enemy-status-container {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #ff4444;
  border-radius: 10px;
  padding: 15px;
  color: white;
  min-width: 160px;
  pointer-events: auto;
}

.enemy-status-name {
  font-size: 18px;
  font-weight: bold;
  color: #ff6666;
  margin-bottom: 10px;
  text-align: center;
}

.enemy-status-hp {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.enemy-status-hp-bar {
  flex: 1;
  height: 10px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  overflow: hidden;
}

.enemy-status-hp-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444, #ff6666);
  transition: width 0.3s ease;
}

.enemy-status-hp-text {
  font-size: 12px;
  color: #ccc;
  min-width: 60px;
  text-align: right;
}

/* Character Display Container (Center) */
.character-display-container {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #4a90e2;
  border-radius: 10px;
  padding: 15px;
  color: white;
  min-width: 180px;
  pointer-events: auto;
}

.character-display-title {
  font-size: 14px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 10px;
  text-align: center;
}

.character-display-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.character-display-name {
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  min-width: 60px;
}

.character-display-hp-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.character-display-hp-fill {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2, #6bb6ff);
  transition: width 0.3s ease;
}

.character-display-hp-text {
  font-size: 10px;
  color: #ccc;
  min-width: 50px;
  text-align: right;
}

.character-status-title {
  font-size: 14px;
  font-weight: bold;
  color: #00ff88;
  margin-bottom: 10px;
  text-align: center;
}

.character-status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.character-status-name {
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  min-width: 60px;
}

.character-status-hp-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.character-status-hp-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff88, #00cc66);
  transition: width 0.3s ease;
}

.character-status-hp-text {
  font-size: 10px;
  color: #ccc;
  min-width: 50px;
  text-align: right;
}
