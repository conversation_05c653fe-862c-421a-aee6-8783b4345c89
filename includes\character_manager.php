<?php
/**
 * Character Manager Class
 * Handles character data operations from JSON database
 */

class CharacterManager {
    private $charactersFile;
    private $charactersData;

    public function __construct() {
        $this->charactersFile = __DIR__ . '/../data/characters.json';
        $this->loadCharacters();
    }

    /**
     * Load characters from JSON file
     */
    private function loadCharacters() {
        if (file_exists($this->charactersFile)) {
            $jsonData = file_get_contents($this->charactersFile);
            $this->charactersData = json_decode($jsonData, true);
        } else {
            $this->charactersData = ['characters' => []];
        }
    }

    /**
     * Get all characters
     * @return array Array of all characters
     */
    public function getAllCharacters() {
        return $this->charactersData['characters'] ?? [];
    }

    /**
     * Get character by ID
     * @param int $id Character ID
     * @return array|null Character data or null if not found
     */
    public function getCharacterById($id) {
        $characters = $this->getAllCharacters();
        foreach ($characters as $character) {
            if ($character['id'] == $id) {
                return $character;
            }
        }
        return null;
    }

    /**
     * Get characters by element
     * @param string $element Element type (fire, ice, lightning, etc.)
     * @return array Array of characters with specified element
     */
    public function getCharactersByElement($element) {
        $characters = $this->getAllCharacters();
        return array_filter($characters, function($character) use ($element) {
            return $character['element'] === $element;
        });
    }

    /**
     * Get unlocked characters only
     * @return array Array of unlocked characters
     */
    public function getUnlockedCharacters() {
        $characters = $this->getAllCharacters();
        return array_filter($characters, function($character) {
            return $character['status'] === 'unlocked';
        });
    }

    /**
     * Get characters by rarity
     * @param int $rarity Rarity level (1-5)
     * @return array Array of characters with specified rarity
     */
    public function getCharactersByRarity($rarity) {
        $characters = $this->getAllCharacters();
        return array_filter($characters, function($character) use ($rarity) {
            return $character['rarity'] == $rarity;
        });
    }

    /**
     * Update character level
     * @param int $id Character ID
     * @param int $newLevel New level
     * @return bool Success status
     */
    public function updateCharacterLevel($id, $newLevel) {
        $characters = $this->getAllCharacters();
        for ($i = 0; $i < count($characters); $i++) {
            if ($characters[$i]['id'] == $id) {
                $characters[$i]['level'] = $newLevel;
                $this->charactersData['characters'] = $characters;
                return $this->saveCharacters();
            }
        }
        return false;
    }

    /**
     * Unlock character
     * @param int $id Character ID
     * @return bool Success status
     */
    public function unlockCharacter($id) {
        $characters = $this->getAllCharacters();
        for ($i = 0; $i < count($characters); $i++) {
            if ($characters[$i]['id'] == $id) {
                $characters[$i]['status'] = 'unlocked';
                $this->charactersData['characters'] = $characters;
                return $this->saveCharacters();
            }
        }
        return false;
    }

    /**
     * Get character stats formatted for display
     * @param int $id Character ID
     * @return array|null Formatted stats or null if character not found
     */
    public function getCharacterStats($id) {
        $character = $this->getCharacterById($id);
        if (!$character || !isset($character['stats'])) {
            return null;
        }

        $stats = $character['stats'];
        return [
            'HP' => number_format($stats['hp']),
            'ATK' => number_format($stats['attack']),
            'DEF' => number_format($stats['defense']),
            'SPD' => number_format($stats['speed']),
            'CRIT Rate' => $stats['critical_rate'] . '%',
            'CRIT DMG' => $stats['critical_damage'] . '%'
        ];
    }

    /**
     * Get element color for UI display
     * @param string $element Element type
     * @return string CSS color value
     */
    public static function getElementColor($element) {
        switch($element) {
            case 'fire': return '#ff5722';
            case 'ice': return '#00bcd4';
            case 'lightning': return '#ffeb3b';
            case 'water': return '#2196f3';
            case 'earth': return '#4caf50';
            case 'wind': return '#00bcd4';
            case 'light': return '#ffeb3b';
            case 'dark': return '#9c27b0';
            default: return '#666';
        }
    }

    /**
     * Get element icon for UI display
     * @param string $element Element type
     * @return string Element emoji icon
     */
    public static function getElementIcon($element) {
        switch($element) {
            case 'fire': return '🔥';
            case 'ice': return '❄️';
            case 'lightning': return '⚡';
            case 'water': return '💧';
            case 'earth': return '🌱';
            case 'wind': return '💨';
            case 'light': return '✨';
            case 'dark': return '🌙';
            default: return '❓';
        }
    }

    /**
     * Save characters data to JSON file
     * @return bool Success status
     */
    private function saveCharacters() {
        $jsonData = json_encode($this->charactersData, JSON_PRETTY_PRINT);
        return file_put_contents($this->charactersFile, $jsonData) !== false;
    }

    /**
     * Get character power level (calculated from stats)
     * @param int $id Character ID
     * @return int Power level
     */
    public function getCharacterPower($id) {
        $character = $this->getCharacterById($id);
        if (!$character || !isset($character['stats'])) {
            return 0;
        }

        $stats = $character['stats'];
        // Calculate power as weighted sum of stats
        $power = ($stats['hp'] * 0.3) + 
                ($stats['attack'] * 0.4) + 
                ($stats['defense'] * 0.2) + 
                ($stats['speed'] * 0.1);
        
        return round($power);
    }

    /**
     * Search characters by name
     * @param string $searchTerm Search term
     * @return array Array of matching characters
     */
    public function searchCharacters($searchTerm) {
        $characters = $this->getAllCharacters();
        return array_filter($characters, function($character) use ($searchTerm) {
            return stripos($character['name'], $searchTerm) !== false ||
                   stripos($character['title'], $searchTerm) !== false;
        });
    }
}
?>
