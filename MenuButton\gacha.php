<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../register/login.php');
    exit;
}

// Include required managers
require_once '../includes/resource_manager.php';
require_once '../includes/gacha_manager.php';

// Get username and user ID
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Initialize gacha tickets if not exists
GachaManager::initializeUserTickets($user_id);

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Get user tickets
$tickets = GachaManager::getUserTickets($user_id);

// Get active gacha pools
$pools = GachaManager::getActivePools();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Gacha</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        /* Gacha Page - Hollow Hymnal Style */
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #ff6b9d;
            --accent-gold: #ffd700;
            --dark-bg: #0a0e1a;
            --card-bg: rgba(20, 25, 40, 0.9);
            --text-light: #ffffff;
            --text-gray: #b0b8c8;
            --border-glow: rgba(74, 144, 226, 0.5);
            --event-bg: rgba(15, 20, 35, 0.95);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f35 50%, #2a2f45 100%);
            color: var(--text-light);
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Background Effects */
        .gacha-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, rgba(74, 144, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 107, 157, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #0a0e1a 0%, #1a1f35 100%);
            z-index: -1;
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }

        /* Top Navigation - Using shared styles from shared-styles.css */

        /* Main Content Layout */
        .main-content {
            display: flex;
            height: calc(100vh - 70px);
            position: relative;
            z-index: 10;
        }

        /* Event Sidebar */
        .event-sidebar {
            width: 200px;
            background: var(--event-bg);
            border-right: 1px solid var(--border-glow);
            padding: 20px 0;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .event-nav-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .event-nav-item:hover {
            background: rgba(74, 144, 226, 0.1);
            border-left-color: var(--primary-color);
        }

        .event-nav-item.active {
            background: rgba(74, 144, 226, 0.2);
            border-left-color: var(--primary-color);
        }

        .event-label {
            font-size: 12px;
            color: var(--text-gray);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }

        .event-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-light);
        }

        /* Banner Area */
        .banner-area {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .event-banner {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .event-banner.active {
            opacity: 1;
            visibility: visible;
        }

        .banner-background {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a2040 0%, #2a3060 50%, #3a4080 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 40px;
        }

        .standard-bg {
            background: linear-gradient(135deg, #2a2040 0%, #3a3060 50%, #4a4080 100%);
        }

        .premium-bg {
            background: linear-gradient(135deg, #402020 0%, #603030 50%, #804040 100%);
        }

        .banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 70% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 30% 70%, rgba(74, 144, 226, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Character Showcase */
        .character-showcase {
            display: flex;
            align-items: center;
            gap: 30px;
            z-index: 2;
            position: relative;
        }

        .main-character {
            text-align: center;
            position: relative;
        }

        .character-img {
            width: 200px;
            height: 300px;
            object-fit: cover;
            border-radius: 15px;
            border: 3px solid var(--accent-gold);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }

        .character-img.main {
            width: 250px;
            height: 350px;
        }

        .character-img:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.5);
        }

        .character-name {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-light);
            margin-top: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .character-rarity {
            font-size: 16px;
            color: var(--accent-gold);
            margin-top: 5px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .side-characters {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .side-character .character-img {
            width: 120px;
            height: 180px;
        }

        .side-character .character-name {
            font-size: 14px;
            margin-top: 10px;
        }

        .side-character .character-rarity {
            font-size: 12px;
        }

        /* Banner Info */
        .banner-info {
            text-align: right;
            z-index: 2;
            position: relative;
            max-width: 400px;
        }

        .banner-title {
            font-size: 48px;
            font-weight: 900;
            color: var(--text-light);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--accent-gold), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .banner-subtitle {
            font-size: 16px;
            color: var(--text-gray);
            line-height: 1.6;
            margin-bottom: 25px;
            font-style: italic;
        }

        .banner-details {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 15px;
        }

        .guarantee-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid var(--accent-gold);
            border-radius: 20px;
            font-size: 14px;
            color: var(--accent-gold);
        }

        /* Pull Controls */
        .pull-controls {
            position: absolute;
            bottom: 30px;
            right: 30px;
            z-index: 10;
        }

        .pull-buttons {
            display: flex;
            gap: 15px;
        }

        .pull-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 25px;
            background: linear-gradient(135deg, var(--primary-color), #357abd);
            border: 2px solid var(--accent-gold);
            border-radius: 12px;
            color: var(--text-light);
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pull-btn.multi {
            background: linear-gradient(135deg, var(--secondary-color), #e55a87);
        }

        .pull-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(74, 144, 226, 0.4);
        }

        .pull-btn.multi:hover {
            box-shadow: 0 10px 25px rgba(255, 107, 157, 0.4);
        }

        .pull-btn:active {
            transform: translateY(-1px);
        }

        .pull-icon {
            font-size: 24px;
        }

        .pull-text span {
            display: block;
            font-size: 18px;
            font-weight: 700;
        }

        .pull-text small {
            display: block;
            font-size: 12px;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #45a049);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .event-sidebar {
                width: 100%;
                height: auto;
                flex-direction: row;
                overflow-x: auto;
                padding: 10px;
            }

            .event-nav-item {
                min-width: 150px;
                border-left: none;
                border-bottom: 3px solid transparent;
            }

            .event-nav-item:hover,
            .event-nav-item.active {
                border-left: none;
                border-bottom-color: var(--primary-color);
            }

            .banner-background {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .character-showcase {
                flex-direction: column;
                gap: 20px;
            }

            .character-img.main {
                width: 200px;
                height: 280px;
            }

            .banner-title {
                font-size: 32px;
            }

            .pull-controls {
                position: relative;
                bottom: auto;
                right: auto;
                margin-top: 20px;
                display: flex;
                justify-content: center;
            }

            /* Top navigation responsive styles handled by shared-styles.css */
        }
    </style>
</head>
<body>
    <!-- Background Effects -->
    <div class="gacha-background"></div>
    <div class="floating-particles">
        <div class="particle" style="width: 4px; height: 4px; left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="width: 6px; height: 6px; left: 30%; animation-delay: 3s;"></div>
        <div class="particle" style="width: 3px; height: 3px; left: 50%; animation-delay: 6s;"></div>
        <div class="particle" style="width: 5px; height: 5px; left: 70%; animation-delay: 9s;"></div>
        <div class="particle" style="width: 4px; height: 4px; left: 90%; animation-delay: 12s;"></div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <div class="gacha-container">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="top-left">
                <a href="../menu/home.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title">Gacha</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo number_format($resources['energy']); ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🎫</span>
                    <span id="standard-tickets"><?php echo $tickets['standard']; ?></span>
                    <div class="plus-btn" onclick="goToStore('tickets')" title="Buy Tickets">+</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Event Navigation Sidebar -->
            <div class="event-sidebar">
                <div class="event-nav-item active" data-event="hollow-hymnal">
                    <div class="event-label">Event</div>
                    <div class="event-name">Hollow Hymnal</div>
                </div>
                <div class="event-nav-item" data-event="standard">
                    <div class="event-label">Standard</div>
                    <div class="event-name">Character Pool</div>
                </div>
                <div class="event-nav-item" data-event="premium">
                    <div class="event-label">Premium</div>
                    <div class="event-name">Elite Banner</div>
                </div>
            </div>

            <!-- Main Banner Area -->
            <div class="banner-area">
                <!-- Event Banner -->
                <div class="event-banner active" id="hollow-hymnal">
                    <div class="banner-background">
                        <div class="banner-overlay"></div>
                        <div class="character-showcase">
                            <div class="main-character">
                                <img src="../assets/qistina/qistina2.png" alt="Qistina" class="character-img main">
                                <div class="character-name">Qistina</div>
                                <div class="character-rarity">★★★★★</div>
                            </div>
                            <div class="side-characters">
                                <div class="side-character">
                                    <img src="../assets/zulaikha/zulaikha.png" alt="Zulaikha" class="character-img">
                                    <div class="character-name">Zulaikha</div>
                                    <div class="character-rarity">★★★★</div>
                                </div>
                            </div>
                        </div>

                        <div class="banner-info">
                            <div class="banner-title">Hollow Hymnal</div>
                            <div class="banner-subtitle">No sun shall rise, no vow retract—<br>Our souls entwined in endless pact</div>
                            <div class="banner-details">
                                <div class="guarantee-info">
                                    <i class="fas fa-info-circle"></i>
                                    <span>1/80</span>
                                    <span>Guarantee ★ Sinner</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Standard Banner -->
                <div class="event-banner" id="standard">
                    <div class="banner-background standard-bg">
                        <div class="banner-overlay"></div>
                        <div class="character-showcase">
                            <div class="main-character">
                                <img src="../assets/qistina/qistina2.png" alt="Qistina" class="character-img main">
                                <div class="character-name">Standard Pool</div>
                                <div class="character-rarity">★★★★</div>
                            </div>
                        </div>
                        <div class="banner-info">
                            <div class="banner-title">Standard Banner</div>
                            <div class="banner-subtitle">Regular character recruitment</div>
                        </div>
                    </div>
                </div>

                <!-- Premium Banner -->
                <div class="event-banner" id="premium">
                    <div class="banner-background premium-bg">
                        <div class="banner-overlay"></div>
                        <div class="character-showcase">
                            <div class="main-character">
                                <img src="../assets/zulaikha/zulaikha.png" alt="Zulaikha" class="character-img main">
                                <div class="character-name">Elite Pool</div>
                                <div class="character-rarity">★★★★★</div>
                            </div>
                        </div>
                        <div class="banner-info">
                            <div class="banner-title">Elite Banner</div>
                            <div class="banner-subtitle">Premium character recruitment</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pull Controls -->
            <div class="pull-controls">
                <div class="pull-buttons">
                    <button class="pull-btn single" onclick="performPull(1, 1)">
                        <div class="pull-icon">🎫</div>
                        <div class="pull-text">
                            <span>×1</span>
                            <small>Spin</small>
                        </div>
                    </button>
                    <button class="pull-btn multi" onclick="performPull(1, 10)">
                        <div class="pull-icon">🎫</div>
                        <div class="pull-text">
                            <span>×10</span>
                            <small>Spin</small>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Modal -->
    <div id="results-modal" class="results-modal" style="display: none;">
        <div class="results-content">
            <div class="results-header">
                <h2>Gacha Results</h2>
                <p>You obtained the following items:</p>
            </div>
            <div id="results-grid" class="results-grid">
                <!-- Results will be populated here -->
            </div>
            <button class="close-results" onclick="closeResults()">
                <i class="fas fa-check"></i>
                Continue
            </button>
        </div>
    </div>

    <style>
        /* Results Modal Styles */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(10px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .results-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .results-content {
            background: linear-gradient(135deg, var(--card-bg), rgba(30, 35, 50, 0.95));
            border: 2px solid var(--border-glow);
            border-radius: 20px;
            padding: 30px;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .results-modal.show .results-content {
            transform: scale(1);
        }

        .results-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .results-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-light);
            margin-bottom: 10px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .result-item {
            background: linear-gradient(135deg, var(--card-bg), rgba(40, 45, 60, 0.8));
            border: 2px solid var(--border-glow);
            border-radius: 15px;
            padding: 20px 15px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .result-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .result-item:hover {
            transform: scale(1.05) translateY(-5px);
            box-shadow: 0 10px 25px rgba(74, 144, 226, 0.3);
        }

        .result-item:hover::before {
            transform: translateX(100%);
        }

        .result-icon {
            font-size: 40px;
            margin-bottom: 12px;
            display: block;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .result-name {
            font-size: 14px;
            font-weight: 700;
            color: var(--text-light);
            margin-bottom: 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .result-type {
            font-size: 11px;
            color: var(--text-gray);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        /* Rarity-based styling */
        .result-item.rarity-1 {
            border-color: #8e8e93;
            box-shadow: 0 0 15px rgba(142, 142, 147, 0.3);
        }
        .result-item.rarity-2 {
            border-color: #34c759;
            box-shadow: 0 0 15px rgba(52, 199, 89, 0.3);
        }
        .result-item.rarity-3 {
            border-color: #007aff;
            box-shadow: 0 0 15px rgba(0, 122, 255, 0.3);
        }
        .result-item.rarity-4 {
            border-color: #af52de;
            box-shadow: 0 0 15px rgba(175, 82, 222, 0.3);
        }
        .result-item.rarity-5 {
            border-color: #ff9500;
            box-shadow: 0 0 15px rgba(255, 149, 0, 0.3);
        }

        /* Result item animations */
        @keyframes resultAppear {
            0% {
                opacity: 0;
                transform: scale(0.5) translateY(20px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .result-item {
            animation: resultAppear 0.5s ease forwards;
            opacity: 0;
        }

        .close-results {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, var(--primary-color), #357abd);
            color: white;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-results:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
        }
    </style>

    <script>
        let isPulling = false;
        let currentTickets = <?php echo json_encode($tickets); ?>;
        let currentBanner = 'hollow-hymnal';

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Switch between banners
        function switchBanner(bannerType) {
            // Update navigation
            document.querySelectorAll('.event-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-event="${bannerType}"]`).classList.add('active');

            // Update banner display
            document.querySelectorAll('.event-banner').forEach(banner => {
                banner.classList.remove('active');
            });
            document.getElementById(bannerType).classList.add('active');

            currentBanner = bannerType;
        }

        // Update ticket display (override shared function to include updateButtonStates)
        function updateTicketDisplay(ticketType, newCount) {
            const topTicketElement = document.getElementById(`${ticketType}-tickets`);
            if (topTicketElement) {
                topTicketElement.textContent = newCount;
            }
            currentTickets[ticketType] = newCount;
            updateButtonStates();
        }

        // Update button states based on ticket availability
        function updateButtonStates() {
            document.querySelectorAll('.pull-btn').forEach(button => {
                const available = currentTickets['standard'] || 0;
                const cost = button.classList.contains('multi') ? 10 : 1;

                if (available < cost) {
                    button.disabled = true;
                    button.style.opacity = '0.5';
                } else {
                    button.disabled = false;
                    button.style.opacity = '1';
                }
            });
        }

        // Perform gacha pull
        async function performPull(poolId, pullCount) {
            if (isPulling) return;

            const available = currentTickets['standard'] || 0;
            const cost = pullCount;

            if (available < cost) {
                showNotification(`Not enough tickets! Need ${cost}, have ${available}`, 'error');
                return;
            }

            isPulling = true;

            // Show spinning animation
            const buttons = document.querySelectorAll('.pull-btn');
            buttons.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.5';
            });

            // Add spinning effect to character images
            document.querySelectorAll('.character-img').forEach(img => {
                img.style.animation = 'spin 2s linear infinite';
            });

            try {
                const response = await fetch('../api/gacha.php?action=pull', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        pool_id: poolId,
                        pull_count: pullCount
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Update ticket count
                    updateTicketDisplay('standard', result.remaining_tickets);

                    // Show results after animation
                    setTimeout(() => {
                        showResults(result.items);
                        showNotification(`Successfully pulled ${pullCount} item(s)!`, 'success');
                    }, 2000);

                    // Update resources if any resource items were obtained
                    updateUserResources();
                } else {
                    showNotification(result.message || 'Failed to perform pull', 'error');
                }

            } catch (error) {
                console.error('Error performing pull:', error);
                showNotification('Network error occurred', 'error');
            } finally {
                setTimeout(() => {
                    isPulling = false;
                    // Remove spinning animation
                    document.querySelectorAll('.character-img').forEach(img => {
                        img.style.animation = '';
                    });
                    updateButtonStates();
                }, 2000);
            }
        }

        // Add spin animation to CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // Show gacha results
        function showResults(items) {
            const modal = document.getElementById('results-modal');
            const grid = document.getElementById('results-grid');

            grid.innerHTML = '';

            items.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = `result-item rarity-${item.rarity || 1}`;

                let icon = '🎁';
                let displayName = item.item_name || 'Unknown Item';

                switch(item.item_type) {
                    case 'character':
                        icon = '👤';
                        break;
                    case 'weapon':
                        icon = '⚔️';
                        break;
                    case 'item':
                        icon = '📦';
                        break;
                    case 'resource':
                        try {
                            const rewardData = JSON.parse(item.reward_data || '{}');
                            if (rewardData.type === 'coins') {
                                icon = '🪙';
                                displayName = `${rewardData.amount || 0} Coins`;
                            } else if (rewardData.type === 'diamonds') {
                                icon = '💎';
                                displayName = `${rewardData.amount || 0} Diamonds`;
                            } else if (rewardData.type === 'energy') {
                                icon = '⚡';
                                displayName = `${rewardData.amount || 0} Energy`;
                            }
                        } catch (e) {
                            console.error('Error parsing reward data:', e);
                        }
                        break;
                }

                itemElement.innerHTML = `
                    <span class="result-icon">${icon}</span>
                    <div class="result-name">${displayName}</div>
                    <div class="result-type">${item.item_type || 'item'}</div>
                `;

                // Add staggered animation
                itemElement.style.animationDelay = `${index * 0.1}s`;

                grid.appendChild(itemElement);
            });

            // Show modal with animation
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        // Close results modal
        function closeResults() {
            const modal = document.getElementById('results-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // Update user resources
        async function updateUserResources() {
            try {
                const response = await fetch('../api/gacha.php?action=get_user_resources');
                const result = await response.json();

                if (result.success) {
                    updateResourceDisplay(result.resources);
                }
            } catch (error) {
                console.error('Error updating resources:', error);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateButtonStates();

            // Event navigation
            document.querySelectorAll('.event-nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    const eventType = this.dataset.event;
                    switchBanner(eventType);
                });
            });

            // Close modal when clicking outside
            const resultsModal = document.getElementById('results-modal');
            if (resultsModal) {
                resultsModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeResults();
                    }
                });

                // Close modal with Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && resultsModal.classList.contains('show')) {
                        closeResults();
                    }
                });
            }
        });
    </script>

    <!-- Include shared functions -->
    <script src="shared-functions.js"></script>
</body>
</html>
