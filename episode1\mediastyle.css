/* Media Responsive CSS for Style1 Dialog Box & Regular Dialog Box */
/* Fokus pada responsive design untuk mobile phones */
/* ALL MONOLOGUE REFERENCES RENAMED TO STYLE1 */

/* Desktop/Tablet - Default styles already defined in main CSS */

/* ===== REGULAR DIALOG BOX RESPONSIVE ===== */

/* Large Tablets (768px - 1024px) */
@media screen and (max-width: 1024px) {
  #dialog-box {
    min-height: 80px;
    width: 85%;
    padding: 2px 0;
  }

  #dialog-text {
    font-size: 0.9rem;
    line-height: 1.2;
    padding: 0 8px;
  }

  #character-name {
    font-size: 0.8rem;
    padding: 2px 8px;
  }
}

/* Small Tablets (600px - 768px) */
@media screen and (max-width: 768px) {
  #dialog-box {
    min-height: 70px;
    width: 80%;
    padding: 2px 0;
  }

  #dialog-text {
    font-size: 0.8rem;
    line-height: 1.15;
    padding: 0 6px;
  }

  #character-name {
    font-size: 0.7rem;
    padding: 2px 6px;
  }
}

/* Large Mobile Phones (480px - 600px) */
@media screen and (max-width: 600px) {
  #dialog-box {
    min-height: 60px;
    width: 75%;
    padding: 1px 0;
    bottom: 8px;
  }

  #dialog-text {
    font-size: 0.7rem;
    line-height: 1.1;
    padding: 0 5px;
  }

  #character-name {
    font-size: 0.6rem;
    padding: 1px 5px;
  }

  #next-indicator {
    font-size: 0.8rem;
    bottom: 3px;
    right: 8px;
  }
}

/* Standard Mobile Phones (375px - 480px) */
@media screen and (max-width: 480px) {
  #dialog-box {
    min-height: 50px;
    width: 70%;
    padding: 1px 0;
    bottom: 5px;
  }

  #dialog-text {
    font-size: 0.6rem;
    line-height: 1.05;
    padding: 0 4px;
  }

  #character-name {
    font-size: 0.5rem;
    padding: 1px 4px;
  }

  #next-indicator {
    font-size: 0.7rem;
    bottom: 2px;
    right: 6px;
  }
}

/* Small Mobile Phones (320px - 375px) */
@media screen and (max-width: 375px) {
  #dialog-box {
    min-height: 45px;
    width: 65%;
    padding: 1px 0;
    bottom: 3px;
  }

  #dialog-text {
    font-size: 0.55rem;
    line-height: 1.0;
    padding: 0 3px;
  }

  #character-name {
    font-size: 0.45rem;
    padding: 1px 3px;
  }

  #next-indicator {
    font-size: 0.6rem;
    bottom: 1px;
    right: 5px;
  }
}

/* Very Small Mobile Phones (280px - 320px) */
@media screen and (max-width: 320px) {
  #dialog-box {
    min-height: 40px;
    width: 60%;
    padding: 0;
    bottom: 2px;
  }

  #dialog-text {
    font-size: 0.5rem;
    line-height: 0.95;
    padding: 0 2px;
  }

  #character-name {
    font-size: 0.4rem;
    padding: 0 2px;
  }

  #next-indicator {
    font-size: 0.5rem;
    bottom: 1px;
    right: 3px;
  }
}

/* ===== STYLE1 DIALOG BOX RESPONSIVE ===== */

/* Large Tablets (768px - 1024px) */
@media screen and (max-width: 1024px) {
  #style1-box {
    width: 90%;
    height: 90px;
    padding: 0 15px;
  }

  #style1-text {
    font-size: 1.3rem;
    max-width: 80%;
    line-height: 1.4;
  }

  #style1-next-indicator {
    font-size: 1.3rem;
    bottom: 10px;
    right: 20px;
  }
}

/* Small Tablets (600px - 768px) */
@media screen and (max-width: 768px) {
  #style1-box {
    width: 85%;
    height: 80px;
    padding: 0 12px;
  }

  #monologue-text {
    font-size: 1.2rem;
    max-width: 85%;
    line-height: 1.3;
  }

  #monologue-next-indicator {
    font-size: 1.2rem;
    bottom: 8px;
    right: 15px;
  }
}

/* Large Mobile Phones (480px - 600px) */
@media screen and (max-width: 600px) {
  #monologue-box {
    width: 90%;
    height: 70px;
    padding: 0 10px;
    border-radius: 0;
  }

  #monologue-text {
    font-size: 1.0rem;
    max-width: 88%;
    line-height: 1.25;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  #monologue-next-indicator {
    font-size: 1.0rem;
    bottom: 6px;
    right: 12px;
  }
}

/* Standard Mobile Phones (375px - 480px) */
@media screen and (max-width: 480px) {
  #monologue-box {
    width: 95%;
    height: 60px;
    padding: 0 8px;
    background: rgba(0, 0, 0, 0.9);
  }

  #monologue-text {
    font-size: 0.9rem;
    max-width: 90%;
    line-height: 1.2;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
  }

  #monologue-next-indicator {
    font-size: 0.9rem;
    bottom: 4px;
    right: 10px;
  }
}

/* Small Mobile Phones (320px - 375px) */
@media screen and (max-width: 375px) {
  #monologue-box {
    width: 100%;
    height: 50px;
    padding: 0 6px;
    background: rgba(0, 0, 0, 0.92);
  }

  #monologue-text {
    font-size: 0.8rem;
    max-width: 92%;
    line-height: 1.15;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
  }

  #monologue-next-indicator {
    font-size: 0.8rem;
    bottom: 3px;
    right: 8px;
  }
}

/* Very Small Mobile Phones (280px - 320px) */
@media screen and (max-width: 320px) {
  #monologue-box {
    width: 100%;
    height: 45px;
    padding: 0 4px;
    background: rgba(0, 0, 0, 0.95);
  }

  #monologue-text {
    font-size: 0.7rem;
    max-width: 94%;
    line-height: 1.1;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
  }

  #monologue-next-indicator {
    font-size: 0.7rem;
    bottom: 2px;
    right: 6px;
  }
}

/* ===== ORIENTATION SPECIFIC ADJUSTMENTS ===== */

/* Portrait Orientation - Regular Dialog Box */
@media screen and (orientation: portrait) and (max-width: 600px) {
  #dialog-box {
    min-height: 50px;
    bottom: 5px;
    width: 70%;
  }

  #monologue-box {
    top: 45%;
    height: 55px;
  }
}

/* Landscape Orientation - Regular Dialog Box */
@media screen and (orientation: landscape) and (max-height: 500px) {
  #dialog-box {
    min-height: 35px;
    bottom: 2px;
    width: 65%;
  }

  #dialog-text {
    font-size: 0.5rem;
    line-height: 0.95;
  }

  #character-name {
    font-size: 0.4rem;
    padding: 0 3px;
  }

  #monologue-box {
    height: 40px;
    top: 50%;
  }

  #monologue-text {
    font-size: 0.75rem;
    line-height: 1.05;
  }

  #monologue-next-indicator {
    font-size: 0.75rem;
    bottom: 2px;
  }
}

/* ===== DEVICE SPECIFIC ADJUSTMENTS ===== */

/* iPhone SE and similar small screens */
@media screen and (max-width: 320px) and (max-height: 568px) {
  #dialog-box {
    min-height: 90px;
    bottom: 3px;
  }

  #dialog-text {
    font-size: 0.8rem;
    line-height: 1.1;
  }

  #character-name {
    font-size: 0.7rem;
  }

  #monologue-box {
    height: 60px;
    padding: 0 5px;
  }

  #monologue-text {
    font-size: 0.85rem;
    line-height: 1.1;
  }
}

/* iPhone 12/13/14 Pro Max and similar large phones */
@media screen and (min-width: 414px) and (max-width: 480px) {
  #dialog-box {
    min-height: 150px;
    bottom: 12px;
  }

  #dialog-text {
    font-size: 1.05rem;
    line-height: 1.3;
  }

  #character-name {
    font-size: 0.95rem;
  }

  #monologue-box {
    height: 85px;
    padding: 0 12px;
  }

  #monologue-text {
    font-size: 1.15rem;
    line-height: 1.3;
  }
}

/* Samsung Galaxy and similar Android phones */
@media screen and (min-width: 360px) and (max-width: 414px) {
  #dialog-box {
    min-height: 130px;
    bottom: 8px;
  }

  #dialog-text {
    font-size: 0.95rem;
    line-height: 1.25;
  }

  #character-name {
    font-size: 0.85rem;
  }

  #monologue-box {
    height: 75px;
    padding: 0 10px;
  }

  #monologue-text {
    font-size: 1.05rem;
    line-height: 1.25;
  }
}

/* ===== DISPLAY & ACCESSIBILITY OPTIMIZATIONS ===== */

/* High DPI/Retina Display Adjustments */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
  #dialog-text, #character-name {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #monologue-text {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  #dialog-box {
    cursor: default;
    -webkit-tap-highlight-color: transparent;
  }

  #dialog-box:active {
    transform: translateX(-50%) scale(0.98);
  }

  #monologue-box {
    cursor: default;
    -webkit-tap-highlight-color: transparent;
  }

  #monologue-box:active {
    background: rgba(0, 0, 0, 0.95);
    transform: translate(-50%, -50%) scale(0.98);
  }
}

/* Accessibility - Reduce Motion */
@media (prefers-reduced-motion: reduce) {
  #dialog-box {
    transition: none;
  }

  #monologue-box {
    transition: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  #dialog-box {
    background: linear-gradient(to bottom, rgba(15, 15, 25, 0.95), rgba(25, 25, 35, 0.98));
    border-color: rgba(100, 100, 120, 0.5);
  }

  #dialog-text {
    color: #e0e0e0;
  }

  #character-name {
    color: #f0f0f0;
    background: rgba(30, 30, 40, 0.8);
  }

  #monologue-box {
    background: rgba(0, 0, 0, 0.95);
  }

  #monologue-text {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9);
  }
}
