<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include resource manager and mission manager
require_once '../includes/resource_manager.php';
require_once '../includes/mission_manager.php';

// Get username and user ID
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Get user missions
$missions = MissionManager::getUserMissions($user_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Missions</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="mission.css">
</head>
<body>
    <!-- Animated Gaming Background -->
    <div class="gaming-background">
        <!-- Floating Particles -->
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>

        <!-- Neon Orbs -->
        <div class="neon-orb"></div>
        <div class="neon-orb"></div>
        <div class="neon-orb"></div>
    </div>

    <div class="mission-container">
        <!-- Top navigation bar -->
        <div class="top-nav">
            <div class="top-left">
                <a href="../menu/home.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title">Operation Summary</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main content area -->
        <div class="mission-content">
            <!-- Mission header -->
            <div class="mission-header">
                <div class="operation-summary">
                    <div class="operation-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="operation-title">Operation Summary</div>
                </div>
                <button class="claim-all-button" id="claim-all-btn" onclick="claimAllRewards()">
                    <i class="fas fa-gift"></i> Claim All
                </button>
            </div>

            <!-- Mission list -->
            <div class="mission-list" id="mission-list">
                <?php if (empty($missions)): ?>
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading missions...</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($missions as $index => $mission): ?>
                        <?php
                            $progress_percentage = $mission['target_value'] > 0 ?
                                min(100, ($mission['current_progress'] / $mission['target_value']) * 100) : 0;
                            $is_completed = $mission['is_completed'];
                            $is_claimed = $mission['is_claimed'];
                        ?>
                        <div class="mission-item <?php echo $is_completed ? 'completed' : ''; ?> <?php echo $is_claimed ? 'claimed' : ''; ?>"
                             data-mission-id="<?php echo $mission['id']; ?>">
                            <div class="mission-number">
                                <?php echo str_pad($index + 1, 2, '0', STR_PAD_LEFT); ?>
                            </div>
                            <div class="mission-details">
                                <div class="mission-title"><?php echo htmlspecialchars($mission['title']); ?></div>
                                <div class="mission-description"><?php echo htmlspecialchars($mission['description']); ?></div>
                                <div class="mission-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo $progress_percentage; ?>%"></div>
                                    </div>
                                    <div class="progress-text">
                                        (<?php echo $mission['current_progress']; ?>/<?php echo $mission['target_value']; ?>)
                                    </div>
                                </div>
                            </div>
                            <div class="mission-rewards">
                                <?php if ($mission['reward_coins'] > 0): ?>
                                    <div class="reward-item">
                                        <span class="reward-icon">🪙</span>
                                        <span class="reward-amount"><?php echo number_format($mission['reward_coins']); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($mission['reward_diamonds'] > 0): ?>
                                    <div class="reward-item">
                                        <span class="reward-icon">💎</span>
                                        <span class="reward-amount"><?php echo number_format($mission['reward_diamonds']); ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($mission['reward_energy'] > 0): ?>
                                    <div class="reward-item">
                                        <span class="reward-icon">⚡</span>
                                        <span class="reward-amount"><?php echo $mission['reward_energy']; ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <button class="claim-button <?php echo $is_claimed ? 'claimed' : ''; ?>"
                                    <?php echo (!$is_completed || $is_claimed) ? 'disabled' : ''; ?>
                                    onclick="claimReward(<?php echo $mission['id']; ?>)">
                                <?php if ($is_claimed): ?>
                                    <i class="fas fa-check"></i> Claimed
                                <?php elseif ($is_completed): ?>
                                    <i class="fas fa-gift"></i> Claim
                                <?php else: ?>
                                    <i class="fas fa-lock"></i> Go
                                <?php endif; ?>
                            </button>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <script>
        // Global variables
        let isClaimingAll = false;

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }



        // Claim single mission reward
        async function claimReward(missionId) {
            const button = document.querySelector(`[data-mission-id="${missionId}"] .claim-button`);
            const originalText = button.innerHTML;

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Claiming...';

            try {
                const response = await fetch('../api/missions.php?action=claim', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        mission_id: missionId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Update button state
                    button.innerHTML = '<i class="fas fa-check"></i> Claimed';
                    button.classList.add('claimed');

                    // Update mission item appearance
                    const missionItem = document.querySelector(`[data-mission-id="${missionId}"]`);
                    missionItem.classList.add('claimed');

                    // Show success notification
                    let rewardText = 'Reward claimed!';
                    if (result.rewards) {
                        const rewards = [];
                        if (result.rewards.coins) rewards.push(`${result.rewards.coins.toLocaleString()} coins`);
                        if (result.rewards.diamonds) rewards.push(`${result.rewards.diamonds.toLocaleString()} diamonds`);
                        if (result.rewards.energy) rewards.push(`${result.rewards.energy} energy`);
                        if (rewards.length > 0) {
                            rewardText = `Claimed: ${rewards.join(', ')}`;
                        }
                    }
                    showNotification(rewardText, 'success');

                    // Update resource display
                    if (result.rewards) {
                        const currentResources = {
                            coins: parseInt(document.getElementById('coins-count').textContent.replace(/,/g, '')),
                            diamonds: parseInt(document.getElementById('diamonds-count').textContent.replace(/,/g, '')),
                            energy: parseInt(document.getElementById('energy-count').textContent)
                        };

                        if (result.rewards.coins) currentResources.coins += result.rewards.coins;
                        if (result.rewards.diamonds) currentResources.diamonds += result.rewards.diamonds;
                        if (result.rewards.energy) currentResources.energy += result.rewards.energy;

                        updateResourceDisplay(currentResources);
                    }

                    // Update claim all button state
                    updateClaimAllButton();

                } else {
                    showNotification(result.message || 'Failed to claim reward', 'error');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }

            } catch (error) {
                console.error('Error claiming reward:', error);
                showNotification('Network error occurred', 'error');
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }

        // Claim all available rewards
        async function claimAllRewards() {
            if (isClaimingAll) return;

            const button = document.getElementById('claim-all-btn');
            const originalText = button.innerHTML;

            isClaimingAll = true;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Claiming All...';

            try {
                const response = await fetch('../api/missions.php?action=claim_all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });

                const result = await response.json();

                if (result.success && result.claimed_count > 0) {
                    // Update all claimed missions
                    const completedMissions = document.querySelectorAll('.mission-item.completed:not(.claimed)');
                    completedMissions.forEach(mission => {
                        const claimButton = mission.querySelector('.claim-button');
                        claimButton.innerHTML = '<i class="fas fa-check"></i> Claimed';
                        claimButton.classList.add('claimed');
                        claimButton.disabled = true;
                        mission.classList.add('claimed');
                    });

                    // Show success notification
                    let rewardText = `Claimed ${result.claimed_count} mission(s)!`;
                    if (result.total_rewards) {
                        const rewards = [];
                        if (result.total_rewards.coins > 0) rewards.push(`${result.total_rewards.coins.toLocaleString()} coins`);
                        if (result.total_rewards.diamonds > 0) rewards.push(`${result.total_rewards.diamonds.toLocaleString()} diamonds`);
                        if (result.total_rewards.energy > 0) rewards.push(`${result.total_rewards.energy} energy`);
                        if (rewards.length > 0) {
                            rewardText += ` Total rewards: ${rewards.join(', ')}`;
                        }
                    }
                    showNotification(rewardText, 'success');

                    // Update resource display
                    if (result.total_rewards) {
                        const currentResources = {
                            coins: parseInt(document.getElementById('coins-count').textContent.replace(/,/g, '')),
                            diamonds: parseInt(document.getElementById('diamonds-count').textContent.replace(/,/g, '')),
                            energy: parseInt(document.getElementById('energy-count').textContent)
                        };

                        currentResources.coins += result.total_rewards.coins || 0;
                        currentResources.diamonds += result.total_rewards.diamonds || 0;
                        currentResources.energy += result.total_rewards.energy || 0;

                        updateResourceDisplay(currentResources);
                    }

                } else {
                    showNotification(result.message || 'No rewards to claim', 'error');
                }

            } catch (error) {
                console.error('Error claiming all rewards:', error);
                showNotification('Network error occurred', 'error');
            } finally {
                isClaimingAll = false;
                updateClaimAllButton();
            }
        }

        // Update claim all button state
        function updateClaimAllButton() {
            const button = document.getElementById('claim-all-btn');
            const claimableCount = document.querySelectorAll('.mission-item.completed:not(.claimed)').length;

            if (claimableCount > 0) {
                button.disabled = false;
                button.innerHTML = `<i class="fas fa-gift"></i> Claim All (${claimableCount})`;
            } else {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-gift"></i> Claim All';
            }
        }

        // Interactive Background Effects
        function createInteractiveParticles() {
            const background = document.querySelector('.gaming-background');

            // Mouse move effect
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // Move orbs slightly based on mouse position
                const orbs = document.querySelectorAll('.neon-orb');
                orbs.forEach((orb, index) => {
                    const speed = (index + 1) * 0.5;
                    const x = mouseX * speed;
                    const y = mouseY * speed;
                    orb.style.transform = `translate(${x}px, ${y}px)`;
                });
            });

            // Click effect - create temporary burst
            document.addEventListener('click', function(e) {
                createClickBurst(e.clientX, e.clientY);
            });
        }

        function createClickBurst(x, y) {
            const burst = document.createElement('div');
            burst.style.position = 'fixed';
            burst.style.left = x + 'px';
            burst.style.top = y + 'px';
            burst.style.width = '20px';
            burst.style.height = '20px';
            burst.style.background = 'radial-gradient(circle, rgba(255, 20, 147, 0.8) 0%, transparent 70%)';
            burst.style.borderRadius = '50%';
            burst.style.pointerEvents = 'none';
            burst.style.zIndex = '1000';
            burst.style.animation = 'burstEffect 0.6s ease-out forwards';

            document.body.appendChild(burst);

            setTimeout(() => {
                document.body.removeChild(burst);
            }, 600);
        }

        // Add dynamic particles
        function addDynamicParticles() {
            const background = document.querySelector('.gaming-background');

            setInterval(() => {
                if (document.querySelectorAll('.dynamic-particle').length < 15) {
                    const particle = document.createElement('div');
                    particle.className = 'dynamic-particle';
                    particle.style.position = 'absolute';
                    particle.style.width = Math.random() * 4 + 2 + 'px';
                    particle.style.height = particle.style.width;
                    particle.style.background = `rgba(${Math.random() * 255}, ${Math.random() * 255}, 255, 0.6)`;
                    particle.style.borderRadius = '50%';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = '100%';
                    particle.style.pointerEvents = 'none';
                    particle.style.animation = 'floatUp 8s linear forwards';

                    background.appendChild(particle);

                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 8000);
                }
            }, 2000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize interactive background
            createInteractiveParticles();
            addDynamicParticles();

            updateClaimAllButton();

            // Add smooth scrolling for better UX
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>

    <!-- Include shared functions -->
    <script src="shared-functions.js"></script>
</body>
</html>
