// DOM Elements
const dialogBox = document.getElementById('dialog-box');
const characterName = document.getElementById('character-name');
const dialogText = document.getElementById('dialog-text');
const choices = document.getElementById('choices');
const nextIndicator = document.getElementById('next-indicator');
const startButton = document.getElementById('start-button');
const titleScreen = document.getElementById('title-screen');
const character = document.getElementById('character');
const controlsMenu = document.getElementById('controls-menu');
// Style1 elements
const style1Box = document.getElementById('style1-box');
const style1Text = document.getElementById('style1-text');
const style1NextIndicator = document.getElementById('style1-next-indicator');

// Game state variables
let currentDialogIndex = 0;
let isTyping = false;
let typingSpeed = 50; // milliseconds per character
let typingTimeout = null;
let choicesMade = {
  firstChoice: null,
  secondChoice: null
};
let isTransitioning = false; // New variable to prevent multiple clicks
let dialogHistory = []; // Array to store dialog history

// Story script - Episode 1: "Fenomena Pecahan Ruang"
const story = [
  { speaker: "", text: "Hari Isnin. Pagi macam biasa.", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "", text: "Cikgu Fizik depan tu entah dah berapa lama terang pasal tekanan gelombang.", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "", text: "Tapi aku? Fikiran aku masih stuck dekat suara ASMR malam tadi.", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "", text: "Suara dia lembut gila... macam peluk telinga. Tak sedar, aku senyum sendiri.", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "Cikgu", text: "Salman! Jawab soalan ni. Kalau tekanan naik, apa jadi pada amplitud gelombang?", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "Salman", text: "Err... bertambah?", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "Cikgu", text: "Betul. Tapi tolong jangan termenung sangat.", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "Salman", text: "Nasib baik. Tapi memang habit aku. Angan-angan.", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "", text: "*SIREN BERBUNYI KERAS*", backgroundImage: "../assets/backgrounds/kelas.png" },
  { speaker: "Siren", text: "PERHATIAN! Fenomena Pecahan Ruang dikesan! Semua pelajar sila berpindah ke tempat perlindungan!", backgroundImage: "../assets/backgrounds/koridor.png" },
  { speaker: "Salman", text: "Hah? Lagi?", backgroundImage: "../assets/backgrounds/koridor.png" },
  { speaker: "Salman", text: "Kejadian ni makin kerap. Langit tiba-tiba pecah. Masa terganggu.", backgroundImage: "../assets/backgrounds/koridor.png" },
  { speaker: "Salman", text: "Fenomena Pecahan Ruang — orang panggil spacequake. Tapi tak pernah ada orang tahu puncanya.", backgroundImage: "../assets/backgrounds/koridor.png" },
  { speaker: "", text: "Semua pelajar lari keluar. Aku ikut… tapi laluan utama sesak. Aku ambil lorong belakang. Lagi cepat. Tapi...", backgroundImage: "../assets/backgrounds/lorong.png" },
  { type: "Style1", speaker: "Salman", text: "*BOOOOOOMMM*", backgroundImage: "../assets/backgrounds/explosion.png" },
  { speaker: "", text: "Letupan berlaku betul-betul depan mata. Debu naik. Tanah retak. Langit… terbuka. Macam kaca yang hancur.", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "", text: "Dan dari dalam retakan tu… dia turun perlahan. Gadis bertudung hitam, blouse gelap, mata merah samar. Dia tenang. Tapi ada sesuatu dalam renungan dia…", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "Zulaikha", text: "…Dunia ni… masih belum musnah rupanya…", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "Salman", text: "Siapa dia ni...? Dia manusia ke…? Atau bukan?", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "Salman", text: "Awak siapa?", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "Zulaikha", text: "Orang yang tak sepatutnya wujud di sini.", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "Salman", text: "Aura dia… berat. Tapi dia nampak sunyi. Kenapa aku rasa macam dia… perlukan aku?", backgroundImage: "../assets/backgrounds/spacequake.png" },
  { speaker: "", text: "*TEMBAKAN LASER*", backgroundImage: "../assets/backgrounds/battle.png" },
  { speaker: "Pasukan AST", text: "Target terkunci. FIRE!", backgroundImage: "../assets/backgrounds/battle.png" },
  { speaker: "", text: "Laser menyambar ke arah dia. Dia cuma pandang. Tak bergerak. Aku gerak sendiri. Jerit.", backgroundImage: "../assets/backgrounds/battle.png" },
  { speaker: "Salman", text: "BERHENTI!!", backgroundImage: "../assets/backgrounds/battle.png" },
  { speaker: "Salman", text: "Aku tak tahu kenapa. Tapi aku tahu satu benda — Dia tak patut dibunuh.", backgroundImage: "../assets/backgrounds/battle.png" },
  { speaker: "", text: "Letupan besar. Cahaya putih. Segalanya lenyap.", backgroundImage: "../assets/backgrounds/whiteout.png" },
  { speaker: "", text: "...", backgroundImage: "../assets/backgrounds/hospital.png" },
  { speaker: "Qistina", text: "Salman… kau okay?", backgroundImage: "../assets/backgrounds/hospital.png" },
  { speaker: "", text: "Aku buka mata. Bilik putih. Bunyi mesin. Depan aku — Qistina. Tudung peach. Uniform pelik.", backgroundImage: "../assets/backgrounds/hospital.png" },
  { speaker: "Salman", text: "Qistina…? Apa semua ni?", backgroundImage: "../assets/backgrounds/hospital.png" },
  { speaker: "Qistina", text: "Selamat datang ke Ratatoskr. Kau dah terlibat.", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Salman", text: "Aku... apa?", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Qistina", text: "Gadis tadi, Zulaikha — dia adalah Spirit. Makhluk dari dimensi emosi. Bila emosi dia tak stabil, dunia ini hancur.", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Salman", text: "Jadi nak buat apa? Tembak dia?", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Qistina", text: "Tak. Kau kena buat dia jatuh cinta dengan kau.", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Salman", text: "APA?!", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Salman", text: "Ni… apa jenis misi ni? Cinta… untuk selamatkan dunia?", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Qistina", text: "Kalau dia cintakan kau, kuasanya boleh disegel. Dunia akan selamat. Dan hanya kau… yang mampu buat begitu.", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Salman", text: "...Kalau itu satu-satunya cara… Aku akan buat.", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "Salman", text: "Tak kira berapa ramai Spirit yang akan datang… Aku akan buat mereka semua jatuh cinta.", backgroundImage: "../assets/backgrounds/ratatoskr.png" },
  { speaker: "", text: "💕 TAMAT EPISOD 1", showMissionComplete: true }
];

// Choice options
const choiceOptions = [
  // First choice options
  [
    { text: "Hai… maaf kalau kacau. Awak selalu jogging sini ke?", nextIndex: 10 },
    { text: "Selamat pagi. Saya perhatikan awak selalu jogging sini?", nextIndex: 3 },
    { text: "Maaf... saya tertarik dengan aura awak. Awak selalu ke daatang sini?", nextIndex: 3 }
  ],
  // Second choice options
  [
    { text: "Awak ada IG tak…? Maksud saya… boleh saya kenal awak lebih?", nextIndex: 10 },
    { text: "Eh tak… saya maksudkan, aura awak tu… tenang. Macam... motivasi.", nextIndex: 10 },
    { text: "Saya jenis simpan gambar dalam hati, bukan dalam phone.", nextIndex: 10 }
  ]
];

// Initialize the game
function initGame() {
  // Wait for DOM to be fully loaded
  if (!dialogBox || !characterName || !dialogText || !choices ||
      !nextIndicator || !startButton || !titleScreen ||
      !character || !controlsMenu || !style1Box ||
      !style1Text || !style1NextIndicator) {
    setTimeout(initGame, 100);
    return;
  }

  // Hide dialog box and style1 box initially
  dialogBox.style.display = 'none';
  style1Box.style.display = 'none';

  // Set up event listeners
  startButton.addEventListener('click', startGame);
  dialogBox.addEventListener('click', advanceDialog);
  style1Box.addEventListener('click', advanceDialog);
  
  // Set up choice buttons
  const choiceButtons = document.querySelectorAll('.choice-btn');
  
  choiceButtons.forEach(button => {
    button.addEventListener('click', handleChoice);
  });
  
  // Set up control menu buttons
  const menuButtons = document.querySelectorAll('.menu-btn');
  menuButtons.forEach((button) => {
    if (button.classList.contains('auto-btn')) { // Auto button
      button.addEventListener('click', toggleAutoPlay);
    } else if (button.classList.contains('log-btn')) { // Log button
      button.addEventListener('click', showDialogLog);
    }
  });
}

// Start the game
function startGame() {
  titleScreen.style.display = 'none';
  dialogBox.style.display = 'flex';
  controlsMenu.style.display = 'flex';
  currentDialogIndex = 0;
  showDialog(currentDialogIndex);
}

// Show dialog
function showDialog(index) {
  if (index >= story.length) {
    return;
  }
  
  const dialog = story[index];
  
  // Reset UI elements
  isTyping = true;
  nextIndicator.style.display = 'none';
  dialogText.textContent = ''; // Clear text before starting new dialog
  
  // Handle choices
  if (dialog.type === 'choice') {
    // Hide regular dialog elements

    // Show choices container with important flag
    choices.style.cssText = 'display: flex !important; opacity: 1 !important; z-index: 9999 !important;';

    // Determine which choice set to show (first or second choice)
    const choiceSet = index === 9 ? 0 : 1;

    // Get all choice buttons
    const choiceButtons = document.querySelectorAll('.choice-btn');

    // Update each button
    choiceButtons.forEach((button, i) => {
      if (i < choiceOptions[choiceSet].length) {
        // Set button text and data
        button.textContent = choiceOptions[choiceSet][i].text;
        button.dataset.choice = i + 1;
        button.dataset.nextIndex = choiceOptions[choiceSet][i].nextIndex;

        // Force button to be visible
        button.style.cssText = 'display: block !important; opacity: 1 !important;';
      } else {
        button.style.display = 'none';
      }
    });

    isTyping = false;
  } else if (dialog.type === 'Style1') {
    // Handle Style1 - internal thoughts using center positioned box
    dialogBox.style.display = 'none'; // Hide regular dialog box
    style1Box.style.display = 'flex'; // Show style1 box
    choices.style.display = 'none';

    // Reset style1 indicators
    style1NextIndicator.style.display = 'none';
    style1Text.textContent = ''; // Clear text before starting new style1

    // Type out style1 text
    let i = 0;
    const text = dialog.text || "";

    // Add to dialog history with Style1 marker
    if (text && !dialogHistory.some(entry => entry.speaker === dialog.speaker && entry.text === text)) {
      dialogHistory.push({
        speaker: dialog.speaker + " (Style1)",
        text: text
      });
    }

    // Clear any existing typing timeouts
    if (typingTimeout) {
      clearTimeout(typingTimeout);
      typingTimeout = null;
    }

    // Reset style1 text before starting to type
    style1Text.textContent = '';

    const typeWriter = () => {
      if (i < text.length) {
        style1Text.textContent += text.charAt(i);
        i++;
        typingTimeout = setTimeout(typeWriter, typingSpeed);
      } else {
        isTyping = false;
        typingTimeout = null;
        style1NextIndicator.style.display = 'block';
      }
    };

    typeWriter();
  } else {
    // Regular dialog
    dialogBox.style.display = 'flex';
    style1Box.style.display = 'none'; // Hide style1 box for regular dialog
    choices.style.display = 'none';
    
    // Get all character elements
    const mainCharacter = character;
    const characterLeft = document.getElementById('character-left');
    const characterRight = document.getElementById('character-right');
    
    // Set all characters to dimmed state first (visible but dimmed)
    if (mainCharacter) {
      mainCharacter.style.filter = 'brightness(0.3)'; // Make darker
      mainCharacter.style.opacity = '0.7'; // Dimmed opacity
      mainCharacter.style.display = 'block';
    }
    
    if (characterLeft) {
      characterLeft.style.filter = 'brightness(0.3)'; // Make darker
      characterLeft.style.opacity = '0.7'; // Dimmed opacity
      characterLeft.style.display = 'block';
    }
    
    if (characterRight) {
      characterRight.style.filter = 'brightness(0.3)'; // Make darker
      characterRight.style.opacity = '0.7'; // Dimmed opacity
      characterRight.style.display = 'block';
    }
    
    // Highlight the speaking character with full opacity
    if (dialog.speaker === "Qistina") {
      mainCharacter.src = "../assets/qistina/qistina3.png";
      mainCharacter.style.opacity = '1'; // Full opacity for speaking character
      mainCharacter.style.filter = 'brightness(1)'; // Normal brightness
    } else if (dialog.speaker === "Zulaikha") {
      if (characterLeft) {
        characterLeft.src = "../assets/Zulaikha/zulaikha1.png";
        characterLeft.style.opacity = '1'; // Full opacity for speaking character
        characterLeft.style.filter = 'brightness(1)'; // Normal brightness
      }
    } else if (dialog.speaker === "Fifi") {
      if (characterRight) {
        characterRight.src = "../assets/Fifii/fifii1.png";
        characterRight.style.opacity = '1'; // Full opacity for speaking character
        characterRight.style.filter = 'brightness(1)'; // Normal brightness
      }
    } else if (dialog.speaker === "Salman") {
      // Optionally show Salman if you have his image
    } else if (dialog.speaker === "") {
      // For narration, keep all characters dimmed
    }
    
    // Override with specific character settings if provided
    if (dialog.showCharacter) {
      mainCharacter.style.display = 'block';
      mainCharacter.style.opacity = '1'; // Full opacity
    }
    
    if (dialog.showCharacterRight !== undefined) {
      if (characterRight) {
        characterRight.style.display = dialog.showCharacterRight ? 'block' : 'none';
        if (dialog.showCharacterRight) {
          characterRight.style.opacity = '1'; // Full opacity
        }
      }
    }
    
    if (dialog.showCharacterLeft) {
      if (characterLeft) {
        characterLeft.style.display = 'block';
        characterLeft.style.opacity = '1'; // Full opacity
      }
    }
    
    // Change background image if specified
    if (dialog.backgroundImage) {
      document.getElementById('background').style.backgroundImage = `url('${dialog.backgroundImage}')`;
    }
    
    // Change character image if specified
    if (dialog.characterImage) {
      mainCharacter.src = dialog.characterImage;
      mainCharacter.style.display = 'block';
      mainCharacter.style.opacity = '1'; // Full opacity
    }
    
    // Set speaker name and style dialog text based on speaker
    const characterNameContainer = document.getElementById('character-name-container');
    
    if (dialog.speaker === "") {
      // For narration, hide character name and make text blurry
      characterNameContainer.style.display = 'none';
      dialogText.style.fontStyle = 'italic';
    } else {
      // For character dialog, show name and normal text
      characterNameContainer.style.display = 'block';
      characterName.textContent = dialog.speaker;
      dialogText.style.fontStyle = 'normal'; // Reset font style for regular dialog
    }
    
    // Show mission complete if specified
    if (dialog.showMissionComplete) {
      dialogBox.style.display = 'none';
      showMissionComplete();
      isTyping = false;
      return;
    }
    
    // Type out text
    let i = 0;
    const text = dialog.text || "";
    
    // Add to dialog history
    if (text && !dialogHistory.some(entry => entry.speaker === dialog.speaker && entry.text === text)) {
      dialogHistory.push({
        speaker: dialog.speaker || "",
        text: text
      });
    }
    
    // Clear any existing typing timeouts
    if (typingTimeout) {
      clearTimeout(typingTimeout);
      typingTimeout = null;
    }
    
    // Reset dialog text before starting to type
    dialogText.textContent = '';
    
    const typeWriter = () => {
      if (i < text.length) {
        dialogText.textContent += text.charAt(i);
        i++;
        typingTimeout = setTimeout(typeWriter, typingSpeed);
      } else {
        isTyping = false;
        typingTimeout = null;
        nextIndicator.style.display = 'block';
      }
    };
    
    typeWriter();
  }
}

// Advance dialog
function advanceDialog() {
  // Prevent multiple clicks during transitions
  if (isTransitioning) {
    return;
  }
  
  // If typing, complete the text immediately
  if (isTyping) {
    if (typingTimeout) {
      clearTimeout(typingTimeout);
      typingTimeout = null;
    }

    const dialog = story[currentDialogIndex];
    if (dialog && dialog.text) {
      if (dialog.type === 'Style1') {
        style1Text.textContent = dialog.text;
        style1NextIndicator.style.display = 'block';
      } else {
        dialogText.textContent = dialog.text;
        nextIndicator.style.display = 'block';
      }
    }
    isTyping = false;
    return;
  }
  
  // If choices are displayed, don't advance
  if (choices.style.display === 'flex' || choices.style.display === 'block') {
    return;
  }
  
  // Set transitioning flag
  isTransitioning = true;
  
  // Move to next dialog
  currentDialogIndex++;
  
  // Clear previous dialog text before showing new one
  dialogText.textContent = '';
  style1Text.textContent = '';
  
  // Show next dialog after a short delay
  setTimeout(() => {
    showDialog(currentDialogIndex);
    isTransitioning = false; // Reset transitioning flag
  }, 100);
}

// Handle choice selection
function handleChoice(event) {
  // Prevent multiple clicks
  if (isTransitioning) {
    return;
  }
  
  isTransitioning = true;
  
  const choiceIndex = parseInt(event.target.dataset.choice);
  const nextIndex = parseInt(event.target.dataset.nextIndex);
  
  // Store choice
  if (currentDialogIndex === 9) {
    choicesMade.firstChoice = choiceIndex;
  } else {
    choicesMade.secondChoice = choiceIndex;
  }
  
  // Animate selection
  const selectedButton = event.target;
  selectedButton.style.transform = 'scale(1.05)';
  selectedButton.style.boxShadow = '0 0 20px rgba(100, 180, 255, 0.7)';
  
  // Fade out other choices
  const allButtons = document.querySelectorAll('.choice-btn');
  allButtons.forEach(button => {
    if (button !== selectedButton) {
      button.style.opacity = '0.3';
    }
  });
  
  // Delay before moving to next dialog
  setTimeout(() => {
    // Hide choices container
    choices.style.display = 'none';
    
    // Clear dialog text before showing next dialog
    dialogText.textContent = '';
    
    // Move to next dialog
    currentDialogIndex = nextIndex;
    showDialog(currentDialogIndex);
    
    // Reset transitioning flag
    isTransitioning = false;
  }, 800);
}

// Skip dialog
function skipDialog() {
  // Skip to the end of the current episode
  currentDialogIndex = story.length - 1;
  showDialog(currentDialogIndex);
}

// Auto play
let autoPlayInterval = null;
function toggleAutoPlay() {
  const autoBtn = document.querySelector('.auto-btn');
  
  if (autoPlayInterval) {
    // Stop auto play
    clearInterval(autoPlayInterval);
    autoPlayInterval = null;
    autoBtn.classList.remove('active');
    autoBtn.textContent = 'Auto';
  } else {
    // Start auto play
    autoBtn.classList.add('active');
    autoBtn.innerHTML = 'Auto<span class="auto-dots"></span>';
    autoPlayInterval = setInterval(() => {
      if (!isTyping && choices.style.display !== 'flex') {
        advanceDialog();
      }
    }, 3000); // Advance every 2 seconds
  }
}

// Function to stop auto play if it's active
function stopAutoPlayIfActive() {
  if (autoPlayInterval) {
    clearInterval(autoPlayInterval);
    autoPlayInterval = null;
    const autoBtn = document.querySelector('.auto-btn');
    autoBtn.classList.remove('active');
    autoBtn.textContent = 'Auto';
  }
}

// Dialog Log
function showDialogLog() {
  // Stop auto play if active
  stopAutoPlayIfActive();
  
  // Create log container if it doesn't exist
  let logContainer = document.getElementById('dialog-log');
  
  if (!logContainer) {
    logContainer = document.createElement('div');
    logContainer.id = 'dialog-log';
    logContainer.innerHTML = `
      <div class="log-header">
        <h2>Dialog Log</h2>
        <button id="close-log">×</button>
      </div>
      <div class="log-content"></div>
    `;
    document.getElementById('game-container').appendChild(logContainer);
    
    // Add event listener to close button
    document.getElementById('close-log').addEventListener('click', () => {
      logContainer.classList.remove('active');
      setTimeout(() => {
        logContainer.style.display = 'none';
      }, 300);
    });
  }
  
  // Show log container
  logContainer.style.display = 'block';
  setTimeout(() => {
    logContainer.classList.add('active');
  }, 10);
  
  // Populate log content
  const logContent = logContainer.querySelector('.log-content');
  logContent.innerHTML = '';
  
  if (dialogHistory.length === 0) {
    logContent.innerHTML = '<p class="log-empty">No dialog history yet.</p>';
  } else {
    dialogHistory.forEach(entry => {
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry';
      
      if (entry.speaker) {
        logEntry.innerHTML = `<div class="log-speaker">${entry.speaker}</div><div class="log-text">${entry.text}</div>`;
      } else {
        logEntry.innerHTML = `<div class="log-text narrator">${entry.text}</div>`;
      }
      
      logContent.appendChild(logEntry);
    });
    
    // Scroll to bottom
    logContent.scrollTop = logContent.scrollHeight;
  }
}

// Debug function to check state
function debugState() {
  // Force show choices for testing
  if (story[currentDialogIndex].type === 'choice') {
    choices.style.cssText = 'display: flex !important; opacity: 1 !important; z-index: 1000 !important;';
    const choiceSet = currentDialogIndex === 9 ? 0 : 1;
    const choiceButtons = document.querySelectorAll('.choice-btn');
    choiceButtons.forEach((button, i) => {
      if (i < choiceOptions[choiceSet].length) {
        button.style.cssText = 'display: block !important; opacity: 1 !important;';
      }
    });
  }
}

// Add debug key
window.addEventListener('keydown', (e) => {
  if (e.key === 'd') {
    debugState();
  }
});

// Initialize the game when the page loads
window.addEventListener('DOMContentLoaded', initGame);

// Add this function at the end of your game script
function completeChapter() {
  // Get the current chapter ID
  const chapterId = '01-01'; // First chapter ID
  
  // Create form data
  const formData = new FormData();
  formData.append('chapter_id', chapterId);
  
  // Send request to unlock the next chapter
  fetch('../unlock_chapter.php', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    console.log('Chapter completed:', data);
    if (data.success) {
      console.log('Next chapter unlocked:', data.next_chapter);
    }
  })
  .catch(error => {
    console.error('Error completing chapter:', error);
  });
}

// Exit game function
function exitGame() {
  // Stop auto play if active
  stopAutoPlayIfActive();
  
  // Create exit dialog if it doesn't exist
  let exitDialog = document.getElementById('exit-dialog');
  
  if (!exitDialog) {
    exitDialog = document.createElement('div');
    exitDialog.id = 'exit-dialog';
    exitDialog.innerHTML = `
      <div class="exit-dialog-content">
        <h2>Exit</h2>
        <p>Are you sure you want to exit?</p>
        <p class="warning">Your progress will NOT be saved!</p>
        <div class="exit-buttons">
          <button id="cancel-exit">Cancel</button>
          <button id="confirm-exit">Exit Game</button>
        </div>
      </div>
    `;
    document.getElementById('game-container').appendChild(exitDialog);
    
    // Add event listeners to buttons
    document.getElementById('cancel-exit').addEventListener('click', () => {
      exitDialog.classList.remove('active');
      setTimeout(() => {
        exitDialog.style.display = 'none';
      }, 300);
    });
    
    document.getElementById('confirm-exit').addEventListener('click', () => {
      // Navigate back to the chapters page
      window.location.href = '../menuchapters/story-chapters.php';
    });
  }
  
  // Show exit dialog
  exitDialog.style.display = 'flex';
  setTimeout(() => {
    exitDialog.classList.add('active');
  }, 10);
}

// Add exit button to the controls menu
const exitButton = document.createElement('button');
exitButton.className = 'menu-btn exit-btn';
exitButton.textContent = 'Exit';
exitButton.addEventListener('click', exitGame);
controlsMenu.appendChild(exitButton);

// Fungsi untuk menunjukkan skrin mission complete
function showMissionComplete() {
  // Dapatkan container
  const gameContainer = document.getElementById('game-container');
  
  // Sembunyikan karakter-karakter
  const character = document.getElementById('character');
  const characterLeft = document.getElementById('character-left');
  const characterRight = document.getElementById('character-right');
  
  if (character) character.style.display = 'none';
  if (characterLeft) characterLeft.style.display = 'none';
  if (characterRight) characterRight.style.display = 'none';
  
  // Sembunyikan dialog box dan style1 box
  const dialogBox = document.getElementById('dialog-box');
  const style1Box = document.getElementById('style1-box');
  if (dialogBox) dialogBox.style.display = 'none';
  if (style1Box) style1Box.style.display = 'none';
  
  // Buat elemen mission complete
  const missionComplete = document.createElement('div');
  missionComplete.id = 'mission-complete';
  
  // Tambah HTML untuk mission complete
  missionComplete.innerHTML = `
    <div class="mission-banner">
      <div class="mission-badge"></div>
      <div class="mission-text">Mission Complete</div>
    </div>
    
    <div class="success-container">
      <h1 class="success-title">Success</h1>
      <div class="episode-title">Episode 1 - Pertemuan Pertama</div>
      
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      
      <div class="stats-container">
        <div class="level-display">3</div>
        <div class="exp-info">
          <div class="exp-text">120/300</div>
          <div class="exp-gain">+60</div>
        </div>
      </div>
      
      <div class="rewards-section">
        <div class="reward-header">
          <div class="red-dot"></div>
          <div>Compliance Increased</div>
        </div>
        
        <div class="character-icons">
          <div class="char-icon"></div>
          <div class="char-icon"></div>
          <div class="char-icon"></div>
        </div>
        
        <div class="reward-header">
          <div class="red-dot"></div>
          <div>Obtain Reward</div>
        </div>
        
        <div class="rewards-container">
          <div class="reward-item">
            <div class="reward-img"></div>
            <div class="reward-count">35</div>
            <div class="reward-label">1st Clear</div>
          </div>
          <div class="reward-item">
            <div class="reward-img"></div>
            <div class="reward-count">35</div>
            <div class="reward-label">Special</div>
          </div>
          <div class="reward-item">
            <div class="reward-img"></div>
            <div class="reward-count">1</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="character-display"></div>
    
    <div class="next-prompt">Now, what's next?</div>
    
    <button id="continue-mission">CONTINUE</button>
  `;
  
  // Tambah ke game container
  gameContainer.appendChild(missionComplete);
  
  // Tambah event listener untuk button continue
  document.getElementById('continue-mission').addEventListener('click', function() {
    completeChapter(); // Unlock chapter seterusnya
    window.location.href = '../menuchapters/story-chapters.php'; // Pergi ke episode seterusnya
  });
  
  // Tunjukkan mission complete dengan animasi
  setTimeout(function() {
    missionComplete.classList.add('active');
    
    // Animasi progress bar
    setTimeout(function() {
      document.querySelector('.progress-fill').style.width = '40%';
    }, 500);
    
    // Animasi karakter
    setTimeout(function() {
      document.querySelector('.character-display').style.opacity = '1';
      document.querySelector('.character-display').style.transform = 'translateX(0)';
    }, 800);
  }, 100);
}









