/* Gacha Page - Gaming Style CSS */

:root {
    --primary-color: #00b8ff;
    --secondary-color: #ff4d7e;
    --dark-bg: #1a1a1a;
    --card-bg: rgba(40, 44, 52, 0.85);
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --accent-orange: #ff6b00;
    --gacha-bg: rgba(20, 25, 35, 0.95);
    --gacha-border: rgba(74, 144, 226, 0.3);
    --rarity-5: #ffd700;
    --rarity-4: #9966cc;
    --rarity-3: #4169e1;
    --rarity-2: #32cd32;
    --rarity-1: #808080;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-light);
    overflow-x: hidden;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.gacha-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Gaming Background */
.gaming-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    z-index: -2;
}

.gaming-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Floating particles */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
    animation: float 15s infinite linear;
}

.particle:nth-child(1) { width: 4px; height: 4px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 6px; height: 6px; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { width: 3px; height: 3px; left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { width: 5px; height: 5px; left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { width: 4px; height: 4px; left: 60%; animation-delay: 8s; }
.particle:nth-child(6) { width: 7px; height: 7px; left: 80%; animation-delay: 10s; }

@keyframes float {
    0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

.back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: var(--card-bg);
    border: 1px solid var(--gacha-border);
    border-radius: 12px;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.back-button:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 184, 255, 0.3);
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.resource span:last-child {
    font-weight: 600;
    color: var(--text-light);
}

/* Ticket specific styling */
.resource:has(.resource-icon:contains("🎫")) {
    background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), rgba(30, 144, 255, 0.1));
    border-color: rgba(65, 105, 225, 0.5);
}

.resource:has(.resource-icon:contains("🎟️")) {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 237, 78, 0.1));
    border-color: rgba(255, 215, 0, 0.5);
}

.resource:has(.resource-icon:contains("🎪")) {
    background: linear-gradient(135deg, rgba(255, 77, 126, 0.2), rgba(255, 107, 157, 0.1));
    border-color: rgba(255, 77, 126, 0.5);
}

/* Main content */
.gacha-content {
    padding: 30px;
    height: calc(100vh - 85px);
    overflow-y: auto;
    position: relative;
    z-index: 10;
}

/* Gacha pools */
.gacha-pools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.gacha-pool {
    background: var(--gacha-bg);
    border: 1px solid var(--gacha-border);
    border-radius: 20px;
    padding: 25px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.gacha-pool::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.gacha-pool:hover::before {
    left: 100%;
}

.gacha-pool:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(74, 144, 226, 0.2);
    border-color: var(--primary-color);
}

.pool-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.pool-info h3 {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 8px;
}

.pool-info p {
    color: var(--text-gray);
    font-size: 14px;
    line-height: 1.4;
}

.pool-type {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pool-type.standard {
    background: linear-gradient(135deg, #4169e1, #1e90ff);
    color: white;
}

.pool-type.premium {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a1a1a;
}

.pool-type.limited {
    background: linear-gradient(135deg, #ff4d7e, #ff6b9d);
    color: white;
}

.ticket-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ticket-icon {
    font-size: 24px;
}

.ticket-details span {
    display: block;
    color: var(--text-gray);
    font-size: 12px;
}

.ticket-count {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-light);
}

.pull-buttons {
    display: flex;
    gap: 12px;
}

.pull-button {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pull-button.single {
    background: linear-gradient(135deg, var(--primary-color), #0099cc);
    color: white;
}

.pull-button.multi {
    background: linear-gradient(135deg, var(--secondary-color), #ff6b9d);
    color: white;
}

.pull-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.pull-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Spinning animation */
.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Results modal */
.results-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.results-content {
    background: var(--gacha-bg);
    border: 1px solid var(--gacha-border);
    border-radius: 20px;
    padding: 30px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.results-header {
    text-align: center;
    margin-bottom: 25px;
}

.results-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: 10px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.result-item {
    background: var(--card-bg);
    border: 2px solid;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-item.rarity-5 { border-color: var(--rarity-5); }
.result-item.rarity-4 { border-color: var(--rarity-4); }
.result-item.rarity-3 { border-color: var(--rarity-3); }
.result-item.rarity-2 { border-color: var(--rarity-2); }
.result-item.rarity-1 { border-color: var(--rarity-1); }

.result-item:hover {
    transform: scale(1.05);
}

.result-icon {
    font-size: 32px;
    margin-bottom: 8px;
    display: block;
}

.result-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 4px;
}

.result-type {
    font-size: 10px;
    color: var(--text-gray);
    text-transform: uppercase;
}

.close-results {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, var(--primary-color), #0099cc);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-results:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 184, 255, 0.3);
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    z-index: 1001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.notification.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

/* Rarity glow effects */
.result-item.rarity-5::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--rarity-5), transparent, var(--rarity-5));
    border-radius: 12px;
    z-index: -1;
    animation: rarityGlow 2s ease-in-out infinite;
}

.result-item.rarity-4::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--rarity-4), transparent, var(--rarity-4));
    border-radius: 12px;
    z-index: -1;
    animation: rarityGlow 2s ease-in-out infinite;
}

@keyframes rarityGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

/* Responsive design */
@media (max-width: 768px) {
    .top-nav {
        padding: 15px 20px;
    }

    .page-title {
        font-size: 22px;
    }

    .gacha-content {
        padding: 20px;
    }

    .gacha-pools {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .gacha-pool {
        padding: 20px;
    }

    .pull-buttons {
        flex-direction: column;
    }

    .results-content {
        margin: 20px;
        padding: 20px;
        max-width: calc(100vw - 40px);
    }

    .results-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
}
