<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include resource manager
require_once '../includes/resource_manager.php';

// Get username and user ID
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Include character manager
require_once '../includes/character_manager.php';

// Get character data from JSON database
$characterManager = new CharacterManager();
$characters = $characterManager->getAllCharacters();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Characters</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background:
                linear-gradient(135deg, rgba(26, 26, 46, 0.5) 0%, rgba(22, 33, 62, 0.5) 50%, rgba(15, 52, 96, 0.7) 100%),
                url('../assets/backcharacter.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Add backdrop filter to specific elements instead of body */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            backdrop-filter:brightness(0.3) opacity(0.5);
            pointer-events: none;
            z-index: -1;
        }

        #game-container {
            display: flex;
            min-height: 100vh;
        }

        /* Left Sidebar */
        .sidebar {
            width: 250px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: bold;
            color: #00bcd4;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            font-size: 12px;
            color: #999;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #ccc;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(0, 188, 212, 0.1);
            color: #00bcd4;
            border-right: 3px solid #00bcd4;
        }

        .sidebar-menu .icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Top Navigation - Using shared styles from shared-styles.css */

        /* Character Grid */
        .character-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .character-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .character-card {
            background: rgba(20, 25, 40, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            aspect-ratio: 0.75;
            display: flex;
            flex-direction: column;
        }

        .character-card:hover {
            border-color: #00bcd4;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 188, 212, 0.3);
        }

        .character-card.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .character-card.locked:hover {
            transform: none;
            border-color: rgba(255, 255, 255, 0.1);
            box-shadow: none;
        }

        /* Character Image */
        .character-image {
            width: 100%;
            height: 80%;
            border-radius: 6px;
            margin-bottom: 5px;
        }

        .character-card.locked .character-image {
            filter: grayscale(100%);
        }

        /* Character Info */
        .character-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .character-name {
            font-size: 12px;
            font-weight: bold;
            color: white;
            text-align: center;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .character-level {
            font-size: 10px;
            color: #ffa726;
            text-align: center;
            margin-bottom: 4px;
        }

        .character-rarity {
            display: flex;
            justify-content: center;
            gap: 1px;
            margin-bottom: 4px;
        }

        .star {
            color: #ffd700;
            font-size: 10px;
        }

        .star.empty {
            color: #444;
        }

        /* Element Badge */
        .character-element {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Locked Overlay */
        .locked-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            color: #666;
            border-radius: 8px;
        }

        .character-card.unlocked .locked-overlay {
            display: none;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .character-grid {
                grid-template-columns: repeat(5, 1fr);
            }
        }

        @media (max-width: 1000px) {
            .character-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .sidebar {
                width: 200px;
            }
        }

        @media (max-width: 800px) {
            .character-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .sidebar {
                display: none;
            }
        }

        @media (max-width: 600px) {
            .character-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .character-content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <!-- Left Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">🎮 Our Best Characters</div>
                <div class="sidebar-subtitle">Indexed 8/11</div>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active"><span class="icon">👥</span> All</a></li>
                <li><a href="#"><span class="icon">🗡️</span> Destruction</a></li>
                <li><a href="#"><span class="icon">🩺</span> The Hunt</a></li>
                <li><a href="#"><span class="icon">🛡️</span> Erudition</a></li>
                <li><a href="#"><span class="icon">🎵</span> Harmony</a></li>
                <li><a href="#"><span class="icon">❄️</span> Nihility</a></li>
                <li><a href="#"><span class="icon">🌟</span> Preservation</a></li>
                <li><a href="#"><span class="icon">💰</span> Abundance</a></li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="nav-left">
                    <a href="../menu/home.php" class="back-btn style-text">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="page-title">Characters</div>
                </div>
                <div class="top-right">
                    <div class="resource-item">
                        <span class="resource-icon">💎</span>
                        <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                        <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">🪙</span>
                        <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                        <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">⚡</span>
                        <span id="energy-count"><?php echo $resources['energy']; ?></span>
                        <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                    </div>
                </div>
            </div>

            <!-- Character Content -->
            <div class="character-content">
                <div class="character-grid">
            <?php foreach ($characters as $character): ?>
                <div class="character-card <?php echo $character['status']; ?>"
                     onclick="<?php echo $character['status'] === 'unlocked' ? 'selectCharacter(' . $character['id'] . ')' : ''; ?>">

                    <!-- Element indicator -->
                    <div class="character-element" style="background: <?php echo CharacterManager::getElementColor($character['element']); ?>">
                        <?php echo CharacterManager::getElementIcon($character['element']); ?>
                    </div>

                    <img src="<?php echo $character['image']; ?>"
                         alt="<?php echo $character['name']; ?>"
                         class="character-image"
                         onerror="this.src='../assets/icons/cards.png'">

                    <div class="character-info">
                        <div class="character-name"><?php echo $character['name']; ?></div>

                        <?php if ($character['status'] === 'unlocked'): ?>
                            <div class="character-level">Lv.<?php echo $character['level']; ?></div>
                        <?php endif; ?>

                        <div class="character-rarity">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <span class="star <?php echo $i <= $character['rarity'] ? '' : 'empty'; ?>">★</span>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <?php if ($character['status'] === 'locked'): ?>
                        <div class="locked-overlay">🔒</div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectCharacter(characterId) {
            // Navigate to character detail page
            console.log('Selected character:', characterId);

            // Add animation effect before navigation
            const characterCard = event.target.closest('.character-card');
            if (characterCard) {
                characterCard.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    window.location.href = `character-detail.php?id=${characterId}`;
                }, 150);
            } else {
                window.location.href = `character-detail.php?id=${characterId}`;
            }
        }



        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.character-card.unlocked');

            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(40, 44, 52, 0.95)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.background = 'rgba(40, 44, 52, 0.85)';
                });
            });
        });
    </script>

    <!-- Include shared functions -->
    <script src="shared-functions.js"></script>
</body>
</html>