/* Character Detail Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    color: white;
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
}

/* Backdrop overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 50%, rgba(0, 0, 0, 0.8) 100%);
    backdrop-filter: blur(2px);
    pointer-events: none;
    z-index: -1;
}

.character-detail-container {
    height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* Main Content Layout */
.character-main {
    flex: 1;
    display: flex;
    padding: 0.5vh 1vw;
    gap: 1vw;
    max-width: min(1400px, 98vw);
    min-width: 320px;
    margin: 0 auto;
    width: 100%;
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
    overflow: hidden;
    box-sizing: border-box;
}

/* Character Image Panel */
.character-image-panel {
    flex: 0 0 min(350px, 35vw);
    min-width: min(280px, 30vw);
    max-width: 400px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 100%;
    overflow: hidden;
}

.character-portrait {
    position: relative;
    width: 100%;
    height: fit-content;
    max-height: calc(100vh - 80px);
    max-width: 100%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: min(12px, 1.5vw);
    padding: min(12px, 2vw);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-sizing: border-box;
    overflow: hidden;
}

/* Character Name Above Image */
.character-name-display {
    text-align: center;
    margin-bottom: 15px;
}

.character-name-display h2 {
    font-size: 1.8rem;
    font-weight: bold;
    background: linear-gradient(45deg, #00bcd4, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Character Name Above Image */
.character-name-display {
    text-align: center;
    margin-bottom: min(8px, 1vh);
}

.character-name-display h2 {
    font-size: min(1.3rem, 4.5vw);
    font-weight: bold;
    background: linear-gradient(45deg, #00bcd4, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 min(5px, 0.8vh) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.character-title-display {
    font-size: min(0.9rem, 3.2vw);
    color: #ffa726;
    margin: 0;
    font-style: italic;
    font-weight: 500;
}

.character-portrait img {
    width: 100%;
    height: auto;
    max-height: calc(85vh - 60px);
    object-fit: cover;
    border-radius: min(6px, 1vw);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.element-badge {
    position: absolute;
    top: min(15px, 2vh);
    right: min(15px, 2vw);
    padding: min(4px, 0.8vw) min(8px, 1.5vw);
    border-radius: min(12px, 2vw);
    display: flex;
    align-items: center;
    gap: min(3px, 0.5vw);
    font-size: min(0.7rem, 2.5vw);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.rarity-display {
    display: flex;
    gap: 3px;
    justify-content: center;
}

.star {
    font-size: min(14px, 3.5vw);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.star.filled {
    color: #ffd700;
}

.star.empty {
    color: #444;
}

/* Character Level Below Stars */
.character-level-display {
    text-align: center;
    margin-top: min(6px, 1vh);
}

.level-text {
    color: #d5ce4e;
    font-size: min(0.8rem, 2.8vw);
    font-weight: bold;
    padding: min(3px, 0.5vh) min(8px, 1.5vw);
    display: inline-block;
}

/* Character Info Panel */
.character-info-panel {
    flex: 1;
    background: rgba(0, 0, 0, 0.4);
    border-radius: min(12px, 1.5vw);
    padding: min(12px, 2vw);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: calc(100vh - 80px);
    overflow: hidden;
    box-sizing: border-box;
    min-width: 0;
}

/* Character Header */
.character-header {
    margin-bottom: 20px;
    text-align: center;
}

.character-name {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0;
    background: linear-gradient(45deg, #00bcd4, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    margin-bottom: min(8px, 1vh);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
    flex: 1;
    padding: min(8px, 1.2vh) min(12px, 2vw);
    background: transparent;
    border: none;
    color: #ccc;
    font-size: min(0.85rem, 3vw);
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    position: relative;
    font-weight: 500;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 188, 212, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px 8px 0 0;
}

.tab-btn:hover {
    color: #00bcd4;
}

.tab-btn:hover::before {
    opacity: 1;
}

.tab-btn.active {
    color: #00bcd4;
    border-bottom-color: #00bcd4;
    background: rgba(0, 188, 212, 0.1);
}

.tab-btn.active::before {
    opacity: 1;
}

/* Tab Content */
.tab-content {
    flex: 1;
    overflow: hidden;
    height: calc(100% - 40px);
    max-height: calc(100vh - 140px);
}

.tab-panel {
    display: none;
    animation: fadeIn 0.3s ease;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
    height: 100%;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Info Tab Styles */
.character-upgrade {
    margin-bottom: 30px;
}

.character-info-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.character-description,
.character-lore,
.character-stats {
    margin-bottom: 25px;
}

.character-description h3,
.character-lore h3,
.character-upgrade h3,
.character-stats h3 {
    color: #00bcd4;
    margin-bottom: min(8px, 1.2vh);
    font-size: min(1.1rem, 3.5vw);
    display: flex;
    align-items: center;
    gap: min(6px, 1vw);
}

.character-description p,
.character-lore p {
    line-height: 1.4;
    color: #ddd;
    font-size: min(0.9rem, 3vw);
    margin-bottom: min(10px, 1.5vh);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(150px, 40vw), 1fr));
    gap: min(15px, 2vw);
    margin-top: min(15px, 2vh);
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: min(10px, 1.5vw);
    border-radius: min(6px, 1vw);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: min(35px, 7vw);
    box-sizing: border-box;
}

.stat-name {
    color: #ccc;
    font-size: min(0.8rem, 2.8vw);
}

.stat-value {
    color: #00bcd4;
    font-weight: bold;
    font-size: min(0.95rem, 3.2vw);
}

/* Character Upgrade Styles */
.character-upgrade {
    background: linear-gradient(135deg, rgba(0, 188, 212, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: min(8px, 1.5vw);
    padding: min(10px, 2vw);
    position: relative;
    overflow: hidden;
    margin-bottom: min(8px, 1vh);
}

.character-upgrade::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00bcd4, #4caf50, #00bcd4);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.upgrade-header-info {
    margin-bottom: 15px;
}

.upgrade-subtitle {
    color: #ccc;
    font-size: 0.9rem;
    font-style: italic;
}

.upgrade-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.level-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 10px;
}

.current-level,
.next-level {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    min-width: 100px;
}

.level-label {
    font-size: 0.8rem;
    color: #ccc;
    margin-bottom: 5px;
}

.level-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #00bcd4;
}

.level-arrow {
    font-size: 1.5rem;
    color: #ffa726;
    font-weight: bold;
}

.upgrade-requirements {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.requirement-icon {
    font-size: 1.5rem;
}

.requirement-name {
    flex: 1;
    font-weight: 500;
    color: #ddd;
}

.requirement-progress {
    font-weight: bold;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
}

.requirement-progress.can-upgrade {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid #4caf50;
}

.requirement-progress.insufficient {
    background: rgba(255, 87, 34, 0.2);
    color: #ff5722;
    border: 1px solid #ff5722;
}

.upgrade-progress-bar {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00bcd4, #4caf50);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: 0.9rem;
    color: #ccc;
}

.upgrade-ready,
.upgrade-insufficient,
.max-level-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px;
    border-radius: 8px;
    font-weight: bold;
}

.upgrade-ready {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid #4caf50;
    flex-direction: column;
    gap: 15px;
}

/* Upgrade Action Button */
.upgrade-action-btn {
    background: linear-gradient(45deg, #4caf50, #388e3c);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
    animation: pulse-upgrade 2s infinite;
}

.upgrade-action-btn:hover {
    background: linear-gradient(45deg, #388e3c, #2e7d32);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.upgrade-action-btn:active {
    transform: translateY(0);
}

@keyframes pulse-upgrade {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

.upgrade-insufficient {
    background: rgba(255, 87, 34, 0.2);
    color: #ff5722;
    border: 1px solid #ff5722;
}

.max-level-notice {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid #ffc107;
}

/* Upgrade Benefits Styles */
.upgrade-benefits {
    margin-top: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.upgrade-benefits h3 {
    color: #ffa726;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.benefit-item {
    background: rgba(255, 152, 0, 0.1);
    padding: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid rgba(255, 152, 0, 0.2);
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: rgba(255, 152, 0, 0.15);
    border-color: rgba(255, 152, 0, 0.4);
}

.benefit-icon {
    font-size: 1.5rem;
    min-width: 24px;
}

.benefit-name {
    flex: 1;
    font-weight: 500;
    color: #ddd;
}

.benefit-value {
    color: #4caf50;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Skills Tab Styles */
.skills-container h3 {
    color: #00bcd4;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.skill-item {
    display: flex;
    align-items: flex-start;
    gap: min(12px, 2vw);
    background: rgba(255, 255, 255, 0.05);
    padding: min(12px, 2vw);
    border-radius: min(8px, 1.2vw);
    margin-bottom: min(10px, 1.5vh);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.skill-item:hover {
    background: rgba(255, 255, 255, 0.08);
}

.skill-icon {
    font-size: min(1.2rem, 4vw);
    min-width: min(30px, 8vw);
    text-align: center;
}

.skill-info {
    flex: 1;
}

.skill-name {
    font-size: min(1rem, 3.5vw);
    font-weight: bold;
    color: #00bcd4;
    margin-bottom: min(4px, 0.8vh);
}

.skill-type {
    font-size: min(0.8rem, 2.8vw);
    color: #ffa726;
    text-transform: uppercase;
    margin-bottom: min(6px, 1vh);
}

.skill-description {
    color: #ddd;
    margin-bottom: min(6px, 1vh);
    line-height: 1.3;
    font-size: min(0.85rem, 3vw);
}

.skill-damage {
    color: #ff5722;
    font-weight: bold;
    font-size: min(0.8rem, 2.8vw);
}

.skill-cooldown {
    color: #9c27b0;
    font-size: min(0.8rem, 2.8vw);
    margin-top: min(4px, 0.8vh);
}

/* Equipment Tab Styles */
.equipment-container h3 {
    color: #00bcd4;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.equipment-item {
    background: rgba(255, 255, 255, 0.05);
    padding: min(8px, 1.5vw);
    border-radius: min(4px, 0.8vw);
    margin-bottom: min(6px, 1vh);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.equipment-type {
    color: #ffa726;
    font-weight: bold;
    display: inline-block;
    min-width: min(80px, 20vw);
    font-size: min(0.7rem, 2.3vw);
}

.equipment-name {
    color: #00bcd4;
    font-weight: bold;
    font-size: min(0.7rem, 2.3vw);
}

.accessories-list {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.accessory-name {
    color: #ddd;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    font-size: 0.9rem;
}

/* Constellation Tab Styles */
.constellation-container h3 {
    color: #00bcd4;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.constellation-level {
    color: #ffa726;
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.constellation-bonuses h4 {
    color: #4caf50;
    margin-bottom: 15px;
}

.bonus-item {
    background: rgba(76, 175, 80, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 3px solid #4caf50;
    color: #ddd;
}

/* Removed old action buttons - upgrade button now integrated in upgrade section */

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(45deg, #4caf50, #388e3c);
}

.notification.error {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

.notification.warning {
    background: linear-gradient(45deg, #ff9800, #f57c00);
}

.notification.info {
    background: linear-gradient(45deg, #2196f3, #1976d2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .character-main {
        gap: min(15px, 2vw);
        padding: 1vh 1.5vw;
    }

    .character-image-panel {
        flex: 0 0 min(280px, 32vw);
        min-width: min(250px, 28vw);
    }

    .character-portrait {
        padding: min(15px, 2.5vw);
    }

    .character-info-panel {
        padding: min(15px, 2.5vw);
    }
}

@media (max-width: 768px) {
    body {
        height: 100vh;
        max-height: 100vh;
    }

    .character-main {
        padding: 0.3vh 0.8vw;
        gap: min(6px, 1vw);
        height: calc(100vh - 50px);
        max-height: calc(100vh - 50px);
    }

    .character-image-panel {
        flex: 0 0 min(160px, 32vw);
        min-width: min(140px, 28vw);
    }

    .character-info-panel {
        padding: min(6px, 1.2vw);
        flex: 1;
        min-width: 0;
        height: 100%;
        max-height: calc(100vh - 60px);
    }

    .character-portrait {
        padding: min(6px, 1.2vw);
        max-height: calc(100vh - 70px);
    }

    .character-portrait img {
        max-height: calc(40vh - 50px);
    }

    .character-name-display h2 {
        font-size: min(0.9rem, 3.2vw);
    }

    .character-title-display {
        font-size: min(0.6rem, 2.2vw);
    }

    .character-name {
        font-size: min(1rem, 3.5vw);
    }

    .tab-navigation {
        flex-wrap: wrap;
        gap: min(1px, 0.2vw);
        margin-bottom: min(6px, 1vh);
    }

    .tab-btn {
        flex: 1 1 33%;
        min-width: min(60px, 16vw);
        font-size: min(0.6rem, 2.2vw);
        padding: min(4px, 0.8vw) min(6px, 1.2vw);
    }

    .tab-content {
        font-size: min(0.7rem, 2.5vw);
        overflow: hidden;
        height: calc(100% - 50px);
        max-height: calc(100vh - 110px);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: min(4px, 0.8vw);
    }

    .stat-item {
        padding: min(6px, 1.2vw);
        min-height: min(24px, 6vw);
    }

    /* Mobile upgrade button adjustments */
    .upgrade-action-btn {
        padding: 10px 20px;
        font-size: 12px;
    }

    /* Upgrade section mobile adjustments */
    .level-progress {
        flex-direction: column;
        gap: 10px;
    }

    .level-arrow {
        transform: rotate(90deg);
    }

    .requirement-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .requirement-progress {
        align-self: flex-end;
    }

    /* Benefits grid mobile adjustments */
    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .benefit-item {
        padding: 12px;
    }

    /* Adjust element badge for mobile */
    .element-badge {
        top: 20px;
        right: 20px;
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    /* Adjust level display for mobile */
    .level-text {
        font-size: 1rem;
        padding: 4px 12px;
    }
}

/* Extra small screens - maintain side-by-side layout */
@media (max-width: 480px) {
    .character-main {
        padding: 0.2vh 0.4vw;
        gap: min(4px, 0.8vw);
        height: calc(100vh - 45px);
        max-height: calc(100vh - 45px);
    }

    .character-image-panel {
        flex: 0 0 min(120px, 35vw);
        min-width: min(100px, 30vw);
    }

    .character-portrait {
        padding: min(4px, 1vw);
        max-height: calc(100vh - 60px);
    }

    .character-portrait img {
        max-height: calc(35vh - 40px);
    }

    .character-info-panel {
        padding: min(4px, 1vw);
        flex: 1;
        min-width: 0;
        height: 100%;
        max-height: calc(100vh - 55px);
    }

    .character-name-display h2 {
        font-size: min(0.7rem, 3vw);
    }

    .character-title-display {
        font-size: min(0.5rem, 2vw);
    }

    .tab-btn {
        flex: 1 1 50%;
        min-width: min(50px, 15vw);
        font-size: min(0.5rem, 1.8vw);
        padding: min(3px, 0.6vw) min(5px, 1vw);
    }

    .tab-content {
        font-size: min(0.6rem, 2.2vw);
        overflow: hidden;
        height: calc(100% - 40px);
        max-height: calc(100vh - 95px);
    }

    .character-upgrade {
        padding: min(15px, 3vw);
    }

    .level-progress {
        flex-direction: column;
        gap: min(8px, 1.5vw);
    }

    .current-level,
    .next-level {
        min-width: min(80px, 20vw);
        padding: min(10px, 2vw);
    }

    .level-number {
        font-size: min(1.2rem, 4vw);
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: min(8px, 1.5vw);
    }

    .benefit-item {
        padding: min(8px, 1.5vw);
        font-size: min(0.8rem, 2.5vw);
    }

    /* Extra small mobile upgrade button */
    .upgrade-action-btn {
        padding: min(8px, 1.5vw) min(15px, 3vw);
        font-size: min(11px, 2.5vw);
    }

    /* Ensure all content fits without scroll */
    .skill-item,
    .equipment-item,
    .bonus-item {
        padding: min(4px, 1vw);
        margin-bottom: min(3px, 0.8vw);
        font-size: min(0.6rem, 2vw);
    }

    .element-badge {
        top: min(8px, 2vw);
        right: min(8px, 2vw);
        padding: min(2px, 0.5vw) min(6px, 1.2vw);
        font-size: min(0.5rem, 1.8vw);
    }

    .level-text {
        font-size: min(0.6rem, 2.2vw);
        padding: min(2px, 0.5vw) min(6px, 1.5vw);
    }

    /* Make all text super small */
    .skill-name,
    .equipment-name,
    .character-name {
        font-size: min(0.6rem, 2.2vw) !important;
    }

    .skill-description,
    .character-description p,
    .character-lore p {
        font-size: min(0.55rem, 2vw) !important;
        line-height: 1.1 !important;
    }

    .stat-name {
        font-size: min(0.5rem, 1.8vw) !important;
    }

    .stat-value {
        font-size: min(0.55rem, 2vw) !important;
    }

    /* Ultra compact spacing */
    .character-description,
    .character-lore,
    .character-stats {
        margin-bottom: min(6px, 1vh) !important;
    }

    .character-upgrade {
        margin-bottom: min(4px, 0.8vh) !important;
        padding: min(6px, 1.2vw) !important;
    }
}

/* Additional rules to ensure no scroll and proper fitting */
@media (max-height: 600px) {
    .character-main {
        height: calc(100vh - 40px);
        max-height: calc(100vh - 40px);
        padding: 0.5vh 1vw;
    }

    .character-portrait {
        max-height: calc(100vh - 60px);
        padding: min(8px, 1.5vw);
    }

    .character-portrait img {
        max-height: calc(40vh - 40px);
    }

    .character-info-panel {
        max-height: calc(100vh - 60px);
        padding: min(8px, 1.5vw);
    }

    .tab-content {
        height: calc(100% - 50px);
        max-height: calc(100vh - 120px);
    }

    .character-name-display h2 {
        font-size: min(1rem, 3.5vw);
        margin-bottom: min(5px, 1vh);
    }

    .character-title-display {
        font-size: min(0.7rem, 2.5vw);
    }

    .tab-btn {
        padding: min(6px, 1vw) min(8px, 1.5vw);
        font-size: min(0.7rem, 2.5vw);
    }
}

/* Ultra-wide screens optimization */
@media (min-width: 1600px) {
    .character-main {
        max-width: min(1600px, 90vw);
        gap: 3vw;
    }

    .character-image-panel {
        flex: 0 0 min(400px, 25vw);
    }

    .character-portrait {
        padding: 2vw;
    }

    .character-info-panel {
        padding: 2vw;
    }
}

/* Ensure no overflow on any screen size */
html, body {
    overflow: hidden;
    height: 100vh;
    max-height: 100vh;
}

.character-detail-container,
.character-main,
.character-info-panel,
.tab-content,
.tab-panel {
    overflow: hidden;
}

/* Global ultra-compact rules */
* {
    box-sizing: border-box;
}

/* Reasonable text sizes - not too small */
.skills-container h3,
.equipment-container h3,
.constellation-container h3 {
    font-size: min(1rem, 3.5vw);
    margin-bottom: min(8px, 1.2vh);
}

/* Scrollable content only for specific sections if needed */
.character-info-section {
    overflow-y: hidden; /* Changed from auto to hidden */
    max-height: calc(100% - 20px);
    padding-right: 5px;
}

.character-info-section::-webkit-scrollbar,
.skills-container::-webkit-scrollbar,
.equipment-container::-webkit-scrollbar,
.constellation-container::-webkit-scrollbar {
    display: none;
}

/* REASONABLE TABLET ADJUSTMENTS */
@media (max-width: 1024px) {
    .character-main {
        height: calc(100vh - 50px);
        max-height: calc(100vh - 50px);
        padding: 0.8vh 1.2vw;
        gap: 1.2vw;
    }

    .character-portrait {
        padding: min(10px, 1.5vw);
        max-height: calc(100vh - 70px);
    }

    .character-portrait img {
        max-height: calc(50vh - 50px);
    }

    .character-info-panel {
        padding: min(10px, 1.5vw);
        max-height: calc(100vh - 70px);
    }

    .tab-content {
        height: calc(100% - 50px);
        max-height: calc(100vh - 120px);
    }

    .character-name-display h2 {
        font-size: min(1.1rem, 3.8vw);
    }

    .character-title-display {
        font-size: min(0.8rem, 2.8vw);
    }

    .tab-btn {
        font-size: min(0.75rem, 2.6vw);
        padding: min(6px, 1vh) min(10px, 1.5vw);
    }

    .element-badge {
        font-size: min(0.7rem, 2.4vw);
        padding: min(4px, 0.8vw) min(8px, 1.2vw);
    }

    .level-text {
        font-size: min(0.75rem, 2.6vw);
        padding: min(4px, 0.8vw) min(10px, 1.5vw);
    }

    .star {
        font-size: min(16px, 3.5vw);
    }

    .skill-item,
    .equipment-item,
    .stat-item,
    .bonus-item {
        padding: min(8px, 1.2vw);
        margin-bottom: min(6px, 1vh);
        min-height: min(30px, 6vw);
    }

    .character-upgrade {
        padding: min(10px, 1.5vw);
        margin-bottom: min(8px, 1.2vh);
    }
}

/* MOBILE REASONABLE ADJUSTMENTS */
@media (max-width: 600px) {
    .character-main {
        height: calc(100vh - 45px);
        max-height: calc(100vh - 45px);
        padding: 0.5vh 0.8vw;
        gap: 0.8vw;
    }

    .character-image-panel {
        flex: 0 0 min(140px, 32vw);
        min-width: min(120px, 28vw);
    }

    .character-portrait {
        padding: min(8px, 1.2vw);
        max-height: calc(100vh - 60px);
    }

    .character-portrait img {
        max-height: calc(40vh - 40px);
    }

    .character-info-panel {
        padding: min(8px, 1.2vw);
        max-height: calc(100vh - 60px);
    }

    .tab-content {
        height: calc(100% - 40px);
        max-height: calc(100vh - 100px);
    }

    .character-name-display h2 {
        font-size: min(1rem, 3.5vw);
        margin-bottom: min(4px, 0.8vh);
    }

    .character-title-display {
        font-size: min(0.7rem, 2.5vw);
    }

    .tab-btn {
        font-size: min(0.65rem, 2.3vw);
        padding: min(5px, 0.8vh) min(8px, 1.2vw);
    }

    .element-badge {
        font-size: min(0.6rem, 2.2vw);
        padding: min(3px, 0.6vw) min(6px, 1vw);
        top: min(8px, 1.5vw);
        right: min(8px, 1.5vw);
    }

    .level-text {
        font-size: min(0.65rem, 2.3vw);
        padding: min(3px, 0.6vw) min(8px, 1.2vw);
    }

    .star {
        font-size: min(12px, 3vw);
    }

    .skill-item,
    .equipment-item,
    .stat-item,
    .bonus-item {
        padding: min(6px, 1vw);
        margin-bottom: min(4px, 0.8vh);
        min-height: min(25px, 5vw);
    }

    .character-upgrade {
        padding: min(8px, 1.2vw);
        margin-bottom: min(6px, 1vh);
    }

    .stats-grid {
        gap: min(4px, 0.8vw);
        grid-template-columns: 1fr 1fr;
    }

    /* Keep all content visible but make text smaller */
    .skill-name {
        font-size: min(0.85rem, 3vw);
    }

    .skill-description {
        font-size: min(0.75rem, 2.6vw);
    }

    .skill-damage,
    .skill-cooldown {
        font-size: min(0.7rem, 2.4vw);
    }
}

/* Ensure skills and equipment containers fit properly */
.skills-container,
.equipment-container,
.constellation-container {
    height: 100%;
    overflow-y: hidden; /* Changed from auto to hidden */
    padding-right: 5px;
}

/* Compact spacing for better fit */
.skill-item,
.equipment-item,
.bonus-item {
    margin-bottom: min(12px, 1.5vh);
}

.character-description,
.character-lore,
.character-stats {
    margin-bottom: min(20px, 2.5vh);
}

.character-upgrade {
    margin-bottom: min(20px, 2.5vh);
}

/* Ensure text doesn't overflow */
.skill-description,
.character-description p,
.character-lore p {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Responsive font scaling */
@media (max-width: 1024px) {
    .skill-name,
    .equipment-name,
    .character-name {
        font-size: min(1.1rem, 3.5vw);
    }

    .skill-description,
    .character-description p,
    .character-lore p {
        font-size: min(0.9rem, 3vw);
        line-height: 1.4;
    }
}

/* Very small screens optimization */
@media (max-width: 360px) {
    .character-main {
        padding: 0.3vh 0.5vw;
        gap: min(6px, 1vw);
    }

    .character-image-panel {
        flex: 0 0 min(140px, 38vw);
        min-width: min(120px, 32vw);
    }

    .character-portrait {
        padding: min(8px, 1.5vw);
    }

    .character-portrait img {
        max-height: calc(40vh - 50px);
    }

    .character-info-panel {
        padding: min(8px, 1.5vw);
    }

    .tab-btn {
        font-size: min(0.55rem, 2vw);
        padding: min(5px, 1vw) min(6px, 1.2vw);
    }

    .character-name-display h2 {
        font-size: min(0.9rem, 3.5vw);
    }

    .character-title-display {
        font-size: min(0.6rem, 2.2vw);
    }

    .element-badge {
        font-size: min(0.6rem, 2vw);
        padding: min(4px, 0.8vw) min(8px, 1.5vw);
    }

    .level-text {
        font-size: min(0.8rem, 2.8vw);
        padding: min(3px, 0.8vw) min(10px, 2vw);
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .character-main {
        height: calc(100vh - 50px);
        max-height: calc(100vh - 50px);
        padding: 0.5vh 1vw;
    }

    .character-portrait {
        max-height: calc(100vh - 70px);
        padding: min(10px, 2vw);
    }

    .character-portrait img {
        max-height: calc(70vh - 60px);
    }

    .character-info-panel {
        max-height: calc(100vh - 70px);
        padding: min(10px, 2vw);
    }

    .tab-content {
        height: calc(100% - 45px);
        max-height: calc(100vh - 115px);
    }

    .character-name-display h2 {
        font-size: min(1rem, 3vw);
        margin-bottom: min(3px, 0.5vh);
    }

    .character-title-display {
        font-size: min(0.7rem, 2.5vw);
    }

    .tab-btn {
        padding: min(5px, 1vw) min(8px, 1.5vw);
        font-size: min(0.65rem, 2.2vw);
    }
}

