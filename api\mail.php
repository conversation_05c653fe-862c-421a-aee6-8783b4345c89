<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

require_once '../includes/mail_manager.php';

$user_id = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                $archived = isset($_GET['archived']) && $_GET['archived'] === 'true';
                $mail = MailManager::getUserMail($user_id, $archived);
                echo json_encode([
                    'success' => true,
                    'mail' => $mail
                ]);
            } elseif ($action === 'unread_count') {
                $count = MailManager::getUnreadCount($user_id);
                echo json_encode([
                    'success' => true,
                    'unread_count' => $count
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if ($action === 'mark_read') {
                $mail_id = $input['mail_id'] ?? 0;
                $success = MailManager::markAsRead($user_id, $mail_id);
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'Mail marked as read' : 'Failed to mark mail as read'
                ]);
            } elseif ($action === 'mark_all_read') {
                $success = MailManager::markAllAsRead($user_id);
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'All mail marked as read' : 'Failed to mark all mail as read'
                ]);
            } elseif ($action === 'toggle_archive') {
                $mail_id = $input['mail_id'] ?? 0;
                $success = MailManager::toggleArchive($user_id, $mail_id);
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'Mail archive status updated' : 'Failed to update archive status'
                ]);
            } elseif ($action === 'claim_attachment') {
                $mail_id = $input['mail_id'] ?? 0;
                $result = MailManager::claimAttachment($user_id, $mail_id);
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        case 'DELETE':
            if ($action === 'delete') {
                $mail_id = $_GET['mail_id'] ?? 0;
                $success = MailManager::deleteMail($user_id, $mail_id);
                echo json_encode([
                    'success' => $success,
                    'message' => $success ? 'Mail deleted successfully' : 'Failed to delete mail'
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
