<?php
require_once __DIR__ . '/../config/database.php';

class AnnouncementManager {
    
    /**
     * Get all active announcements
     */
    public static function getActiveAnnouncements($category = null) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT * FROM announcements 
                WHERE is_active = 1 
                AND (start_date <= NOW()) 
                AND (end_date IS NULL OR end_date >= NOW())";
        
        if ($category) {
            $sql .= " AND category = ?";
        }
        
        $sql .= " ORDER BY priority DESC, is_featured DESC, created_at DESC";
        
        $stmt = $conn->prepare($sql);
        if ($category) {
            $stmt->bind_param("s", $category);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $announcements = [];
        while ($row = $result->fetch_assoc()) {
            $announcements[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $announcements;
    }
    
    /**
     * Get announcements by category
     */
    public static function getAnnouncementsByCategory($category) {
        return self::getActiveAnnouncements($category);
    }
    
    /**
     * Get unread announcements count for a user
     */
    public static function getUnreadCount($user_id) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT COUNT(*) as unread_count 
                FROM announcements a
                LEFT JOIN user_announcement_reads uar ON a.id = uar.announcement_id AND uar.user_id = ?
                WHERE a.is_active = 1 
                AND (a.start_date <= NOW()) 
                AND (a.end_date IS NULL OR a.end_date >= NOW())
                AND uar.id IS NULL";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        $count = $row['unread_count'];
        
        $stmt->close();
        $conn->close();
        
        return $count;
    }
    
    /**
     * Mark announcement as read for a user
     */
    public static function markAsRead($user_id, $announcement_id) {
        $conn = getDatabaseConnection();
        
        $sql = "INSERT IGNORE INTO user_announcement_reads (user_id, announcement_id) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $user_id, $announcement_id);
        $success = $stmt->execute();
        
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Check if user has read an announcement
     */
    public static function hasUserRead($user_id, $announcement_id) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT id FROM user_announcement_reads WHERE user_id = ? AND announcement_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $user_id, $announcement_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $hasRead = $result->num_rows > 0;
        
        $stmt->close();
        $conn->close();
        
        return $hasRead;
    }
    
    /**
     * Create a new announcement
     */
    public static function createAnnouncement($title, $content, $type = 'system', $category = 'System Announcement', $priority = 'normal', $is_featured = false, $end_date = null) {
        $conn = getDatabaseConnection();
        
        $sql = "INSERT INTO announcements (title, content, announcement_type, category, priority, is_featured, end_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssssis", $title, $content, $type, $category, $priority, $is_featured, $end_date);
        $success = $stmt->execute();
        
        $announcement_id = $conn->insert_id;
        
        $stmt->close();
        $conn->close();
        
        return $success ? $announcement_id : false;
    }
    
    /**
     * Get announcement by ID
     */
    public static function getAnnouncementById($id) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT * FROM announcements WHERE id = ? AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $announcement = $result->fetch_assoc();
        
        $stmt->close();
        $conn->close();
        
        return $announcement;
    }
    
    /**
     * Get announcements with read status for a user
     */
    public static function getAnnouncementsWithReadStatus($user_id, $category = null) {
        $conn = getDatabaseConnection();
        
        $sql = "SELECT a.*, 
                       CASE WHEN uar.id IS NOT NULL THEN 1 ELSE 0 END as is_read
                FROM announcements a
                LEFT JOIN user_announcement_reads uar ON a.id = uar.announcement_id AND uar.user_id = ?
                WHERE a.is_active = 1 
                AND (a.start_date <= NOW()) 
                AND (a.end_date IS NULL OR a.end_date >= NOW())";
        
        if ($category) {
            $sql .= " AND a.category = ?";
        }
        
        $sql .= " ORDER BY a.priority DESC, a.is_featured DESC, a.created_at DESC";
        
        $stmt = $conn->prepare($sql);
        if ($category) {
            $stmt->bind_param("is", $user_id, $category);
        } else {
            $stmt->bind_param("i", $user_id);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $announcements = [];
        while ($row = $result->fetch_assoc()) {
            $announcements[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $announcements;
    }
    
    /**
     * Delete announcement (soft delete by setting is_active to false)
     */
    public static function deleteAnnouncement($id) {
        $conn = getDatabaseConnection();
        
        $sql = "UPDATE announcements SET is_active = 0 WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $id);
        $success = $stmt->execute();
        
        $stmt->close();
        $conn->close();
        
        return $success;
    }
}
?>
