<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON> <PERSON> - Visual Novel</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;700&display=swap');
    
    :root {
      --primary-color: #ff6b6b;
      --primary-dark: #c83349;
      --secondary-color: #4ecdc4;
      --text-light: #f7f7f7;
      --text-dark: #2d3436;
      --bg-dark: #1e272e;
      --bg-light: #f5f5f5;
      --accent: #ffd166;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Montserrat', sans-serif;
      background-color: var(--bg-dark);
      color: var(--text-light);
      overflow: hidden;
    }
    
    #game-container {
      position: relative;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
    }
    
    /* Background with gradient overlay */
    #background {
      position: absolute;
      width: 110%;
      height: 110%;
      top: -5%;
      left: -5%;
      background-image: url('../assets/park-background.png');
      background-size: cover;
      background-position: center;
      filter: brightness(0.9);
      transition: transform 0.5s ease-out;
    }
    
    #background::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.6));
    }
    
    #character {
      position: absolute;
      bottom: -20%;        /* Further lowered position (was 0%) */
      left: 50%;
      transform: translateX(-50%);
      height: 130%;  
      width: 50%;      /* Keeping the increased size */
      transition: opacity 0.8s ease, transform 0.5s ease;
    }
    
    /* Title screen */
    #title-screen {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 100;
      background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.7)), url('../assets/duo.png');
      background-size: cover;
      background-position: center;
    }
    
    #title-content {
      text-align: center;
      padding: 3rem;
      border-radius: 1.5rem;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      background: rgba(0,0,0,0.4);
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      border: 1px solid rgba(255,255,255,0.1);
      max-width: 80%;
      animation: fadeIn 1.5s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    #title-content h1 {
      font-family: 'Playfair Display', serif;
      font-size: 4rem;
      margin-bottom: 1rem;
      color: var(--text-light);
      text-shadow: 0 0 15px rgba(0,0,0,0.7);
      letter-spacing: 2px;
      line-height: 1.2;
    }
    
    #title-content h2 {
      font-family: 'Montserrat', sans-serif;
      font-size: 1.8rem;
      font-weight: 400;
      margin-bottom: 3rem;
      color: var(--accent);
      text-shadow: 0 0 10px rgba(0,0,0,0.5);
      letter-spacing: 1px;
    }
    
    #start-button {
      padding: 1.2rem 3.5rem;
      font-size: 1.5rem;
      background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      letter-spacing: 2px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      position: relative;
      overflow: hidden;
    }
    
    #start-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: 0.5s;
    }
    
    #start-button:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.5);
    }
    
    #start-button:hover::before {
      left: 100%;
    }
    
    #dialog-box {
      position: absolute;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      max-width: 1200px;
      background: linear-gradient(to bottom, rgba(220, 220, 225, 0.5), rgba(200, 200, 210, 0.45));
      color: #333;
      display: flex;
      flex-direction: column;
      border-radius: 50px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 4px solid rgba(180, 180, 190, 0.5);
      overflow: hidden;
      padding: 5px 0;
      min-height: 160px; /* Increased height for 2 lines of text */
      backdrop-filter: blur(3px);
      -webkit-backdrop-filter: blur(3px);
    }
    #dialog-box::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(230,230,235,0.2), rgba(210,210,220,0.05));
      pointer-events: none;
    }
    #dialog-box:hover {
      transform: translateX(-50%) translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
      border-color: rgba(190, 190, 200, 0.9);
    }    #character-name-container {
      position: relative;
      padding: 0.5rem 0;
      margin-left: 2rem;
      margin-top: 0.5rem;
    }
    
    #character-name {
      padding: 0.5rem 1.5rem;
      font-weight: 700;
      color: white;
      font-size: 1.6rem;
      background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
      display: inline-block;
      border-radius: 30px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }
    
    #dialog-text {
      padding: 1rem 2.5rem 2rem 2.5rem;
      line-height: 1.8;
      font-size: 1.5rem;
      color: #000000;
      min-height: 140px; /* Increased from 80px */
      font-weight: 500;
      text-shadow: 0 0 1px rgba(0,0,0,0.1);
      letter-spacing: 0.3px;
    }
    
    #next-indicator {
      position: absolute;
      bottom: 15px;
      right: 30px;
      color: var(--primary-dark);
      animation: bounce 1s infinite;
      font-size: 1.8rem;
    }
    
    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-10px); }
    }
    
    /* Choices */
    #choices {
      position: absolute;
      bottom: 220px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      max-width: 800px;
      display: none;
      flex-direction: column;
      gap: 1.2rem;
      padding: 2rem;
      background: rgba(30,30,40,0.9);
      border-radius: 20px;
      z-index: 9999;
      box-shadow: 0 0 30px rgba(0,0,0,0.5);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.1);
    }
    
    .choice-btn {
      padding: 1.2rem 1.8rem;
      background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      text-align: left;
      font-size: 1.4rem;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      font-family: 'Montserrat', sans-serif;
      font-weight: 500;
      position: relative;
      overflow: hidden;
    }
    
    .choice-btn::after {
      content: '→';
      position: absolute;
      right: 20px;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .choice-btn:hover {
      transform: translateX(10px);
      padding-right: 2.5rem;
    }
    
    .choice-btn:hover::after {
      opacity: 1;
    }
    
    /* Controls menu */
    #controls-menu {
      position: absolute;
      top: 25px;
      right: 25px;
      display: flex;
      gap: 12px;
      z-index: 50;
    }
    
    .menu-btn {
      padding: 0.8rem 1.2rem;
      background: rgba(0,0,0,0.6);
      color: white;
      border: 1px solid rgba(255,255,255,0.2);
      border-radius: 10px;
      cursor: pointer;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      box-shadow: 0 3px 8px rgba(0,0,0,0.3);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      font-family: 'Montserrat', sans-serif;
      font-weight: 500;
    }
    
    .menu-btn:hover {
      background: rgba(var(--primary-dark),0.8);
      border-color: rgba(255,255,255,0.4);
      transform: translateY(-3px);
      box-shadow: 0 5px 12px rgba(0,0,0,0.4);
    }
    
    /* Continue button */
    #continue-button {
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      padding: 1rem 3rem;
      font-size: 1.4rem;
      background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
      color: white;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 5px 20px rgba(0,0,0,0.3);
      font-weight: 600;
      letter-spacing: 2px;
      display: none;
      z-index: 100;
      font-family: 'Montserrat', sans-serif;
    }
    
    #continue-button:hover {
      background: linear-gradient(to right, #ff8585, #e04c63);
      transform: translateX(-50%) translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.4);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
      #title-content h1 {
        font-size: 2.5rem;
      }
      
      #title-content h2 {
        font-size: 1.3rem;
      }
      
      #dialog-text {
        font-size: 1.2rem;
        padding: 0.8rem 2rem 1.8rem 2rem;
      }
      
      #character-name {
        font-size: 1.3rem;
      }
      
      .choice-btn {
        font-size: 1.2rem;
        padding: 1rem 1.5rem;
      }
      
      .menu-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
      }
      
      #continue-button {
        font-size: 1.2rem;
        padding: 0.8rem 2.5rem;
      }
    }
  </style>
</head>
<body>
  <div id="game-container">
    <div id="background"></div>
    <img id="character" src="../assets/aisyah.png" alt="Character" style="display: none;">
    
    <div id="title-screen">
      <div id="title-content">
        <h1>Cinta di Taman</h1>
        <h2>EPISOD 2 – Awek Baru</h2>
        <button id="start-button">Sambung</button>
      </div>
    </div>
    
    <div id="controls-menu" style="display: none;">
      <button class="menu-btn">Auto</button>
      <button class="menu-btn">Skip</button>
      <button class="menu-btn">Log</button>
      <button class="menu-btn">Save</button>
      <button class="menu-btn">Menu</button>
    </div>
    
    <div id="dialog-box">
      <div id="character-name-container">
        <div id="character-name">Salman</div>
      </div>
      <div id="dialog-text"></div>
      <div id="next-indicator">▼</div>
    </div>
    
    <div id="choices" style="display: none;">
      <button class="choice-btn" data-choice="1">Choice 1</button>
      <button class="choice-btn" data-choice="2">Choice 2</button>
      <button class="choice-btn" data-choice="3">Choice 3</button>
    </div>
    
    <button id="continue-button">TERUSKAN</button>
  </div>

  <script src="game-script2.js"></script>
  <button id="debug-button" style="position: absolute; top: 10px; left: 10px; z-index: 9999; background: red; color: white; padding: 10px; display: none;">Debug</button>
  
  <script>
    document.getElementById('debug-button').addEventListener('click', function() {
      document.getElementById('choices').style.cssText = 'display: flex !important; opacity: 1 !important; z-index: 9999 !important;';
      currentDialogIndex = 9;
      showDialog(currentDialogIndex);
    });
    
    // Add subtle parallax effect
    document.addEventListener('mousemove', function(e) {
      const background = document.getElementById('background');
      const x = e.clientX / window.innerWidth;
      const y = e.clientY / window.innerHeight;
      
      background.style.transform = `translate(-${x * 10}px, -${y * 10}px)`;
    });
    
    // Add pulsing effect to start button
    setInterval(function() {
      const startButton = document.getElementById('start-button');
      startButton.style.transform = 'translateY(-5px)';
      startButton.style.boxShadow = '0 10px 25px rgba(0,0,0,0.5)';
      
      setTimeout(function() {
        startButton.style.transform = 'translateY(0)';
        startButton.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
      }, 700);
    }, 1500);
  </script>
</body>
</html>




