/**
 * Shared JavaScript Functions for Story Game
 * Contains common functionality for resource management, navigation, and UI interactions
 */

// ===== RESOURCE MANAGEMENT FUNCTIONS =====

/**
 * Navigate to store page with resource type filter
 * @param {string} resourceType - Type of resource (coins, diamonds, energy, tickets)
 */
function goToStore(resourceType) {
    // Add a small animation effect
    if (event && event.target) {
        event.target.style.transform = 'scale(0.8)';
        setTimeout(() => {
            event.target.style.transform = 'scale(1.1)';
            setTimeout(() => {
                event.target.style.transform = '';
                // Navigate to store page with the specific resource type
                window.location.href = `store.php?resource=${resourceType}`;
            }, 100);
        }, 100);
    } else {
        // Fallback without animation
        window.location.href = `store.php?resource=${resourceType}`;
    }
}

/**
 * Update resource display on the page
 * @param {Object} resources - Object containing resource values
 */
function updateResourceDisplay(resources) {
    if (resources.coins !== undefined) {
        const coinsElement = document.getElementById('coins-count');
        if (coinsElement) {
            coinsElement.textContent = resources.coins.toLocaleString();
        }
    }
    if (resources.diamonds !== undefined) {
        const diamondsElement = document.getElementById('diamonds-count');
        if (diamondsElement) {
            diamondsElement.textContent = resources.diamonds.toLocaleString();
        }
    }
    if (resources.energy !== undefined) {
        const energyElement = document.getElementById('energy-count');
        if (energyElement) {
            energyElement.textContent = resources.energy;
        }
    }
}

/**
 * Update ticket display on the page
 * @param {string} ticketType - Type of ticket (standard, premium, limited)
 * @param {number} newCount - New ticket count
 */
function updateTicketDisplay(ticketType, newCount) {
    const topTicketElement = document.getElementById(`${ticketType}-tickets`);
    if (topTicketElement) {
        topTicketElement.textContent = newCount;
    }
}

/**
 * Fetch and update user resources from server
 */
async function updateUserResources() {
    try {
        const response = await fetch('../api/resources.php');
        const result = await response.json();

        if (result.success) {
            updateResourceDisplay(result.resources);
        }
    } catch (error) {
        console.error('Error updating resources:', error);
    }
}

// ===== NOTIFICATION FUNCTIONS =====

/**
 * Show notification message to user
 * @param {string} message - Message to display
 * @param {string} type - Type of notification (success, error, warning, info)
 */
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    if (notification) {
        notification.textContent = message;
        notification.className = `notification ${type}`;
        notification.classList.add('show');

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// ===== UTILITY FUNCTIONS =====

/**
 * Format number with commas for better readability
 * @param {number} num - Number to format
 * @returns {string} Formatted number string
 */
function formatNumber(num) {
    return num.toLocaleString();
}

/**
 * Animate element with scale effect
 * @param {HTMLElement} element - Element to animate
 * @param {number} duration - Animation duration in milliseconds
 */
function animateScale(element, duration = 200) {
    if (!element) return;
    
    element.style.transform = 'scale(0.95)';
    setTimeout(() => {
        element.style.transform = 'scale(1.05)';
        setTimeout(() => {
            element.style.transform = '';
        }, duration / 2);
    }, duration / 2);
}

/**
 * Add loading state to button
 * @param {HTMLElement} button - Button element
 * @param {string} loadingText - Text to show while loading
 * @returns {string} Original button text
 */
function setButtonLoading(button, loadingText = 'Loading...') {
    if (!button) return '';
    
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;
    return originalText;
}

/**
 * Remove loading state from button
 * @param {HTMLElement} button - Button element
 * @param {string} originalText - Original button text to restore
 */
function removeButtonLoading(button, originalText) {
    if (!button) return;
    
    button.disabled = false;
    button.innerHTML = originalText;
}

// ===== API HELPER FUNCTIONS =====

/**
 * Make API request with error handling
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} API response
 */
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('API request failed:', error);
        showNotification('Network error occurred', 'error');
        throw error;
    }
}

// ===== INITIALIZATION =====

/**
 * Initialize shared functionality when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add click animation to all plus buttons
    const plusButtons = document.querySelectorAll('.plus-btn');
    plusButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            animateScale(this, 150);
        });
    });

    // Add hover effects to resource items
    const resourceItems = document.querySelectorAll('.resource-item');
    resourceItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });

    // Auto-update resources every 30 seconds
    setInterval(updateUserResources, 30000);
});

// ===== EXPORT FOR MODULE USAGE (if needed) =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        goToStore,
        updateResourceDisplay,
        updateTicketDisplay,
        updateUserResources,
        showNotification,
        formatNumber,
        animateScale,
        setButtonLoading,
        removeButtonLoading,
        apiRequest
    };
}
