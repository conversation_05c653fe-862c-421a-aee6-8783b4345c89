/* Mission Page - Honkai Star Rail Style CSS */

:root {
    --primary-color: #00b8ff;
    --secondary-color: #ff4d7e;
    --dark-bg: #1a1a1a;
    --card-bg: rgba(40, 44, 52, 0.85);
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --accent-orange: #ff6b00;
    --mission-bg: rgba(20, 25, 35, 0.95);
    --mission-border: rgba(74, 144, 226, 0.3);
    --claim-button: #ff4444;
    --claim-button-hover: #ff6666;
    --completed-green: #00ff88;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    color: var(--text-light);
    overflow-x: hidden;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.mission-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* Animated Gaming Background */
.gaming-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, #1a0033, #330066, #4d0080, #6600cc, #8533ff);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

.gaming-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 255, 0.2) 0%, transparent 50%);
    animation: pulseGlow 4s ease-in-out infinite alternate;
}

.gaming-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%);
    background-size: 20px 20px;
    animation: gridMove 20s linear infinite;
}

/* Floating Particles */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { width: 4px; height: 4px; top: 10%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 6px; height: 6px; top: 20%; left: 80%; animation-delay: 1s; }
.particle:nth-child(3) { width: 3px; height: 3px; top: 60%; left: 20%; animation-delay: 2s; }
.particle:nth-child(4) { width: 5px; height: 5px; top: 80%; left: 70%; animation-delay: 3s; }
.particle:nth-child(5) { width: 4px; height: 4px; top: 30%; left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { width: 7px; height: 7px; top: 70%; left: 90%; animation-delay: 5s; }

/* Neon Orbs */
.neon-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(1px);
    pointer-events: none;
    animation: orbFloat 8s ease-in-out infinite;
}

.neon-orb:nth-child(1) {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(138, 43, 226, 0.4) 0%, transparent 70%);
    top: 15%;
    left: 85%;
    animation-delay: 0s;
}

.neon-orb:nth-child(2) {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 20, 147, 0.3) 0%, transparent 70%);
    top: 70%;
    left: 10%;
    animation-delay: 2s;
}

.neon-orb:nth-child(3) {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(0, 191, 255, 0.4) 0%, transparent 70%);
    top: 40%;
    left: 70%;
    animation-delay: 4s;
}

/* Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes pulseGlow {
    0% { opacity: 0.5; transform: scale(1); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

@keyframes orbFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
    33% { transform: translateY(-30px) scale(1.1); opacity: 0.8; }
    66% { transform: translateY(15px) scale(0.9); opacity: 0.7; }
}

/* Enhanced Gaming Effects */
.mission-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(90deg, transparent 0%, rgba(138, 43, 226, 0.1) 50%, transparent 100%),
        linear-gradient(0deg, transparent 0%, rgba(255, 20, 147, 0.1) 50%, transparent 100%);
    pointer-events: none;
    z-index: 1;
    animation: scanLines 3s ease-in-out infinite alternate;
}

@keyframes scanLines {
    0% { opacity: 0.3; transform: translateX(-100%); }
    100% { opacity: 0.7; transform: translateX(100%); }
}

/* Interactive Effects */
@keyframes burstEffect {
    0% { transform: scale(0) rotate(0deg); opacity: 1; }
    100% { transform: scale(4) rotate(360deg); opacity: 0; }
}

@keyframes floatUp {
    0% { transform: translateY(0) rotate(0deg); opacity: 0.8; }
    100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
}

.page-title {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 184, 255, 0.5);
}

/* Main content area */
.mission-content {
    position: absolute;
    top: 80px;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20px;
    overflow-y: auto;
}

/* Mission header */
.mission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--mission-bg);
    border: 2px solid var(--mission-border);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.operation-summary {
    display: flex;
    align-items: center;
    gap: 15px;
}

.operation-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 0 20px rgba(0, 184, 255, 0.5);
}

.operation-title {
    font-size: 28px;
    font-weight: bold;
    color: var(--text-light);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.claim-all-button {
    padding: 15px 30px;
    background: linear-gradient(135deg, var(--claim-button), var(--claim-button-hover));
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(255, 68, 68, 0.3);
}

.claim-all-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 68, 68, 0.5);
}

.claim-all-button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Mission list */
.mission-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mission-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: var(--mission-bg);
    border: 2px solid var(--mission-border);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mission-item:hover {
    border-color: var(--primary-color);
    box-shadow:
        0 5px 20px rgba(0, 184, 255, 0.2),
        0 0 20px rgba(138, 43, 226, 0.3),
        inset 0 0 20px rgba(255, 20, 147, 0.1);
    transform: translateY(-2px);
}

.mission-item.completed {
    border-color: var(--completed-green);
    background: rgba(0, 255, 136, 0.05);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

.mission-item.completed:hover {
    box-shadow:
        0 5px 20px rgba(0, 255, 136, 0.4),
        0 0 25px rgba(0, 255, 136, 0.5);
}

.mission-item.claimed {
    opacity: 0.6;
    border-color: #666;
}

.mission-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--claim-button), var(--claim-button-hover));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin-right: 20px;
    flex-shrink: 0;
}

.mission-details {
    flex: 1;
    margin-right: 20px;
}

.mission-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-light);
    margin-bottom: 8px;
}

.mission-description {
    font-size: 14px;
    color: var(--text-gray);
    margin-bottom: 10px;
}

.mission-progress {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--completed-green));
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 12px;
    color: var(--text-gray);
    min-width: 60px;
    text-align: right;
}

.mission-rewards {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-right: 20px;
}

.reward-item {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.reward-icon {
    font-size: 16px;
}

.reward-amount {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-light);
}

.claim-button {
    padding: 12px 25px;
    background: linear-gradient(135deg, var(--claim-button), var(--claim-button-hover));
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 80px;
}

.claim-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 68, 68, 0.4);
}

.claim-button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.claim-button.claimed {
    background: var(--completed-green);
    cursor: default;
}

.claim-button.claimed:hover {
    transform: none;
    box-shadow: none;
}

/* Loading and notification styles */
.loading {
    text-align: center;
    padding: 40px;
    color: var(--text-gray);
}

.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, var(--completed-green), #00cc66);
}

.notification.error {
    background: linear-gradient(135deg, var(--claim-button), var(--claim-button-hover));
}

/* Responsive design */
@media (max-width: 768px) {
    .mission-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .mission-number {
        margin-right: 0;
    }
    
    .mission-rewards {
        margin-right: 0;
        flex-wrap: wrap;
    }
    
    .operation-title {
        font-size: 20px;
    }
    
    .claim-all-button {
        padding: 12px 20px;
        font-size: 14px;
    }
}

/* Enhanced Gaming Effects for Mission Page */
.claim-button:hover {
    box-shadow:
        0 5px 15px rgba(255, 68, 68, 0.4),
        0 0 15px rgba(255, 20, 147, 0.5) !important;
}

.claim-all-button:hover {
    box-shadow:
        0 8px 25px rgba(0, 255, 136, 0.4),
        0 0 15px rgba(0, 255, 136, 0.5) !important;
}

.mission-main {
    backdrop-filter: blur(15px) !important;
    background: rgba(20, 25, 35, 0.9) !important;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(138, 43, 226, 0.1);
}

.mission-header {
    backdrop-filter: blur(15px) !important;
    background: rgba(20, 25, 35, 0.9) !important;
    border: 2px solid rgba(138, 43, 226, 0.3) !important;
    box-shadow: 0 5px 20px rgba(138, 43, 226, 0.1);
}
