<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/resource_manager.php';

class MissionManager {
    
    /**
     * Initialize default missions for the system
     */
    public static function initializeDefaultMissions() {
        $conn = getDatabaseConnection();
        
        // Check if missions already exist
        $check = $conn->query("SELECT COUNT(*) as count FROM missions");
        $result = $check->fetch_assoc();
        
        if ($result['count'] > 0) {
            $conn->close();
            return; // Missions already initialized
        }
        
        // Default missions similar to Honkai Star Rail
        $missions = [
            [
                'title' => 'Complete Main Story Level 1-1',
                'description' => 'Complete the first level of the main story',
                'mission_type' => 'story',
                'target_value' => 1,
                'reward_coins' => 2000,
                'reward_diamonds' => 2000,
                'reward_energy' => 0
            ],
            [
                'title' => 'Complete Side Story Level Sd-103',
                'description' => 'Complete side story level Sd-103',
                'mission_type' => 'story',
                'target_value' => 1,
                'reward_coins' => 0,
                'reward_diamonds' => 10,
                'reward_energy' => 0
            ],
            [
                'title' => 'Level up Hecate to Lv. 10',
                'description' => 'Upgrade Hecate character to level 10',
                'mission_type' => 'achievement',
                'target_value' => 1,
                'reward_coins' => 0,
                'reward_diamonds' => 0,
                'reward_energy' => 0
            ],
            [
                'title' => 'Complete Cleansing Operation 6 times',
                'description' => 'Complete cleansing operations 6 times',
                'mission_type' => 'daily',
                'target_value' => 6,
                'reward_coins' => 0,
                'reward_diamonds' => 0,
                'reward_energy' => 0
            ]
        ];
        
        $stmt = $conn->prepare("INSERT INTO missions (title, description, mission_type, target_value, reward_coins, reward_diamonds, reward_energy) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($missions as $mission) {
            $stmt->bind_param("sssiiii", 
                $mission['title'],
                $mission['description'],
                $mission['mission_type'],
                $mission['target_value'],
                $mission['reward_coins'],
                $mission['reward_diamonds'],
                $mission['reward_energy']
            );
            $stmt->execute();
        }
        
        $stmt->close();
        $conn->close();
    }
    
    /**
     * Get all missions for a user with their progress
     */
    public static function getUserMissions($user_id) {
        $conn = getDatabaseConnection();
        
        // Initialize default missions if they don't exist
        self::initializeDefaultMissions();
        
        $sql = "SELECT m.*, 
                       COALESCE(um.current_progress, 0) as current_progress,
                       COALESCE(um.is_completed, 0) as is_completed,
                       COALESCE(um.is_claimed, 0) as is_claimed,
                       um.completed_at,
                       um.claimed_at
                FROM missions m
                LEFT JOIN user_missions um ON m.id = um.mission_id AND um.user_id = ?
                WHERE m.is_active = 1
                ORDER BY m.mission_type, m.id";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $missions = [];
        while ($row = $result->fetch_assoc()) {
            $missions[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $missions;
    }
    
    /**
     * Update mission progress for a user
     */
    public static function updateMissionProgress($user_id, $mission_id, $progress_increment = 1) {
        $conn = getDatabaseConnection();
        
        // Get mission details
        $mission_stmt = $conn->prepare("SELECT target_value FROM missions WHERE id = ?");
        $mission_stmt->bind_param("i", $mission_id);
        $mission_stmt->execute();
        $mission_result = $mission_stmt->get_result();
        
        if ($mission_result->num_rows === 0) {
            $mission_stmt->close();
            $conn->close();
            return false;
        }
        
        $mission = $mission_result->fetch_assoc();
        $mission_stmt->close();
        
        // Insert or update user mission progress
        $sql = "INSERT INTO user_missions (user_id, mission_id, current_progress, is_completed, completed_at) 
                VALUES (?, ?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                current_progress = LEAST(current_progress + ?, ?),
                is_completed = CASE WHEN current_progress + ? >= ? THEN 1 ELSE is_completed END,
                completed_at = CASE WHEN current_progress + ? >= ? AND completed_at IS NULL THEN NOW() ELSE completed_at END";
        
        $new_progress = $progress_increment;
        $is_completed = ($new_progress >= $mission['target_value']) ? 1 : 0;
        $completed_at = $is_completed ? date('Y-m-d H:i:s') : null;
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiiisiiiiii", 
            $user_id, $mission_id, $new_progress, $is_completed, $completed_at,
            $progress_increment, $mission['target_value'],
            $progress_increment, $mission['target_value'],
            $progress_increment, $mission['target_value']
        );
        
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Claim mission reward
     */
    public static function claimMissionReward($user_id, $mission_id) {
        $conn = getDatabaseConnection();
        
        // Get mission and user progress
        $sql = "SELECT m.reward_coins, m.reward_diamonds, m.reward_energy,
                       um.is_completed, um.is_claimed
                FROM missions m
                JOIN user_missions um ON m.id = um.mission_id
                WHERE m.id = ? AND um.user_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $mission_id, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $stmt->close();
            $conn->close();
            return ['success' => false, 'message' => 'Mission not found'];
        }
        
        $mission = $result->fetch_assoc();
        $stmt->close();
        
        // Check if mission is completed and not already claimed
        if (!$mission['is_completed']) {
            $conn->close();
            return ['success' => false, 'message' => 'Mission not completed'];
        }
        
        if ($mission['is_claimed']) {
            $conn->close();
            return ['success' => false, 'message' => 'Reward already claimed'];
        }
        
        // Start transaction
        $conn->autocommit(false);
        
        try {
            // Mark mission as claimed
            $claim_stmt = $conn->prepare("UPDATE user_missions SET is_claimed = 1, claimed_at = NOW() WHERE user_id = ? AND mission_id = ?");
            $claim_stmt->bind_param("ii", $user_id, $mission_id);
            $claim_stmt->execute();
            $claim_stmt->close();
            
            // Add rewards to user resources
            $rewards_given = [];
            
            if ($mission['reward_coins'] > 0) {
                ResourceManager::updateCoins($user_id, $mission['reward_coins'], 'add');
                $rewards_given['coins'] = $mission['reward_coins'];
            }
            
            if ($mission['reward_diamonds'] > 0) {
                ResourceManager::updateDiamonds($user_id, $mission['reward_diamonds'], 'add');
                $rewards_given['diamonds'] = $mission['reward_diamonds'];
            }
            
            if ($mission['reward_energy'] > 0) {
                ResourceManager::updateEnergy($user_id, $mission['reward_energy'], 'add');
                $rewards_given['energy'] = $mission['reward_energy'];
            }
            
            $conn->commit();
            $conn->close();
            
            return [
                'success' => true, 
                'message' => 'Reward claimed successfully',
                'rewards' => $rewards_given
            ];
            
        } catch (Exception $e) {
            $conn->rollback();
            $conn->close();
            return ['success' => false, 'message' => 'Failed to claim reward'];
        }
    }
    
    /**
     * Claim all available mission rewards
     */
    public static function claimAllRewards($user_id) {
        $missions = self::getUserMissions($user_id);
        $claimed_rewards = ['coins' => 0, 'diamonds' => 0, 'energy' => 0];
        $claimed_count = 0;
        
        foreach ($missions as $mission) {
            if ($mission['is_completed'] && !$mission['is_claimed']) {
                $result = self::claimMissionReward($user_id, $mission['id']);
                if ($result['success']) {
                    $claimed_count++;
                    if (isset($result['rewards'])) {
                        foreach ($result['rewards'] as $type => $amount) {
                            $claimed_rewards[$type] += $amount;
                        }
                    }
                }
            }
        }
        
        return [
            'success' => $claimed_count > 0,
            'claimed_count' => $claimed_count,
            'total_rewards' => $claimed_rewards
        ];
    }
}
?>
