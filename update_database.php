<?php
// Update database script - adds new columns for diamond pricing
require_once 'config/database.php';

echo "Updating database schema...\n";

$conn = getDatabaseConnection();

// Add new columns to store_items table
$alterQueries = [
    "ALTER TABLE store_items ADD COLUMN price_diamonds INT DEFAULT 0 AFTER price_usd",
    "ALTER TABLE store_items ADD COLUMN original_price_diamonds INT DEFAULT 0 AFTER original_price_usd"
];

foreach ($alterQueries as $query) {
    $result = $conn->query($query);
    if ($result) {
        echo "✓ Successfully executed: " . substr($query, 0, 50) . "...\n";
    } else {
        // Check if column already exists
        if (strpos($conn->error, 'Duplicate column name') !== false) {
            echo "✓ Column already exists: " . substr($query, 0, 50) . "...\n";
        } else {
            echo "✗ Error executing: " . substr($query, 0, 50) . "...\n";
            echo "Error: " . $conn->error . "\n";
        }
    }
}

$conn->close();

echo "Database schema update complete!\n";
echo "You can now run reset_store.php to initialize the new store items.\n";
?>
