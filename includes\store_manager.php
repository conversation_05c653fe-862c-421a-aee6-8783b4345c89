<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/resource_manager.php';

class StoreManager {
    
    /**
     * Initialize default store items
     */
    public static function initializeDefaultStoreItems() {
        $conn = getDatabaseConnection();
        
        // Check if store items already exist
        $check = $conn->query("SELECT COUNT(*) as count FROM store_items");
        $result = $check->fetch_assoc();
        
        if ($result['count'] > 0) {
            $conn->close();
            return; // Store items already initialized
        }
        
        // Default store items - Two-tier economy system
        $store_items = [
            // REAL MONEY PURCHASES - Diamond Packs Only
            [
                'name' => 'Starter Diamond Pack',
                'description' => 'Perfect for new players! Get 300 diamonds with first-time bonus.',
                'category' => 'diamonds',
                'item_type' => 'starter_pack',
                'price_usd' => 4.99,
                'price_diamonds' => 0, // Real money purchase
                'reward_diamonds' => 300,
                'bonus_percentage' => 20,
                'is_featured' => true,
                'image_url' => '../assets/store/starter_pack.png'
            ],
            [
                'name' => 'Monthly Diamond Card',
                'description' => 'Get 90 diamonds daily for 30 days! Best value for regular players.',
                'category' => 'diamonds',
                'item_type' => 'monthly_card',
                'price_usd' => 9.99,
                'price_diamonds' => 0, // Real money purchase
                'reward_diamonds' => 2700,
                'bonus_percentage' => 50,
                'purchase_limit' => 1,
                'is_featured' => true,
                'image_url' => '../assets/store/monthly_card.png'
            ],
            [
                'name' => 'Diamond Pack (Small)',
                'description' => 'Get 980 diamonds instantly.',
                'category' => 'diamonds',
                'item_type' => 'diamond_pack_small',
                'price_usd' => 14.99,
                'price_diamonds' => 0, // Real money purchase
                'reward_diamonds' => 980,
                'image_url' => '../assets/store/diamond_small.png'
            ],
            [
                'name' => 'Diamond Pack (Large)',
                'description' => 'Get 1980 diamonds with bonus!',
                'category' => 'diamonds',
                'item_type' => 'diamond_pack_large',
                'price_usd' => 29.99,
                'price_diamonds' => 0, // Real money purchase
                'original_price_usd' => 34.99,
                'discount_percentage' => 15,
                'reward_diamonds' => 1980,
                'bonus_percentage' => 25,
                'image_url' => '../assets/store/diamond_large.png'
            ],
            [
                'name' => 'Diamond Pack (Mega)',
                'description' => 'Get 6480 diamonds with huge bonus!',
                'category' => 'diamonds',
                'item_type' => 'diamond_pack_mega',
                'price_usd' => 99.99,
                'price_diamonds' => 0, // Real money purchase
                'original_price_usd' => 129.99,
                'discount_percentage' => 25,
                'reward_diamonds' => 6480,
                'bonus_percentage' => 100,
                'is_featured' => true,
                'image_url' => '../assets/store/diamond_mega.png'
            ],

            // DIAMOND PURCHASES - Energy Packs
            [
                'name' => 'Energy Refill (Small)',
                'description' => 'Instantly restore 120 energy points.',
                'category' => 'energy',
                'item_type' => 'energy_refill_small',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 50,
                'reward_energy' => 120,
                'image_url' => '../assets/store/energy_refill.png'
            ],
            [
                'name' => 'Energy Refill (Medium)',
                'description' => 'Instantly restore 300 energy points.',
                'category' => 'energy',
                'item_type' => 'energy_refill_medium',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 100,
                'reward_energy' => 300,
                'image_url' => '../assets/store/energy_refill.png'
            ],
            [
                'name' => 'Energy Refill (Large)',
                'description' => 'Instantly restore 600 energy points.',
                'category' => 'energy',
                'item_type' => 'energy_refill_large',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 180,
                'original_price_diamonds' => 200,
                'discount_percentage' => 10,
                'reward_energy' => 600,
                'image_url' => '../assets/store/energy_refill.png'
            ],
            [
                'name' => 'Energy Boost Pack',
                'description' => 'Get 1000 energy plus bonus coins.',
                'category' => 'energy',
                'item_type' => 'energy_boost',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 280,
                'reward_energy' => 1000,
                'reward_coins' => 50000,
                'is_featured' => true,
                'image_url' => '../assets/store/energy_boost.png'
            ],

            // DIAMOND PURCHASES - Character Items
            [
                'name' => 'Qistina Outfit Pack',
                'description' => 'Unlock exclusive Qistina outfit and get bonus coins.',
                'category' => 'characters',
                'item_type' => 'qistina_outfit',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 500,
                'reward_coins' => 100000,
                'is_featured' => true,
                'image_url' => '../assets/store/qistina_pack.png'
            ],
            [
                'name' => 'Zulaikha Character Pack',
                'description' => 'Unlock Zulaikha character with exclusive content.',
                'category' => 'characters',
                'item_type' => 'zulaikha_character',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 800,
                'reward_coins' => 150000,
                'is_featured' => true,
                'image_url' => '../assets/store/zulaikha_pack.png'
            ],
            [
                'name' => 'Premium Character Bundle',
                'description' => 'Unlock both characters with exclusive outfits.',
                'category' => 'characters',
                'item_type' => 'character_bundle',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 1200,
                'original_price_diamonds' => 1500,
                'discount_percentage' => 20,
                'reward_coins' => 300000,
                'bonus_percentage' => 25,
                'image_url' => '../assets/store/character_bundle.png'
            ],

            // DIAMOND PURCHASES - Items and Boosts
            [
                'name' => 'Coin Booster (Large)',
                'description' => 'Get 500,000 coins instantly.',
                'category' => 'items',
                'item_type' => 'coin_booster_large',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 200,
                'reward_coins' => 500000,
                'image_url' => '../assets/store/coin_booster.png'
            ],
            [
                'name' => 'Experience Booster',
                'description' => 'Double experience gain for 24 hours.',
                'category' => 'items',
                'item_type' => 'exp_booster',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 150,
                'reward_coins' => 0,
                'image_url' => '../assets/store/exp_booster.png'
            ],
            [
                'name' => 'Ultimate Resource Pack',
                'description' => 'Everything you need! Energy, coins, and boosters.',
                'category' => 'bundles',
                'item_type' => 'ultimate_bundle',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 800,
                'original_price_diamonds' => 1000,
                'discount_percentage' => 20,
                'reward_coins' => 1000000,
                'reward_energy' => 1000,
                'bonus_percentage' => 50,
                'time_limited' => true,
                'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days')),
                'is_featured' => true,
                'image_url' => '../assets/store/ultimate_bundle.png'
            ],

            // PREMIUM PASS
            [
                'name' => 'Starlight Premium Pass',
                'description' => 'Unlock premium rewards in the Starlight system for 30 days! Access exclusive premium tier rewards.',
                'category' => 'bundles',
                'item_type' => 'starlight_premium_pass',
                'price_usd' => 9.99, // Real money purchase
                'price_diamonds' => 0,
                'reward_diamonds' => 500, // Bonus diamonds with premium pass
                'bonus_percentage' => 0,
                'purchase_limit' => 1, // Can only buy once per month
                'is_featured' => true,
                'image_url' => '../assets/store/starlight_premium.png'
            ],
            [
                'name' => 'Starlight Premium Pass (Diamonds)',
                'description' => 'Unlock premium rewards in the Starlight system for 30 days! Purchase with diamonds.',
                'category' => 'bundles',
                'item_type' => 'starlight_premium_pass_diamonds',
                'price_usd' => 0, // Diamond purchase
                'price_diamonds' => 1500,
                'reward_diamonds' => 300, // Bonus diamonds with premium pass
                'bonus_percentage' => 0,
                'purchase_limit' => 1, // Can only buy once per month
                'is_featured' => false,
                'image_url' => '../assets/store/starlight_premium.png'
            ]
        ];
        
        $stmt = $conn->prepare("INSERT INTO store_items (name, description, category, item_type, price_usd, price_diamonds, original_price_usd, original_price_diamonds, discount_percentage, reward_diamonds, reward_coins, reward_energy, bonus_percentage, purchase_limit, time_limited, expires_at, is_featured, image_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        if (!$stmt) {
            die("Error preparing statement: " . $conn->error);
        }

        foreach ($store_items as $item) {
            $name = $item['name'];
            $description = $item['description'];
            $category = $item['category'];
            $item_type = $item['item_type'];
            $price_usd = isset($item['price_usd']) ? $item['price_usd'] : 0;
            $price_diamonds = isset($item['price_diamonds']) ? $item['price_diamonds'] : 0;
            $original_price_usd = $item['original_price_usd'] ?? null;
            $original_price_diamonds = isset($item['original_price_diamonds']) ? $item['original_price_diamonds'] : 0;
            $discount_percentage = isset($item['discount_percentage']) ? $item['discount_percentage'] : 0;
            $reward_diamonds = isset($item['reward_diamonds']) ? $item['reward_diamonds'] : 0;
            $reward_coins = isset($item['reward_coins']) ? $item['reward_coins'] : 0;
            $reward_energy = isset($item['reward_energy']) ? $item['reward_energy'] : 0;
            $bonus_percentage = isset($item['bonus_percentage']) ? $item['bonus_percentage'] : 0;
            $purchase_limit = isset($item['purchase_limit']) ? $item['purchase_limit'] : 0;
            $time_limited = isset($item['time_limited']) ? $item['time_limited'] : false;
            $expires_at = isset($item['expires_at']) ? $item['expires_at'] : null;
            $is_featured = isset($item['is_featured']) ? $item['is_featured'] : false;
            $image_url = isset($item['image_url']) ? $item['image_url'] : '';

            // Convert boolean and null values for binding
            $time_limited_int = $time_limited ? 1 : 0;
            $is_featured_int = $is_featured ? 1 : 0;
            $original_price_usd_val = $original_price_usd ?? 0.0;
            $expires_at_val = $expires_at ?? '';

            $stmt->bind_param("ssssdiidiiiiiisiss",
                $name,                    // s - 1
                $description,             // s - 2
                $category,                // s - 3
                $item_type,               // s - 4
                $price_usd,               // d - 5
                $price_diamonds,          // i - 6
                $original_price_usd_val,  // d - 7
                $original_price_diamonds, // i - 8
                $discount_percentage,     // i - 9
                $reward_diamonds,         // i - 10
                $reward_coins,            // i - 11
                $reward_energy,           // i - 12
                $bonus_percentage,        // i - 13
                $purchase_limit,          // i - 14
                $time_limited_int,        // i - 15
                $expires_at_val,          // s - 16
                $is_featured_int,         // i - 17
                $image_url                // s - 18
            );
            $stmt->execute();
        }
        
        $stmt->close();
        $conn->close();
    }
    
    /**
     * Get all store items by category
     */
    public static function getStoreItems($category = null, $featured_only = false) {
        $conn = getDatabaseConnection();
        
        // Initialize store items if they don't exist
        self::initializeDefaultStoreItems();
        
        $sql = "SELECT * FROM store_items WHERE is_active = 1";
        $params = [];
        $types = "";
        
        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
            $types .= "s";
        }
        
        if ($featured_only) {
            $sql .= " AND is_featured = 1";
        }
        
        // Check for expired time-limited items
        $sql .= " AND (time_limited = 0 OR expires_at IS NULL OR expires_at > NOW())";
        
        $sql .= " ORDER BY is_featured DESC, price_usd ASC";
        
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $items = [];
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $items;
    }
    
    /**
     * Get store item by ID
     */
    public static function getStoreItem($item_id) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("SELECT * FROM store_items WHERE id = ? AND is_active = 1");
        $stmt->bind_param("i", $item_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $item = null;
        if ($result->num_rows > 0) {
            $item = $result->fetch_assoc();
        }
        
        $stmt->close();
        $conn->close();
        
        return $item;
    }
    
    /**
     * Create a purchase record
     */
    public static function createPurchase($user_id, $store_item_id, $transaction_id, $payment_method, $amount_usd, $payment_data = null) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("INSERT INTO user_purchases (user_id, store_item_id, transaction_id, payment_method, amount_usd, payment_data) VALUES (?, ?, ?, ?, ?, ?)");
        $payment_data_json = $payment_data ? json_encode($payment_data) : null;
        $stmt->bind_param("iissds", $user_id, $store_item_id, $transaction_id, $payment_method, $amount_usd, $payment_data_json);
        
        $success = $stmt->execute();
        $purchase_id = $success ? $conn->insert_id : null;
        
        $stmt->close();
        $conn->close();
        
        return $purchase_id;
    }
    
    /**
     * Purchase item with diamonds (in-game currency)
     */
    public static function purchaseWithDiamonds($user_id, $store_item_id) {
        $conn = getDatabaseConnection();

        // Get item details
        $stmt = $conn->prepare("SELECT * FROM store_items WHERE id = ? AND is_active = 1");
        $stmt->bind_param("i", $store_item_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            $stmt->close();
            $conn->close();
            return ['success' => false, 'message' => 'Item not found'];
        }

        $item = $result->fetch_assoc();
        $stmt->close();

        // Check if item can be purchased with diamonds
        if ($item['price_diamonds'] <= 0) {
            $conn->close();
            return ['success' => false, 'message' => 'This item can only be purchased with real money'];
        }

        // Check if user has enough diamonds
        $user_resources = ResourceManager::getUserResources($user_id);
        if ($user_resources['diamonds'] < $item['price_diamonds']) {
            $conn->close();
            return ['success' => false, 'message' => 'Insufficient diamonds'];
        }

        // Start transaction
        $conn->autocommit(false);

        try {
            // Deduct diamonds
            $success = ResourceManager::updateDiamonds($user_id, $item['price_diamonds'], 'subtract');
            if (!$success) {
                throw new Exception('Failed to deduct diamonds');
            }

            // Create purchase record
            $transaction_id = 'DIAMOND_' . time() . '_' . $user_id . '_' . $store_item_id;
            $purchase_stmt = $conn->prepare("INSERT INTO user_purchases (user_id, store_item_id, transaction_id, payment_method, amount_usd, status) VALUES (?, ?, ?, 'diamonds', 0, 'completed')");
            $purchase_stmt->bind_param("iis", $user_id, $store_item_id, $transaction_id);
            $purchase_stmt->execute();
            $purchase_stmt->close();

            // Deliver rewards
            $rewards_delivered = [];

            if ($item['reward_diamonds'] > 0) {
                ResourceManager::updateDiamonds($user_id, $item['reward_diamonds'], 'add');
                $rewards_delivered['diamonds'] = $item['reward_diamonds'];
            }

            if ($item['reward_coins'] > 0) {
                ResourceManager::updateCoins($user_id, $item['reward_coins'], 'add');
                $rewards_delivered['coins'] = $item['reward_coins'];
            }

            if ($item['reward_energy'] > 0) {
                ResourceManager::updateEnergy($user_id, $item['reward_energy'], 'add');
                $rewards_delivered['energy'] = $item['reward_energy'];
            }

            // Handle special item types
            if ($item['item_type'] === 'starlight_premium_pass' || $item['item_type'] === 'starlight_premium_pass_diamonds') {
                // Activate premium status for 30 days
                require_once __DIR__ . '/starlight_manager.php';
                $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
                StarlightManager::updatePremiumStatus($user_id, true, $expires_at);
                $rewards_delivered['premium_activated'] = true;
            }

            $conn->commit();
            $conn->close();

            return [
                'success' => true,
                'message' => 'Purchase completed successfully',
                'item_name' => $item['name'],
                'diamonds_spent' => $item['price_diamonds'],
                'rewards' => $rewards_delivered
            ];

        } catch (Exception $e) {
            $conn->rollback();
            $conn->close();
            return ['success' => false, 'message' => 'Failed to complete purchase: ' . $e->getMessage()];
        }
    }

    /**
     * Complete a purchase and deliver rewards (for real money purchases)
     */
    public static function completePurchase($purchase_id) {
        $conn = getDatabaseConnection();
        
        // Get purchase details
        $stmt = $conn->prepare("
            SELECT p.*, s.reward_diamonds, s.reward_coins, s.reward_energy, s.name as item_name
            FROM user_purchases p 
            JOIN store_items s ON p.store_item_id = s.id 
            WHERE p.id = ? AND p.status = 'pending'
        ");
        $stmt->bind_param("i", $purchase_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $stmt->close();
            $conn->close();
            return ['success' => false, 'message' => 'Purchase not found or already processed'];
        }
        
        $purchase = $result->fetch_assoc();
        $stmt->close();
        
        // Start transaction
        $conn->autocommit(false);
        
        try {
            // Update purchase status
            $update_stmt = $conn->prepare("UPDATE user_purchases SET status = 'completed' WHERE id = ?");
            $update_stmt->bind_param("i", $purchase_id);
            $update_stmt->execute();
            $update_stmt->close();
            
            // Deliver rewards
            $rewards_delivered = [];

            if ($purchase['reward_diamonds'] > 0) {
                ResourceManager::updateDiamonds($purchase['user_id'], $purchase['reward_diamonds'], 'add');
                $rewards_delivered['diamonds'] = $purchase['reward_diamonds'];
            }

            if ($purchase['reward_coins'] > 0) {
                ResourceManager::updateCoins($purchase['user_id'], $purchase['reward_coins'], 'add');
                $rewards_delivered['coins'] = $purchase['reward_coins'];
            }

            if ($purchase['reward_energy'] > 0) {
                ResourceManager::updateEnergy($purchase['user_id'], $purchase['reward_energy'], 'add');
                $rewards_delivered['energy'] = $purchase['reward_energy'];
            }

            // Get item details for special handling
            $item_stmt = $conn->prepare("SELECT item_type FROM store_items WHERE id = ?");
            $item_stmt->bind_param("i", $purchase['store_item_id']);
            $item_stmt->execute();
            $item_result = $item_stmt->get_result();
            if ($item_result->num_rows > 0) {
                $item_data = $item_result->fetch_assoc();

                // Handle special item types
                if ($item_data['item_type'] === 'starlight_premium_pass' || $item_data['item_type'] === 'starlight_premium_pass_diamonds') {
                    // Activate premium status for 30 days
                    require_once __DIR__ . '/starlight_manager.php';
                    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
                    StarlightManager::updatePremiumStatus($purchase['user_id'], true, $expires_at);
                    $rewards_delivered['premium_activated'] = true;
                }
            }
            $item_stmt->close();
            
            $conn->commit();
            $conn->close();
            
            return [
                'success' => true,
                'message' => 'Purchase completed successfully',
                'item_name' => $purchase['item_name'],
                'rewards' => $rewards_delivered
            ];
            
        } catch (Exception $e) {
            $conn->rollback();
            $conn->close();
            return ['success' => false, 'message' => 'Failed to complete purchase'];
        }
    }
    
    /**
     * Get user purchase history
     */
    public static function getUserPurchases($user_id, $limit = 10) {
        $conn = getDatabaseConnection();
        
        $stmt = $conn->prepare("
            SELECT p.*, s.name as item_name, s.category 
            FROM user_purchases p 
            JOIN store_items s ON p.store_item_id = s.id 
            WHERE p.user_id = ? 
            ORDER BY p.purchased_at DESC 
            LIMIT ?
        ");
        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $purchases = [];
        while ($row = $result->fetch_assoc()) {
            $purchases[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $purchases;
    }
}
?>
