# Shared JavaScript Functions Documentation

## Overview

This document explains the shared JavaScript functionality implemented across all pages in the story game. The shared functions provide consistent behavior for resource management, navigation, and UI interactions.

## Files Structure

```
MenuButton/
├── shared-functions.js          # Main shared functions file
├── shared-styles.css           # Shared CSS styles for + buttons
├── gacha.php                   # Uses shared functions
├── store.php                   # Uses shared functions
├── Starlight.php              # Uses shared functions
├── Mission.php                # Uses shared functions
├── Character.php              # Uses shared functions
└── menu/home.php              # Uses shared functions (with path override)
```

## Core Functions

### 1. goToStore(resourceType)
**Purpose**: Navigate to store page with specific resource filter
**Parameters**: 
- `resourceType` (string): 'coins', 'diamonds', 'energy', or 'tickets'

**Usage**:
```javascript
// Called when + button is clicked
<div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
```

**Features**:
- Smooth animation effect on button click
- Automatic navigation to store.php with resource parameter
- Fallback navigation without animation if event is not available

### 2. updateResourceDisplay(resources)
**Purpose**: Update resource counts displayed in the top navigation
**Parameters**:
- `resources` (object): Contains coins, diamonds, energy values

**Usage**:
```javascript
updateResourceDisplay({
    coins: 1500,
    diamonds: 250,
    energy: 40
});
```

### 3. updateTicketDisplay(ticketType, newCount)
**Purpose**: Update ticket counts for gacha system
**Parameters**:
- `ticketType` (string): 'standard', 'premium', or 'limited'
- `newCount` (number): New ticket count

**Usage**:
```javascript
updateTicketDisplay('standard', 5);
```

### 4. updateUserResources()
**Purpose**: Fetch latest resource data from server and update display
**Usage**:
```javascript
// Automatically called every 30 seconds
// Can be manually called after purchases/rewards
await updateUserResources();
```

### 5. showNotification(message, type)
**Purpose**: Display notification messages to user
**Parameters**:
- `message` (string): Message to display
- `type` (string): 'success', 'error', 'warning', or 'info'

**Usage**:
```javascript
showNotification('Purchase successful!', 'success');
showNotification('Insufficient funds', 'error');
```

## Utility Functions

### formatNumber(num)
Format numbers with commas for better readability
```javascript
formatNumber(1234567); // Returns "1,234,567"
```

### animateScale(element, duration)
Apply scale animation to any element
```javascript
animateScale(document.getElementById('myButton'), 300);
```

### setButtonLoading(button, loadingText) / removeButtonLoading(button, originalText)
Manage button loading states
```javascript
const originalText = setButtonLoading(button, 'Processing...');
// ... do async operation
removeButtonLoading(button, originalText);
```

### apiRequest(url, options)
Make API requests with error handling
```javascript
const result = await apiRequest('../api/resources.php', {
    method: 'POST',
    body: JSON.stringify({ action: 'purchase' })
});
```

## Implementation in Pages

### Standard Implementation
Most pages include the shared functions like this:
```html
<!-- Include shared functions -->
<script src="shared-functions.js"></script>
```

### Home Page Special Case
The home page needs a path override since it's in a different directory:
```html
<!-- Include shared functions -->
<script src="../MenuButton/shared-functions.js"></script>

<script>
// Override goToStore for home page to use correct path
function goToStore(resourceType) {
    // ... animation code ...
    window.location.href = `../MenuButton/store.php?resource=${resourceType}`;
}
</script>
```

### Gacha Page Special Case
The gacha page overrides updateTicketDisplay to include additional functionality:
```javascript
// Update ticket display (override shared function to include updateButtonStates)
function updateTicketDisplay(ticketType, newCount) {
    const topTicketElement = document.getElementById(`${ticketType}-tickets`);
    if (topTicketElement) {
        topTicketElement.textContent = newCount;
    }
    currentTickets[ticketType] = newCount;
    updateButtonStates(); // Gacha-specific function
}
```

## HTML Structure Required

### Resource Items with + Buttons
```html
<div class="resource-item">
    <span class="resource-icon">💎</span>
    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
</div>
```

### Notification Container
```html
<div id="notification" class="notification"></div>
```

## CSS Classes Used

- `.resource-item`: Container for resource display
- `.resource-icon`: Icon for the resource
- `.plus-btn`: The + button styling
- `.notification`: Notification message container

## Automatic Features

### Auto-initialization
The shared functions automatically:
- Add click animations to all + buttons
- Add hover effects to resource items
- Start auto-refresh of resources every 30 seconds

### Event Listeners
Automatically added on DOM load:
- Click animations for `.plus-btn` elements
- Hover effects for `.resource-item` elements
- Resource auto-refresh timer

## Benefits

1. **Code Reusability**: Single implementation used across all pages
2. **Consistency**: Same behavior and animations everywhere
3. **Maintainability**: Changes in one file affect all pages
4. **Performance**: Cached JavaScript file reduces load times
5. **Error Handling**: Centralized error handling for API calls
6. **User Experience**: Consistent animations and feedback

## Future Enhancements

The shared functions system can be easily extended with:
- Additional resource types
- More animation effects
- Enhanced error handling
- Offline support
- Real-time updates via WebSocket
- Internationalization support
