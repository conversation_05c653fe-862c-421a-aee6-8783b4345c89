<?php
// Setup script to initialize missions and test data
require_once 'config/database.php';
require_once 'includes/mission_manager.php';

echo "Setting up missions...\n";

// Initialize default missions
MissionManager::initializeDefaultMissions();

echo "Default missions initialized!\n";

// Add some test progress for demonstration
if (isset($_GET['user_id'])) {
    $user_id = (int)$_GET['user_id'];
    
    echo "Adding test progress for user ID: $user_id\n";
    
    // Complete first mission
    MissionManager::updateMissionProgress($user_id, 1, 1);
    
    // Partially complete second mission
    MissionManager::updateMissionProgress($user_id, 2, 1);
    
    // Complete third mission
    MissionManager::updateMissionProgress($user_id, 3, 1);
    
    echo "Test progress added!\n";
}

echo "Setup complete! You can now visit the mission page.\n";
echo "To add test progress, visit: setup_missions.php?user_id=YOUR_USER_ID\n";
?>
