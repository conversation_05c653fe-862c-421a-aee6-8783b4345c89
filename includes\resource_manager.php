<?php
require_once __DIR__ . '/../config/database.php';

class ResourceManager {
    
    /**
     * Initialize default resources for a new user
     */
    public static function initializeUserResources($user_id) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("INSERT INTO user_resources (user_id, coins, diamonds, energy) VALUES (?, 1000, 1000, 40)");
        $stmt->bind_param("i", $user_id);

        $success = $stmt->execute();
        $stmt->close();
        $conn->close();

        return $success;
    }
    
    /**
     * Get user resources
     */
    public static function getUserResources($user_id) {
        $conn = getDatabaseConnection();

        $stmt = $conn->prepare("SELECT coins, diamonds, energy FROM user_resources WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $resources = $result->fetch_assoc();
        } else {
            // If no resources found, initialize with defaults
            self::initializeUserResources($user_id);
            $resources = [
                'coins' => 1000,
                'diamonds' => 1000,
                'energy' => 40
            ];
        }

        $stmt->close();
        $conn->close();

        return $resources;
    }
    
    /**
     * Update user coins
     */
    public static function updateCoins($user_id, $amount, $operation = 'add') {
        $conn = getDatabaseConnection();
        
        if ($operation === 'add') {
            $sql = "UPDATE user_resources SET coins = coins + ? WHERE user_id = ?";
        } else if ($operation === 'subtract') {
            $sql = "UPDATE user_resources SET coins = GREATEST(0, coins - ?) WHERE user_id = ?";
        } else {
            $sql = "UPDATE user_resources SET coins = ? WHERE user_id = ?";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $amount, $user_id);
        
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Update user diamonds
     */
    public static function updateDiamonds($user_id, $amount, $operation = 'add') {
        $conn = getDatabaseConnection();
        
        if ($operation === 'add') {
            $sql = "UPDATE user_resources SET diamonds = diamonds + ? WHERE user_id = ?";
        } else if ($operation === 'subtract') {
            $sql = "UPDATE user_resources SET diamonds = GREATEST(0, diamonds - ?) WHERE user_id = ?";
        } else {
            $sql = "UPDATE user_resources SET diamonds = ? WHERE user_id = ?";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $amount, $user_id);
        
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Update user energy
     */
    public static function updateEnergy($user_id, $amount, $operation = 'add') {
        $conn = getDatabaseConnection();
        
        if ($operation === 'add') {
            $sql = "UPDATE user_resources SET energy = energy + ? WHERE user_id = ?";
        } else if ($operation === 'subtract') {
            $sql = "UPDATE user_resources SET energy = GREATEST(0, energy - ?) WHERE user_id = ?";
        } else {
            $sql = "UPDATE user_resources SET energy = ? WHERE user_id = ?";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $amount, $user_id);
        
        $success = $stmt->execute();
        $stmt->close();
        $conn->close();
        
        return $success;
    }
    
    /**
     * Check if user has enough resources for a purchase
     */
    public static function canAfford($user_id, $coins_needed = 0, $diamonds_needed = 0) {
        $resources = self::getUserResources($user_id);
        
        return ($resources['coins'] >= $coins_needed && $resources['diamonds'] >= $diamonds_needed);
    }
    
    /**
     * Process a purchase (deduct resources)
     */
    public static function processPurchase($user_id, $coins_cost = 0, $diamonds_cost = 0) {
        if (!self::canAfford($user_id, $coins_cost, $diamonds_cost)) {
            return false;
        }
        
        $success = true;
        
        if ($coins_cost > 0) {
            $success = $success && self::updateCoins($user_id, $coins_cost, 'subtract');
        }
        
        if ($diamonds_cost > 0) {
            $success = $success && self::updateDiamonds($user_id, $diamonds_cost, 'subtract');
        }
        
        return $success;
    }
}
?>
