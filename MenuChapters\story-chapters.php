<?php
// Start session to track user progress
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit;
}

// Database connection
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'visual_novel_db';

// Connect to database
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Get unlocked chapters for this user
$unlocked_chapters = [];
$sql = "SELECT chapter_id FROM user_progress WHERE user_id = ? AND completed = TRUE";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $unlocked_chapters[] = $row['chapter_id'];
}

// Define all chapters
$chapters = [
    // Chapter 1 episodes
    '1' => [
        ['id' => '01-01', 'title' => 'First Meeting', 'thumbnail' => '../assets/episode1-thumbnail.png', 'url' => '../episode1/game-interface.html'],
        ['id' => '01-02', 'title' => 'Getting to Know You', 'thumbnail' => '../assets/episode2-thumbnail.webp', 'url' => '../episode1/game-interface2.html'],
        ['id' => '01-03', 'title' => 'The Park Date', 'thumbnail' => '../assets/episode3-thumbnail.webp', 'url' => '../episode1/game-interface3.html'],
        ['id' => '01-04', 'title' => 'Coffee Shop Talk', 'thumbnail' => '../assets/episode4-thumbnail.png', 'url' => '../episode1/game-interface4.html'],
        ['id' => '01-05', 'title' => 'Evening Walk', 'thumbnail' => '../assets/episode5-thumbnail.png', 'url' => '../episode1/game-interface5.html'],
        ['id' => '01-06', 'title' => 'First Kiss', 'thumbnail' => '../assets/episode6-thumbnail.png', 'url' => '../episode1/game-interface6.html']
    ]
];

// Function to check if chapter is unlocked
function isChapterUnlocked($chapter_id, $unlocked_chapters) {
    // First chapter is always unlocked
    if ($chapter_id === '01-01') {
        return true;
    }
    
    // Check if chapter is in unlocked list
    return in_array($chapter_id, $unlocked_chapters);
}

// Get current chapter from query parameter or default to 1
$currentChapter = isset($_GET['chapter']) ? intval($_GET['chapter']) : 1;
if ($currentChapter < 1 || $currentChapter > 2) {
    $currentChapter = 1;
}

// Total number of chapters
$totalChapters = 2;
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Story Chapters</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link rel="stylesheet" href="../MenuButton/shared-styles.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Montserrat', sans-serif;
      overflow: hidden;
      height: 100vh;
    }
    
    @keyframes roomAmbience {
      0% {
        background-position: center;
        filter: brightness(1);
      }
      25% {
        background-position: calc(50% + 5px) calc(50% - 5px);
        filter: brightness(1.02);
      }
      50% {
        background-position: calc(50% + 10px) calc(50% - 3px);
        filter: brightness(1.05);
      }
      75% {
        background-position: calc(50% + 5px) calc(50% + 5px);
        filter: brightness(1.02);
      }
      100% {
        background-position: center;
        filter: brightness(1);
      }
    }

    #story-container {
      position: relative;
      width: 100%;
      height: 100%;
      background-image: url('../assets/city-background.png');
      background-size: cover;
      background-position: center;
      animation: roomAmbience 7s ease-in-out infinite;
      transition: background-image 0.5s ease;
    }
    
    #story-container.chapter2 {
      background-image: url('../assets/city-background2.png');
    }
    
    /* Top navigation */
    .top-nav {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      display: flex;
      padding: 20px;
      justify-content: space-between;
      background: linear-gradient(to bottom, rgba(102, 10, 78, 0.5), rgba(193, 14, 248, 0));
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      color: white;
      z-index: 10;
    }
  
    
    /* Add responsive adjustments */
    @media (max-width: 768px) {
      .top-nav {
        padding: 15px;
      }
      
      .back-text {
        font-size: 14px;
      }
    }
    
    /* Day display in topnav */
    .day-display {
      display: flex;
      align-items: center;
    }
    
    .top-nav .day-number {
      font-size: 42px;
      font-weight: 800;
      text-shadow: 0 3px 6px rgba(0,0,0,0.4);
      background: linear-gradient(to bottom, #fff, #e0e0e0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
      vertical-align: middle;
      line-height: 1;
    }
    
    .top-nav .month-name {
      font-size: 24px;
      font-weight: 600;
      letter-spacing: 1px;
      color: rgba(239, 239, 239, 0.9);
      text-transform: uppercase;
      margin-left: 10px;
      display: inline-block;
      vertical-align: middle;
      line-height: 1;
    }
    
    .time-indicator {
      align-items: center;
      margin-top: 8px;
      background: rgba(0,0,0,0.2);
      padding: 5px 12px;
      border-radius: 20px;
      border: 1px solid rgba(255,255,255,0.1);
      margin-left: 15px;
    }
    
    .time-icon {
      margin-right: 8px;
      font-size: 18px;
    }
    
    .time-text {
      font-size: 18px;
      font-weight: 500;
      color: rgba(255,255,255,0.9);
    }
    
    /* Chapter selection - redesigned to be simple horizontal cards */
    .chapter-selection {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 0;
      transition: opacity 0.5s ease;
    }

    .chapter-track {
      display: flex;
      padding: 30px 0;
      overflow-x: auto;
      width: 90%;
      justify-content: flex-start;
      padding-left: 20px;
      -ms-overflow-style: none;
      scrollbar-width: none;
      scroll-behavior: smooth;
    }

    .chapter-track::-webkit-scrollbar {
      display: none;
    }

    .chapter-card {
      width: 200px;
      height: 270px;
      margin: 0 15px;
      background: #fff;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .chapter-card:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    }

    .chapter-card img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .chapter-number {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 12px;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }

    .chapter-locked {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
    }

    @keyframes lockPulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    .lock-icon {
      width: 60px;
      height: 60px;
      background: url('../assets/icons/lock.png');
      background-size: cover;
      animation: lockPulse 2s infinite;
    }

    .chapter-unlocked {
      position: absolute;
      bottom: 10px;
      right: 10px;
      width: 30px;
      height: 30px;
      background: url('../assets/icons/unlock.png');
      background-size: cover;
      z-index: 5;
    }
    
    /* Navigation arrows for chapter track */
    .chapter-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      background: rgba(255,255,255,0.3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      cursor: pointer;
      z-index: 20;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }
    
      .chapter-nav-left {
      left: 20px;
    }

    .chapter-nav-left:hover {
      left: 20px;
      box-shadow: 0 8px 20px rgba(255,77,126,0.5);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    
    .chapter-nav-right {
      right: 20px;
    }
    
    /* Navigation buttons */
    .chapter-navigation {
      position: absolute;
      bottom: 30px;
      left: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
    
    .chapter-controls {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .nav-button {
      width: 50px;
      height: 50px;
      background: rgba(255,255,255,0.2);
      backdrop-filter: blur(5px);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 10px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    }
    
    .nav-button:hover {
      background: rgba(255,255,255,0.3);
    }

    
    .nav-icon {
      color: white;
      font-size: 20px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .next-chapter {
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 8px 15px;
      border-radius: 5px;
      font-size: 14px;
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.3s ease;
      pointer-events: none;
    }
    
    .next-button:hover + .next-chapter,
    .next-button:hover ~ .next-chapter {
      opacity: 1;
      transform: translateY(0);
    }
    
    .chapter-button {
      background: linear-gradient(to bottom, #4a69bd, #1e3799);
      color: #fff;
      padding: 12px 35px;
      border-radius: 8px;
      font-size: 20px;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6px 0 #0c2461, 0 8px 15px rgba(0,0,0,0.4);
      min-width: 180px;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
      border: 2px solid rgba(255,255,255,0.2);
      text-shadow: 0 2px 3px rgba(0,0,0,0.5);
    }
    
    .chapter-number-display {
      position: relative;
      z-index: 2;
      display: inline-block;
      padding: 0 5px;
    }  

    /* Add romantic RPG game styling */
    :root {
      --primary-color: #4a90e2;
      --secondary-color: #ff4d7e; /* Changed from #8a2be2 */
      --accent-color: #ff9e80;
      --text-shadow: 0 2px 4px rgba(0,0,0,0.5);
      --box-shadow: 0 8px 20px rgba(0,0,0,0.4);
    }

    body {
      font-family: 'Montserrat', sans-serif;
      background: #000;
    }

    #story-container {
      position: relative;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      animation: roomAmbience 10s ease-in-out infinite;
      transition: all 0.8s ease;
      overflow: hidden;
    }

    #story-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1;
    }

    /* Floating particles effect */
    @keyframes floatingParticles {
      0% { transform: translateY(0) rotate(0deg); opacity: 0; }
      50% { opacity: 0.8; }
      100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
    }

    .particles {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 2;
      pointer-events: none;
    }

    .particle {
      position: absolute;
      bottom: -10px;
      background-image: url('../assets/icons/heart-particle.png');
      background-size: contain;
      background-repeat: no-repeat;
      width: 15px;
      height: 15px;
      opacity: 0;
      animation: floatingParticles 15s linear infinite;
    }

    /* Enhanced top navigation */
    .top-nav {
      background: linear-gradient(to bottom, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
      border-bottom: 1px solid rgba(255,255,255,0.1);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      padding: 15px 25px;
      z-index: 100;
    }



    /* Enhanced user info */
    .user-info {
      background: linear-gradient(to right, rgba(0,0,0,0.5), rgba(255,77,126,0.3)); /* Changed from purple */
      padding: 10px 20px;
      border-radius: 15px;
      border: 1px solid rgba(255,255,255,0.15);
      box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .user-info span {
      font-weight: 600;
      letter-spacing: 0.5px;
      color: #fff;
      text-shadow: var(--text-shadow);
      margin-right: 15px;
    }



    /* Enhanced chapter cards */
    .chapter-card {
      width: 220px;
      height: 300px;
      margin: 0 20px;
      border-radius: 15px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.5);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      border: 3px solid rgba(255,255,255,0.1);
      overflow: hidden;
    }

    .chapter-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, transparent 70%, rgba(0,0,0,0.8));
      z-index: 2;
      pointer-events: none;
    }

    .chapter-card:hover {
      transform: translateY(-15px) scale(1.05);
      box-shadow: 0 15px 35px rgba(0,0,0,0.6);
      border-color: var(--primary-color);
    }

    .chapter-number {
      background: linear-gradient(to right, rgba(0,0,0,0.8), rgba(138,43,226,0.8));
      padding: 15px;
      font-size: 18px;
      font-weight: 700;
      letter-spacing: 1px;
      text-shadow: var(--text-shadow);
    }

    /* Enhanced chapter navigation */
    .chapter-button {
      background: linear-gradient(to bottom, #4a69bd, #1e3799);
      padding: 15px 40px;
      border-radius: 12px;
      font-size: 22px;
      font-weight: 800;
      box-shadow: 0 8px 0 #0c2461, 0 12px 25px rgba(0,0,0,0.5);
      text-transform: uppercase;
      letter-spacing: 2px;
      border: 2px solid rgba(255,255,255,0.2);
      text-shadow: 0 3px 5px rgba(0,0,0,0.5);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .nav-button {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(138,43,226,0.3));
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-radius: 50%;
      box-shadow: 0 8px 20px rgba(0,0,0,0.4);
      border: 2px solid rgba(255,255,255,0.1);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .nav-button:hover {
      background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(138,43,226,0.5));
      box-shadow: 0 12px 25px rgba(0,0,0,0.5);
    }

    .nav-icon {
      font-size: 24px;
      text-shadow: var(--text-shadow);
    }

    /* Enhanced chapter track navigation */
    .chapter-nav {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      border-radius: 50%;
      font-size: 22px;
      font-weight: bold;
      box-shadow: 0 5px 15px rgba(0,0,0,0.4);
      border: 2px solid rgba(255,255,255,0.2);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }


    /* Enhanced chapter cards with new color scheme for Chapter 1 */
    #chapter1-selection .chapter-card {
      width: 220px;
      height: 300px;
      margin: 0 20px;
      border-radius: 15px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.5);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      border: 3px solid rgba(255,255,255,0.1);
      overflow: hidden;
      position: relative;
    }

    #chapter1-selection .chapter-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, transparent 70%, rgba(0,0,0,0.8));
      z-index: 2;
      pointer-events: none;
    }

    #chapter1-selection .chapter-card:hover {
      transform: translateY(-15px) scale(1.05);
      box-shadow: 0 15px 35px rgba(138,43,226,0.6);
      border-color: #8a2be2;
    }

    #chapter1-selection .chapter-number {
      background: linear-gradient(to right, rgba(0,0,0,0.8), rgba(138,43,226,0.8));
      padding: 15px;
      font-size: 18px;
      font-weight: 700;
      letter-spacing: 1px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }

    /* Chapter 1 navigation styling */
    #chapter1-selection .chapter-nav {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #ff4d7e, #d81b60); /* Changed from purple */
      border-radius: 50%;
      font-size: 22px;
      font-weight: bold;
      box-shadow: 0 5px 15px rgba(255,77,126,0.4); /* Changed from purple */
      border: 2px solid rgba(255,255,255,0.2);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    #chapter1-selection .chapter-nav:hover {
      box-shadow: 0 8px 20px rgba(255,77,126,0.5); /* Changed from purple */
    }

    /* Chapter 1 locked chapter styling */
    #chapter1-selection .chapter-locked {
      background: rgba(0,0,0,0.7);
      backdrop-filter: blur(3px);
      border-radius: 12px;
    }

    #chapter1-selection .lock-icon {
      filter: drop-shadow(0 0 5px rgba(138,43,226,0.7));
    }

    /* Enhanced chapter button for Chapter 1 */
    .chapter-button {
      background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(50,0,80,0.5));
      color: rgba(171, 25, 162, 0.9);
      border: 2px solid;
      border-image: linear-gradient(45deg, #ff0000, #4169e1, #8a2be2, #ffff00) 1;
      padding: 15px 40px;
      border-radius: 12px;
      font-size: 22px;
      font-weight: 800;
      box-shadow: 0 8px 0 #b71c50, 0 12px 25px rgba(0,0,0,0.5); /* Changed shadow color */
      text-transform: uppercase;
      letter-spacing: 2px;
      text-shadow: 0 3px 5px rgba(0,0,0,0.5);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .chapter-button:active {
      transform: translateY(3px);
      box-shadow: 0 5px 0 #2e1065, 0 8px 15px rgba(0,0,0,0.4);
    }

    .chapter-number-display {
      position: relative;
      z-index: 2;
      display: inline-block;
      padding: 0 5px;
    }

    
    .chapter-number-display::after {
      content: '';
      position: absolute;
      margin: 0 5px;
      top: 50%;
      width: 15px;
      height: 5px;
      background: rgba(3, 7, 29, 0.7);
    }



    /* Enhanced nav button hover effects */
    .nav-button {
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
    }

    .nav-button:hover {
      background: rgba(255,255,255,0.3);
      box-shadow: 0 8px 15px rgba(0,0,0,0.3);
    }

    .nav-button:active {
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .nav-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .nav-button:hover::before {
      opacity: 1;
    }

    /* Enhanced chapter nav hover effects */
    .chapter-nav {
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .chapter-nav:hover {
      box-shadow: 0 8px 20px rgba(255,77,126,0.5);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

  </style>
</head>
<body>
  <div id="story-container" class="<?php echo $currentChapter === 2 ? 'chapter2' : ''; ?>">
    <!-- Particle effects -->
    <div class="particles" id="particles-container"></div>
    
    <!-- Top navigation -->
    <div class="top-nav">
            <?php
      // Determine day and time based on unlocked chapters
      $day = '<span class="day-number">26</span>';
      $month = '<span class="month-name">May</span>';
      $timeOfDay = '<div class="time-indicator"><span class="time-icon">☀️</span><span class="time-text">Morning</span></div>';
      
      if(in_array('01-02', $unlocked_chapters)) {
        $timeOfDay = '<div class="time-indicator"><span class="time-icon">🌙</span><span class="time-text">Night</span></div>';
      }
      if(in_array('01-03', $unlocked_chapters)) {
        $day = '<span class="day-number">27</span>';
        $timeOfDay = '<div class="time-indicator"><span class="time-icon"day>☀️</span><span class="time-text">Morning</span></div>';
      }
      if(in_array('01-04', $unlocked_chapters)) {
        $timeOfDay = '<div class="time-indicator"><span class="time-icon">🕒</span><span class="time-text">Afternoon</span></div>';
      }
      if(in_array('01-05', $unlocked_chapters)) {
        $timeOfDay = '<div class="time-indicator"><span class="time-icon">🌅</span><span class="time-text">Evening</span></div>';
      }
      if(in_array('01-06', $unlocked_chapters)) {
        $day = '<span class="day-number">28</span>';
        $timeOfDay = '<div class="time-indicator"><span class="time-icon">☀️</span><span class="time-text">Morning</span></div>';
      }
      
      // Determine background image based on time of day
      $backgroundImage = '../assets/chapterbakday.png'; // Default morning background
      if($timeOfDay == '<div class="time-indicator"><span class="time-icon">🌙</span><span class="time-text">Night</span></div>') {
        $backgroundImage = '../assets/chapterbaknight.png';
      }else if($timeOfDay == '<div class="time-indicator"><span class="time-icon">🕒</span><span class="time-text">Afternoon</span></div>') {
        $backgroundImage = '../assets/chapterbakafteroon.png';
      }
      
      // Apply background image
      echo "<script>document.getElementById('story-container').style.backgroundImage = 'url(\"$backgroundImage\")';</script>";
      ?>
      <!-- Day display and back button (left-aligned) -->
      <div style="display: flex; align-items: center; gap: 20px;">
        <a href="../menu/home.php" class="back-btn style-large">
          <i class="fas fa-arrow-left"></i> 
        </a>
        <div class="day-display">
          <?php echo $day; ?>
          <?php echo $month; ?>
        </div>
        <div class="time-of-day"><?php echo $timeOfDay; ?></div>
      </div>
      <!-- Center spacer -->
      <div style="flex: 1;"></div>
    </div>
    
    <!-- Chapter selection -->
    <div id="chapter1-selection" class="chapter-selection" style="<?php echo $currentChapter === 2 ? 'display: none;' : ''; ?>">
      <div class="chapter-nav chapter-nav-left" onclick="scrollChapters('left')">←</div>
      <div class="chapter-track">
        <?php foreach ($chapters['1'] as $chapter): ?>
          <?php $isUnlocked = isChapterUnlocked($chapter['id'], $unlocked_chapters); ?>
          <div class="chapter-card <?php echo ($chapter['id'] === '01-01') ? 'chapter-active' : ''; ?>" 
               data-chapter-id="<?php echo $chapter['id']; ?>"
               data-chapter-url="<?php echo $chapter['url']; ?>"
               data-is-unlocked="<?php echo $isUnlocked ? 'true' : 'false'; ?>">
            <img src="<?php echo $chapter['thumbnail']; ?>" alt="<?php echo $chapter['id']; ?>">
            <div class="chapter-number"><?php echo $chapter['id']; ?></div>
            <?php if (!$isUnlocked): ?>
              <div class="chapter-locked">
                <div class="lock-icon"></div>
              </div>
            <?php else: ?>
              <div class="chapter-unlocked"></div>
            <?php endif; ?>
          </div>
        <?php endforeach; ?>
      </div>
      <div class="chapter-nav chapter-nav-right" onclick="scrollChapters('right')">→</div>
    </div>
    
    <!-- Navigation buttons -->
    <div class="chapter-navigation">
      <div class="chapter-controls">
        <div class="nav-button prev-button" style="visibility: <?php echo $currentChapter <= 1 ? 'hidden' : 'visible'; ?>">
          <div class="nav-icon">←</div>
        </div>
        <button class="chapter-button">
          <span class="chapter-number-display">CHAPTER <?php echo $currentChapter; ?></span>
        </button>
        <div class="nav-button next-button" onclick="window.location.href='story-chapters2.php'" style="visibility: <?php echo $currentChapter >= $totalChapters ? 'hidden' : 'visible'; ?>">
          <div class="nav-icon">→</div>
        </div>
      </div>
      <div class="next-chapter">Next Chapter</div>
    </div>
  </div>

  <script>
    // Current chapter
    let currentChapter = <?php echo $currentChapter; ?>;
    const totalChapters = <?php echo $totalChapters; ?>;
    
    // Function to navigate between chapters
    function navigateChapter(direction) {
      if (direction === 'prev') {
        if (currentChapter > 1) {
          // Transition to previous chapter
          fadeOutCurrentChapter();
          setTimeout(() => {
            window.location.href = 'story-chapters.php?chapter=' + (currentChapter - 1);
          }, 500);
        }
      } else {
        if (currentChapter < totalChapters) {
          // Transition to next chapter
          fadeOutCurrentChapter();
          setTimeout(() => {
            window.location.href = 'story-chapters.php?chapter=' + (currentChapter + 1);
          }, 500);
        }
      }
    }
    
    // Function to fade out current chapter before transition
    function fadeOutCurrentChapter() {
      const container = document.getElementById('story-container');
      const currentSelection = document.getElementById('chapter' + currentChapter + '-selection');
      
      // Fade out
      container.style.opacity = '0.5';
      currentSelection.style.opacity = '0';
      currentSelection.style.transform = 'translateY(-20px)';
    }
    
    // Update UI to reflect current chapter
    function updateChapterUI() {
      // Update chapter display
      document.querySelector('.chapter-number-display').textContent = `Chapter ${currentChapter}`;
      
      // Update button states
      const prevButton = document.querySelector('.prev-button');
      const nextButton = document.querySelector('.next-button');
      
      if (currentChapter === 1) {
        prevButton.style.opacity = '0.5';
        prevButton.style.cursor = 'not-allowed';
      } else {
        prevButton.style.opacity = '1';
        prevButton.style.cursor = 'pointer';
      }
      
      if (currentChapter === totalChapters) {
        nextButton.style.opacity = '0.5';
        nextButton.style.cursor = 'not-allowed';
      } else {
        nextButton.style.opacity = '1';
        nextButton.style.cursor = 'pointer';
      }
    }
    
    // Function to scroll chapters left or right
    function scrollChapters(direction, chapterNum = currentChapter) {
      const track = document.querySelector(`#chapter${chapterNum}-selection .chapter-track`);
      const cardWidth = 230; // Card width + margin
      
      if (direction === 'left') {
        track.scrollBy({ left: -cardWidth * 2, behavior: 'smooth' });
      } else {
        track.scrollBy({ left: cardWidth * 2, behavior: 'smooth' });
      }
    }
    
    // Initialize the chapter UI
    document.addEventListener('DOMContentLoaded', function() {
      updateChapterUI();
      
      // Set up hover effect for next chapter text
      const nextButton = document.querySelector('.next-button');
      const nextChapterText = document.querySelector('.next-chapter');
      
      nextButton.addEventListener('mouseenter', function() {
        nextChapterText.style.opacity = '1';
        nextChapterText.style.transform = 'translateY(0)';
      });
      
      nextButton.addEventListener('mouseleave', function() {
        nextChapterText.style.opacity = '0';
        nextChapterText.style.transform = 'translateY(10px)';
      });
      
      // Add click event to chapter cards
      document.querySelectorAll('.chapter-card').forEach(card => {
        card.addEventListener('click', function() {
          const chapterId = this.dataset.chapterId;
          const chapterUrl = this.dataset.chapterUrl;
          const isUnlocked = this.dataset.isUnlocked === 'true';
          
          // Set as active
          const chapterNum = chapterId.substring(0, 2);
          document.querySelectorAll(`#chapter${chapterNum[0]}-selection .chapter-card`).forEach(c => {
            c.classList.remove('chapter-active');
          });
          this.classList.add('chapter-active');
          
          // If unlocked, navigate to chapter
          if (isUnlocked) {
            window.location.href = chapterUrl;
          } else {
            alert(`Chapter ${chapterId} is locked. Complete previous chapters to unlock it.`);
          }
        });
      });
      
      // Apply fade-in animation to current chapter selection
      const currentSelection = document.getElementById('chapter' + currentChapter + '-selection');
      if (currentSelection) {
        currentSelection.classList.add('fade-in');
      }
    });
  </script>
</body>
</html>































