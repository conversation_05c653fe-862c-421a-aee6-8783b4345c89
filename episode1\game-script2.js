// DOM Elements
const dialogBox = document.getElementById('dialog-box');
const characterName = document.getElementById('character-name');
const dialogText = document.getElementById('dialog-text');
const choices = document.getElementById('choices');
const nextIndicator = document.getElementById('next-indicator');
const startButton = document.getElementById('start-button');
const titleScreen = document.getElementById('title-screen');
const character = document.getElementById('character');
const controlsMenu = document.getElementById('controls-menu');
const continueButton = document.getElementById('continue-button');

// Game state variables
let currentDialogIndex = 0;
let isTyping = false;
let typingSpeed = 30; // milliseconds per character
let typingTimeout = null;
let choicesMade = {
  firstChoice: null,
  secondChoice: null
};
let isTransitioning = false; // New variable to prevent multiple clicks

// Story script
const story = [
  { speaker: "", text: "<PERSON><PERSON> Aisyah pergi, <PERSON><PERSON> masih duduk beberapa minit di bangku. Dia termenung sambil pandang tasik. Hatinya rasa ringan... tapi juga penuh debar." },
  { speaker: "Salman", text: "*berdiri perlahan* – Aisyah... nama yang aku akan cari esok pagi." },
  { speaker: "", text: "Salman jalan perlahan ke arah pintu keluar taman. Sepanjang laluan pejalan kaki, suasana damai. Tapi bila sampai selekoh kecil dekat tempat parking basikal..." },
  { speaker: "", text: "Dua gadis melintas depan dia. Tudung lilit kemas, baju lengan panjang warna pastel pink dan hijau neon – kedua-duanya sendat dan serba fit. Seluar jeans ketat melekat rapi. Aura manis dan berani terbit dari gaya mereka.", backgroundImage: "../assets/duo.png"  },
  { speaker: "Salman", text: "*bisik perlahan* – Cantik nya baju dia... ketat pulak tu. Macam pek sarung gula-gula." },
  { speaker: "Salman", text: "Haiii... awak nak ke mana tu, jalan berdua macam bidadari tersesat taman?" },
  { speaker: "", text: "Salah seorang gadis tu – Hana – terkejut kecil, tangan dia pegang dada.", backgroundImage: "../assets/terkejut.png"  },
  { speaker: "Hana", text: "Eeee... terkejut kiteeee... Awak ni, sergah orang tengah sweet-sweet!" },
  { speaker: "Salman", text: "*senyum manja* – Sweet tu sebab saya nampak awak. Kalau awak hilang, taman ni rasa tawar je." },
  { speaker: "Ika", text: "*senyum separuh malu* – Awak ni... pandai mengayat ke memang kerja kacau orang?", backgroundImage: "../assets/duamanis.png"  },
  { speaker: "Salman", text: "Saya cuma... suka hargai ciptaan Tuhan. Terutamanya yang tudung dia lilit kemas, baju dia sendat molek, aura dia buat saya taknak kedip mata." },
  { speaker: "", text: "Hana dan Ika saling berpandangan, kemudian gelak kecil. Mereka tetap berdiri di situ, seolah suka dilayan." },
  { speaker: "Hana", text: "Awak kerja puji orang ke? Atau stalker rasmi taman ni?" },
  { type: "choice", speaker: "", text: "Pilih jawapan Salman:" },
  { speaker: "Salman", text: "Kalau awak nak tahu... saya ni ejen pencari senyuman. Bila dapat senyuman manis dari dua orang awek macam ni, saya tahu... misi saya berjaya." },
  { speaker: "", text: "Hana rapatkan botol air dia ke mulut. Minum perlahan. Mata dia tenung Salman dengan sedikit nakal." },
  { speaker: "Hana", text: "*senyum sipu* – Kalau jumpa esok, awak akan puji baju saya lagi ke?" },
  { speaker: "Salman", text: "Kalau awak pakai macam ni lagi... saya takkan puji. Saya akan doa supaya masa berhenti." },
  { speaker: "Ika", text: "*gelak* – Gila betul ayat dia... tapi comel pulak." },
  { speaker: "", text: "Mereka bertiga ketawa kecil. Tapi suasana mula perlahan bila Hana buka phone tengok masa." },
  { speaker: "Hana", text: "Jom Ika... kita lambat dah ni. Tapi thanks sebab buat pagi kami ceria, Encik Puji." },
  { speaker: "Salman", text: "Nama saya Salman. Tapi kalau awak nak panggil saya apa-apa, saya okay... asalkan manja." },
  { speaker: "", text: "Hana dan Ika melangkah pergi sambil tersenyum. Tudung mereka beralun, punggung mereka sedikit bergoyang perlahan ikut langkah. Salman hanya mampu tengok, sambil tangan letak di dada." },
  { speaker: "Salman", text: "Aduh... pagi ni memang buffet cinta. Hati aku kenyang... tapi tetap nak lagi." },
  { speaker: "", text: "🩷", showContinueButton: true }
];

// Choice options
const choiceOptions = [
  // First choice options
  [
  ],
  // Second choice options
  [
    { text: "Suka la tu dapat pujian dari lelaki macam saya ni kann.", nextIndex: 14 },
    { text: "Saya stalker rasmi awak , mana-mana awak pergi saya akan ikut,hihi.", nextIndex: 14 },
    { text: "Awak nak kena puji lagi ke huhu", nextIndex: 14 }]
];

// Initialize the game
function initGame() {
  // Wait for DOM to be fully loaded
  if (!dialogBox || !characterName || !dialogText || !choices || 
      !nextIndicator || !startButton || !titleScreen || 
      !character || !controlsMenu || !continueButton) {
    setTimeout(initGame, 100);
    return;
  }
  
  // Hide dialog box initially
  dialogBox.style.display = 'none';
  
  // Set up event listeners
  startButton.addEventListener('click', startGame);
  dialogBox.addEventListener('click', advanceDialog);
  
  // Set up choice buttons
  const choiceButtons = document.querySelectorAll('.choice-btn');
  
  choiceButtons.forEach(button => {
    button.addEventListener('click', handleChoice);
  });
  
  // Set up continue button
  continueButton.addEventListener('click', () => {
    completeChapter(); // Unlock the next chapter
    window.location.href = '../menuchapters/story-chapters.php';
  });
  
  // Set up control menu buttons
  const menuButtons = document.querySelectorAll('.menu-btn');
  menuButtons.forEach((button, index) => {
    if (index === 0) { // Auto button
      button.addEventListener('click', toggleAutoPlay);
    } else if (index === 1) { // Skip button
      button.addEventListener('click', skipDialog);
    }
  });
}

// Start the game
function startGame() {
  titleScreen.style.display = 'none';
  dialogBox.style.display = 'flex';
  controlsMenu.style.display = 'flex';
  currentDialogIndex = 0;
  showDialog(currentDialogIndex);
}

// Show dialog
function showDialog(index) {
  if (index >= story.length) {
    return;
  }
  
  const dialog = story[index];
  
  // Reset UI elements
  isTyping = true;
  nextIndicator.style.display = 'none';
  dialogText.textContent = ''; // Clear text before starting new dialog
  
  // Handle background image if specified
  if (dialog.backgroundImage) {
    document.getElementById('background').style.backgroundImage = `url('${dialog.backgroundImage}')`;
  }
  
  // Handle choices
  if (dialog.type === 'choice') {
    // Hide regular dialog elements
    dialogBox.style.display = 'none';
    
    // Show choices container with important flag
    choices.style.cssText = 'display: flex !important; opacity: 1 !important; z-index: 9999 !important;';
    
    // Determine which choice set to show (first or second choice)
    const choiceSet = index === 9 ? 0 : 1;
    
    // Get all choice buttons
    const choiceButtons = document.querySelectorAll('.choice-btn');
    
    // Update each button
    choiceButtons.forEach((button, i) => {
      if (i < choiceOptions[choiceSet].length) {
        // Set button text and data
        button.textContent = choiceOptions[choiceSet][i].text;
        button.dataset.choice = i + 1;
        button.dataset.nextIndex = choiceOptions[choiceSet][i].nextIndex;
        
        // Force button to be visible
        button.style.cssText = 'display: block !important; opacity: 1 !important;';
      } else {
        button.style.display = 'none';
      }
    });
    
    isTyping = false;
  } else {
    // Regular dialog
    dialogBox.style.display = 'flex';
    choices.style.display = 'none';
    
    // Show character if needed
    if (dialog.showCharacter) {
      character.style.display = 'block';
      character.style.opacity = 0;
      setTimeout(() => {
        character.style.opacity = 1;
      }, 300);
    }
    
    // Change character image if specified
    if (dialog.characterImage) {
      character.src = dialog.characterImage;
      if (character.style.display === 'none') {
        character.style.display = 'block';
        character.style.opacity = 0;
        setTimeout(() => {
          character.style.opacity = 1;
        }, 300);
      }
    }
    
    // Set speaker name
    characterName.textContent = dialog.speaker || "";
    
    // Show continue button if needed
    if (dialog.showContinueButton) {
      dialogBox.style.display = 'none';
      continueButton.style.display = 'block';
      isTyping = false;
      return;
    } else {
      continueButton.style.display = 'none';
    }
    
    // Type out text
    let i = 0;
    const text = dialog.text || "";
    
    // Clear any existing typing timeouts
    if (typingTimeout) {
      clearTimeout(typingTimeout);
      typingTimeout = null;
    }
    
    // Reset dialog text before starting to type
    dialogText.textContent = '';
    
    const typeWriter = () => {
      if (i < text.length) {
        dialogText.textContent += text.charAt(i);
        i++;
        typingTimeout = setTimeout(typeWriter, typingSpeed);
      } else {
        isTyping = false;
        typingTimeout = null;
        nextIndicator.style.display = 'block';
      }
    };
    
    typeWriter();
  }
}

// Advance dialog
function advanceDialog() {
  // Prevent multiple clicks during transitions
  if (isTransitioning) {
    return;
  }
  
  // If typing, complete the text immediately
  if (isTyping) {
    if (typingTimeout) {
      clearTimeout(typingTimeout);
      typingTimeout = null;
    }
    
    const dialog = story[currentDialogIndex];
    if (dialog && dialog.text) {
      dialogText.textContent = dialog.text;
    }
    isTyping = false;
    nextIndicator.style.display = 'block';
    return;
  }
  
  // If choices are displayed, don't advance
  if (choices.style.display === 'flex' || choices.style.display === 'block') {
    return;
  }
  
  // Set transitioning flag
  isTransitioning = true;
  
  // Move to next dialog
  currentDialogIndex++;
  
  // Clear previous dialog text before showing new one
  dialogText.textContent = '';
  
  // Show next dialog after a short delay
  setTimeout(() => {
    showDialog(currentDialogIndex);
    isTransitioning = false; // Reset transitioning flag
  }, 100);
}

// Handle choice selection
function handleChoice(event) {
  // Prevent multiple clicks
  if (isTransitioning) {
    return;
  }
  
  isTransitioning = true;
  
  const choiceIndex = parseInt(event.target.dataset.choice);
  const nextIndex = parseInt(event.target.dataset.nextIndex);
  
  // Store choice
  if (currentDialogIndex === 9) {
    choicesMade.firstChoice = choiceIndex;
  } else {
    choicesMade.secondChoice = choiceIndex;
  }
  
  // Animate selection
  const selectedButton = event.target;
  selectedButton.style.transform = 'scale(1.05)';
  selectedButton.style.boxShadow = '0 0 20px rgba(100, 180, 255, 0.7)';
  
  // Fade out other choices
  const allButtons = document.querySelectorAll('.choice-btn');
  allButtons.forEach(button => {
    if (button !== selectedButton) {
      button.style.opacity = '0.3';
    }
  });
  
  // Delay before moving to next dialog
  setTimeout(() => {
    // Hide choices container
    choices.style.display = 'none';
    
    // Clear dialog text before showing next dialog
    dialogText.textContent = '';
    
    // Move to next dialog
    currentDialogIndex = nextIndex;
    showDialog(currentDialogIndex);
    
    // Reset transitioning flag
    isTransitioning = false;
  }, 800);
}

// Skip dialog
function skipDialog() {
  // Skip to the end of the current episode
  currentDialogIndex = story.length - 1;
  showDialog(currentDialogIndex);
}

// Auto play
let autoPlayInterval = null;
function toggleAutoPlay() {
  if (autoPlayInterval) {
    // Stop auto play
    clearInterval(autoPlayInterval);
    autoPlayInterval = null;
  } else {
    // Start auto play
    autoPlayInterval = setInterval(() => {
      if (!isTyping && choices.style.display !== 'flex') {
        advanceDialog();
      }
    }, 2000); // Advance every 2 seconds
  }
}

// Debug function to check state
function debugState() {
  // Force show choices for testing
  if (story[currentDialogIndex].type === 'choice') {
    choices.style.cssText = 'display: flex !important; opacity: 1 !important; z-index: 1000 !important;';
    const choiceSet = currentDialogIndex === 9 ? 0 : 1;
    const choiceButtons = document.querySelectorAll('.choice-btn');
    choiceButtons.forEach((button, i) => {
      if (i < choiceOptions[choiceSet].length) {
        button.style.cssText = 'display: block !important; opacity: 1 !important;';
      }
    });
  }
}

// Add debug key
window.addEventListener('keydown', (e) => {
  if (e.key === 'd') {
    debugState();
  }
});

// Initialize the game when the page loads
window.addEventListener('DOMContentLoaded', initGame);

// Add this function at the end of your game script
function completeChapter() {
  // Get the current chapter ID
  const chapterId = '01-02'; // Second chapter ID
  
  // Create form data
  const formData = new FormData();
  formData.append('chapter_id', chapterId);
  
  // Send request to unlock the next chapter
  fetch('../unlock_chapter.php', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    console.log('Chapter completed:', data);
    if (data.success) {
      console.log('Next chapter unlocked:', data.next_chapter);
    }
  })
  .catch(error => {
    console.error('Error completing chapter:', error);
  });
}

// Call this function when the player reaches the end of the chapter
continueButton.addEventListener('click', () => {
  completeChapter(); // Unlock the next chapter
  window.location.href = 'game-interface3.html';
});


