<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include required files
require_once '../includes/resource_manager.php';
require_once '../includes/item_manager.php';

// Get user data
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Get item manager and user inventory
$itemManager = new ItemManager();
$userInventory = $itemManager->getUserInventoryWithDetails($user_id);
$categories = $itemManager->getCategories();

// Filter items by category if specified
$selectedCategory = isset($_GET['category']) ? $_GET['category'] : 'all';
$filteredInventory = $userInventory;

if ($selectedCategory !== 'all') {
    $filteredInventory = array_filter($userInventory, function($item) use ($selectedCategory) {
        return $item['category'] === $selectedCategory;
    });
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Bag</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="bag.css">
</head>
<body>
    <div id="game-container">
        <!-- Top Navigation -->
        <div class="top-nav">
            <div class="nav-left">
                <a href="../menu/home.php" class="back-btn style-text">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="page-title">Bag</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bag-container">
            <!-- Left Sidebar - Categories -->
            <div class="bag-sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-title">📦 Item Categories</div>
                    <div class="sidebar-subtitle">Total: <?php echo count($userInventory); ?> items</div>
                </div>
                
                <div class="category-list">
                    <div class="category-item <?php echo $selectedCategory === 'all' ? 'active' : ''; ?>" 
                         onclick="filterByCategory('all')">
                        <span class="category-icon">📋</span>
                        <span class="category-name">All Items</span>
                        <span class="category-count"><?php echo count($userInventory); ?></span>
                    </div>
                    
                    <?php foreach ($categories as $categoryKey => $categoryData): ?>
                        <?php 
                        $categoryCount = count(array_filter($userInventory, function($item) use ($categoryKey) {
                            return $item['category'] === $categoryKey;
                        }));
                        ?>
                        <div class="category-item <?php echo $selectedCategory === $categoryKey ? 'active' : ''; ?>" 
                             onclick="filterByCategory('<?php echo $categoryKey; ?>')">
                            <span class="category-icon"><?php echo $categoryData['icon']; ?></span>
                            <span class="category-name"><?php echo $categoryData['name']; ?></span>
                            <span class="category-count"><?php echo $categoryCount; ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Right Content - Items -->
            <div class="bag-content">
                <div class="content-header">
                    <div class="content-title">
                        <?php 
                        if ($selectedCategory === 'all') {
                            echo 'All Items';
                        } else {
                            $categoryInfo = $itemManager->getCategoryInfo($selectedCategory);
                            echo $categoryInfo['name'];
                        }
                        ?>
                    </div>
                    <div class="content-subtitle">
                        Showing <?php echo count($filteredInventory); ?> items
                    </div>
                </div>

                <!-- Items Grid -->
                <div class="items-grid">
                    <?php if (empty($filteredInventory)): ?>
                        <div class="no-items">
                            <div class="no-items-icon">📦</div>
                            <div class="no-items-text">No items in this category</div>
                            <div class="no-items-subtitle">Complete missions and explore to find items!</div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($filteredInventory as $item): ?>
                            <div class="item-card" 
                                 data-item-id="<?php echo $item['id']; ?>"
                                 onclick="showItemDetails(<?php echo $item['id']; ?>)">
                                
                                <!-- Rarity Border -->
                                <div class="item-rarity-border" 
                                     style="border-color: <?php echo $itemManager->getRarityBorderColor($item['rarity']); ?>"></div>
                                
                                <!-- Item Icon -->
                                <div class="item-icon"><?php echo $item['icon']; ?></div>
                                
                                <!-- Item Info -->
                                <div class="item-info">
                                    <div class="item-name"><?php echo htmlspecialchars($item['name']); ?></div>
                                    <div class="item-quantity">x<?php echo $item['quantity']; ?></div>
                                    <div class="item-rarity" 
                                         style="color: <?php echo $itemManager->getRarityColor($item['rarity']); ?>">
                                        <?php echo $itemManager->getRarityName($item['rarity']); ?>
                                    </div>
                                </div>
                                
                                <!-- Quick Actions -->
                                <div class="item-actions">
                                    <?php if ($item['type'] === 'consumable' || $item['usage']['type'] === 'character_upgrade'): ?>
                                        <button class="action-btn use-btn" 
                                                onclick="event.stopPropagation(); useItem(<?php echo $item['id']; ?>)"
                                                title="Use Item">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if ($item['sellable']): ?>
                                        <button class="action-btn sell-btn" 
                                                onclick="event.stopPropagation(); sellItem(<?php echo $item['id']; ?>)"
                                                title="Sell Item">
                                            <i class="fas fa-coins"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Details Modal -->
    <div id="item-details-modal" class="item-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-item-name">Item Details</h2>
                <button class="modal-close" onclick="closeItemDetails()">×</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Item details will be loaded here -->
            </div>
            <div class="modal-actions" id="modal-actions">
                <!-- Action buttons will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Notification Element -->
    <div id="notification" class="notification"></div>

    <!-- Include JavaScript -->
    <script src="shared-functions.js"></script>
    <script src="bag.js"></script>
    
    <script>
        // Pass PHP data to JavaScript
        const itemsData = <?php echo json_encode($userInventory); ?>;
        const itemManager = {
            getRarityColor: function(rarity) {
                const rarities = <?php echo json_encode($itemManager->getRarities()); ?>;
                return rarities[rarity]?.color || '#9e9e9e';
            },
            getRarityBorderColor: function(rarity) {
                const rarities = <?php echo json_encode($itemManager->getRarities()); ?>;
                return rarities[rarity]?.border_color || '#757575';
            }
        };
    </script>
</body>
</html>
