/**
 * Bag Page JavaScript
 * Handles item management, filtering, and interactions
 */

// ===== CATEGORY FILTERING =====

/**
 * Filter items by category
 * @param {string} category - Category to filter by
 */
function filterByCategory(category) {
    // Update URL parameter
    const url = new URL(window.location);
    if (category === 'all') {
        url.searchParams.delete('category');
    } else {
        url.searchParams.set('category', category);
    }
    
    // Navigate to filtered page
    window.location.href = url.toString();
}

// ===== ITEM DETAILS MODAL =====

/**
 * Show item details in modal
 * @param {number} itemId - ID of the item to show
 */
function showItemDetails(itemId) {
    const item = itemsData.find(i => i.id === itemId);
    if (!item) {
        showNotification('Item not found', 'error');
        return;
    }

    // Update modal content
    document.getElementById('modal-item-name').textContent = item.name;
    
    const modalBody = document.getElementById('modal-body');
    modalBody.innerHTML = `
        <div class="item-detail-content">
            <div class="item-detail-header">
                <div class="item-detail-icon">${item.icon}</div>
                <div class="item-detail-info">
                    <div class="item-detail-name">${item.name}</div>
                    <div class="item-detail-type">${item.type.charAt(0).toUpperCase() + item.type.slice(1)}</div>
                    <div class="item-detail-rarity" style="color: ${itemManager.getRarityColor(item.rarity)}">
                        ${item.rarity.charAt(0).toUpperCase() + item.rarity.slice(1)}
                    </div>
                    <div class="item-detail-quantity">Quantity: x${item.quantity}</div>
                </div>
            </div>
            
            <div class="item-detail-description">
                <h4>Description</h4>
                <p>${item.long_description || item.description}</p>
            </div>
            
            ${item.usage ? `
                <div class="item-detail-usage">
                    <h4>Usage</h4>
                    <p><strong>Effect:</strong> ${item.usage.effect}</p>
                    ${item.usage.value ? `<p><strong>Value:</strong> ${item.usage.value}</p>` : ''}
                    <p><strong>Target:</strong> ${item.usage.target.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                </div>
            ` : ''}
            
            ${item.obtain_methods ? `
                <div class="item-detail-obtain">
                    <h4>How to Obtain</h4>
                    <ul>
                        ${item.obtain_methods.map(method => `<li>${method}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
            
            ${item.sellable ? `
                <div class="item-detail-sell">
                    <h4>Sell Value</h4>
                    <p>🪙 ${item.sell_price} coins each</p>
                </div>
            ` : ''}
        </div>
    `;

    // Update modal actions
    const modalActions = document.getElementById('modal-actions');
    let actionsHtml = '';
    
    if (item.type === 'consumable' || item.usage?.type === 'character_upgrade') {
        actionsHtml += `
            <button class="modal-action-btn use-btn" onclick="useItem(${item.id})">
                <i class="fas fa-play"></i> Use Item
            </button>
        `;
    }
    
    if (item.sellable) {
        actionsHtml += `
            <button class="modal-action-btn sell-btn" onclick="sellItem(${item.id})">
                <i class="fas fa-coins"></i> Sell Item
            </button>
        `;
    }
    
    actionsHtml += `
        <button class="modal-action-btn cancel-btn" onclick="closeItemDetails()">
            <i class="fas fa-times"></i> Close
        </button>
    `;
    
    modalActions.innerHTML = actionsHtml;

    // Show modal
    const modal = document.getElementById('item-details-modal');
    modal.classList.add('active');
    
    // Add animation
    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);
}

/**
 * Close item details modal
 */
function closeItemDetails() {
    const modal = document.getElementById('item-details-modal');
    modal.style.opacity = '0';
    
    setTimeout(() => {
        modal.classList.remove('active');
    }, 300);
}

// ===== ITEM ACTIONS =====

/**
 * Use an item
 * @param {number} itemId - ID of the item to use
 */
function useItem(itemId) {
    const item = itemsData.find(i => i.id === itemId);
    if (!item) {
        showNotification('Item not found', 'error');
        return;
    }

    // For now, just show a notification about the item usage
    // In a real implementation, this would make an API call
    let message = '';
    
    switch (item.usage?.type) {
        case 'character_upgrade':
            message = `Used ${item.name}! Gained ${item.usage.value} experience points.`;
            break;
        case 'heal':
            message = `Used ${item.name}! Restored ${item.usage.value} health.`;
            break;
        case 'restore_mana':
            message = `Used ${item.name}! Restored ${item.usage.value} mana.`;
            break;
        case 'elemental_enhancement':
            message = `Used ${item.name}! Enhanced elemental abilities.`;
            break;
        default:
            message = `Used ${item.name}!`;
    }
    
    showNotification(message, 'success');
    
    // Close modal if open
    closeItemDetails();
    
    // In a real implementation, you would:
    // 1. Make an API call to use the item
    // 2. Update the user's inventory
    // 3. Apply the item's effects
    // 4. Refresh the page or update the UI
}

/**
 * Sell an item
 * @param {number} itemId - ID of the item to sell
 */
function sellItem(itemId) {
    const item = itemsData.find(i => i.id === itemId);
    if (!item) {
        showNotification('Item not found', 'error');
        return;
    }

    if (!item.sellable) {
        showNotification('This item cannot be sold', 'warning');
        return;
    }

    // Show confirmation dialog
    const confirmed = confirm(`Sell ${item.name} for ${item.sell_price} coins?`);
    if (!confirmed) return;

    // For now, just show a notification
    // In a real implementation, this would make an API call
    showNotification(`Sold ${item.name} for ${item.sell_price} coins!`, 'success');
    
    // Close modal if open
    closeItemDetails();
    
    // In a real implementation, you would:
    // 1. Make an API call to sell the item
    // 2. Update the user's inventory and coins
    // 3. Refresh the page or update the UI
}

// ===== INTERACTIVE EFFECTS =====

/**
 * Add hover effects to item cards
 */
function initializeItemEffects() {
    const itemCards = document.querySelectorAll('.item-card');
    
    itemCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const rarityBorder = this.querySelector('.item-rarity-border');
            if (rarityBorder) {
                rarityBorder.style.boxShadow = `0 0 20px ${rarityBorder.style.borderColor}40`;
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const rarityBorder = this.querySelector('.item-rarity-border');
            if (rarityBorder) {
                rarityBorder.style.boxShadow = 'none';
            }
        });
    });
}

/**
 * Add hover effects to category items
 */
function initializeCategoryEffects() {
    const categoryItems = document.querySelectorAll('.category-item:not(.active)');
    
    categoryItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(0, 188, 212, 0.15)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 255, 255, 0.05)';
        });
    });
}

// ===== SEARCH FUNCTIONALITY =====

/**
 * Search items by name or description
 * @param {string} searchTerm - Search term
 */
function searchItems(searchTerm) {
    const itemCards = document.querySelectorAll('.item-card');
    const searchLower = searchTerm.toLowerCase();
    
    itemCards.forEach(card => {
        const itemId = parseInt(card.dataset.itemId);
        const item = itemsData.find(i => i.id === itemId);
        
        if (!item) return;
        
        const matchesSearch = 
            item.name.toLowerCase().includes(searchLower) ||
            item.description.toLowerCase().includes(searchLower) ||
            (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchLower)));
        
        if (matchesSearch) {
            card.style.display = 'flex';
        } else {
            card.style.display = 'none';
        }
    });
    
    // Update content subtitle
    const visibleCards = document.querySelectorAll('.item-card[style*="flex"], .item-card:not([style*="none"])');
    const contentSubtitle = document.querySelector('.content-subtitle');
    if (contentSubtitle) {
        contentSubtitle.textContent = `Showing ${visibleCards.length} items`;
    }
}

// ===== KEYBOARD SHORTCUTS =====

/**
 * Handle keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Close modal with Escape key
        if (e.key === 'Escape') {
            closeItemDetails();
        }
        
        // Quick search with Ctrl+F
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            // Focus search input if it exists
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
}

// ===== INITIALIZATION =====

/**
 * Initialize all bag page functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize interactive effects
    initializeItemEffects();
    initializeCategoryEffects();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Close modal when clicking outside
    const modal = document.getElementById('item-details-modal');
    modal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeItemDetails();
        }
    });
    
    // Add smooth scrolling to items grid
    const itemsGrid = document.querySelector('.items-grid');
    if (itemsGrid) {
        itemsGrid.style.scrollBehavior = 'smooth';
    }
    
    console.log('Bag page initialized successfully');
});

// ===== UTILITY FUNCTIONS =====

/**
 * Format number with commas
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
function formatNumber(num) {
    return num.toLocaleString();
}

/**
 * Get item by ID from the items data
 * @param {number} itemId - Item ID
 * @returns {object|null} Item object or null
 */
function getItemById(itemId) {
    return itemsData.find(item => item.id === itemId) || null;
}

/**
 * Show notification to user
 * @param {string} message - Message to show
 * @param {string} type - Type of notification (success, error, warning, info)
 */
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    if (notification) {
        notification.textContent = message;
        notification.className = `notification ${type}`;
        notification.classList.add('show');

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// ===== EXPORT FOR MODULE USAGE =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        filterByCategory,
        showItemDetails,
        closeItemDetails,
        useItem,
        sellItem,
        searchItems
    };
}
