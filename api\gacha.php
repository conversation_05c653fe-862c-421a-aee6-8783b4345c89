<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

require_once '../includes/gacha_manager.php';
require_once '../includes/resource_manager.php';

$user_id = $_SESSION['user_id'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_pools':
            $pools = GachaManager::getActivePools();
            echo json_encode(['success' => true, 'pools' => $pools]);
            break;
            
        case 'get_tickets':
            $tickets = GachaManager::getUserTickets($user_id);
            echo json_encode(['success' => true, 'tickets' => $tickets]);
            break;
            
        case 'get_pool_items':
            $pool_id = $_GET['pool_id'] ?? 0;
            if (!$pool_id) {
                echo json_encode(['success' => false, 'message' => 'Pool ID required']);
                break;
            }
            
            $items = GachaManager::getPoolItems($pool_id);
            echo json_encode(['success' => true, 'items' => $items]);
            break;
            
        case 'pull':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['success' => false, 'message' => 'POST method required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            $pool_id = $input['pool_id'] ?? 0;
            $pull_count = $input['pull_count'] ?? 1;
            
            if (!$pool_id) {
                echo json_encode(['success' => false, 'message' => 'Pool ID required']);
                break;
            }
            
            if ($pull_count < 1 || $pull_count > 10) {
                echo json_encode(['success' => false, 'message' => 'Invalid pull count']);
                break;
            }
            
            $result = GachaManager::performPull($user_id, $pool_id, $pull_count);
            echo json_encode($result);
            break;
            
        case 'get_history':
            $limit = $_GET['limit'] ?? 50;
            $history = GachaManager::getUserHistory($user_id, $limit);
            echo json_encode(['success' => true, 'history' => $history]);
            break;
            
        case 'get_user_resources':
            $resources = ResourceManager::getUserResources($user_id);
            echo json_encode(['success' => true, 'resources' => $resources]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
