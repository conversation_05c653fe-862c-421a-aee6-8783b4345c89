<!DOCTYPE html>
<html>
<head>
  <title>Turn-Based RPG - Honkai Style</title>
  <script src="https://cdn.jsdelivr.net/npm/phaser@3/dist/phaser.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="turnbase-rpg.css">
</head>
<body>
  <div class="ui-overlay">
    <!-- Turn Order Display (Top Left) -->
    <div class="turn-order-display">
      <div class="turn-order-title">Turn Order</div>
      <div class="turn-order-list" id="turnOrderList">
        <div class="turn-order-item active" id="turnOrderQistina">
          <div class="turn-order-avatar" style="background: linear-gradient(135deg, #ff6b9d, #ff8fab);">Q</div>
          <div class="turn-order-name">Qistina</div>
        </div>
        <div class="turn-order-item" id="turnOrderZulaikha">
          <div class="turn-order-avatar" style="background: linear-gradient(135deg, #9d6bff, #b88aff);">Z</div>
          <div class="turn-order-name">Zulaikha</div>
        </div>
        <div class="turn-order-item" id="turnOrderEnemy">
          <div class="turn-order-avatar" style="background: linear-gradient(135deg, #ff4444, #ff6666);">E</div>
          <div class="turn-order-name">Enemy</div>
        </div>
      </div>
    </div>

    <div class="turn-indicator" id="turnIndicator">Qistina's Turn</div>

    <!-- Game Results Container -->
    <div class="game-results" id="gameResults">
      <div class="results-container" id="resultsContainer">
        <div class="results-title" id="resultsTitle">Victory!</div>
        <div class="results-stats">
          <div class="stat-row">
            <span class="stat-label">Battle Duration:</span>
            <span class="stat-value" id="battleDuration">0:00</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Qistina HP:</span>
            <span class="stat-value" id="qistinaFinalHP">100/100</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Zulaikha HP:</span>
            <span class="stat-value" id="zulaikhaFinalHP">100/100</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Total Damage Dealt:</span>
            <span class="stat-value" id="totalDamage">0</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">Turns Taken:</span>
            <span class="stat-value" id="turnsTaken">0</span>
          </div>
        </div>
        <div class="countdown-text">
          Returning to previous page in <span class="countdown-number" id="countdownNumber">5</span> seconds...
        </div>
      </div>
    </div>

    <!-- Top Right Buttons -->
    <div class="top-right-buttons">
      <div class="surrender-button" onclick="surrenderGame()" title="Surrender Game">
        <i class="fas fa-flag"></i>
      </div>
      <div class="pause-button" onclick="togglePause()" title="Pause Game">
        <i class="fas fa-pause"></i>
      </div>
    </div>



    <!-- Honkai Star Rail Style Battle UI -->
    <div class="battle-ui" id="battleUI">
      <!-- Ultimate Buttons (Far Left) -->
      <div class="ultimate-panel">
        <div class="ultimate-button" id="qistinaUltimate" onclick="tryUltimate('qistina')" title="Qistina Ultimate (100% Energy)">
          <div style="background: linear-gradient(135deg, #ff6b9d, #ff8fab); width: 100%; height: 100%; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; flex-direction: column;">
            <div>Q</div>
            <div style="font-size: 8px;" id="qistinaUltimatePercent">0%</div>
          </div>
        </div>

        <div class="ultimate-button" id="zulaikhaUltimate" onclick="tryUltimate('zulaikha')" title="Zulaikha Ultimate (100% Energy)">
          <div style="background: linear-gradient(135deg, #9d6bff, #b88aff); width: 100%; height: 100%; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; flex-direction: column;">
            <div>Z</div>
            <div style="font-size: 8px;" id="zulaikhaUltimatePercent">0%</div>
          </div>
        </div>
      </div>

      <!-- 2. Team Energy (Next to Ultimate) -->
      <div class="team-energy-display">
        <div class="energy-title">Team Energy</div>
        <div class="energy-dots" id="teamEnergyDots">
          <div class="energy-dot active"></div>
          <div class="energy-dot active"></div>
          <div class="energy-dot active"></div>
          <div class="energy-dot active"></div>
          <div class="energy-dot active"></div>
        </div>
        <div class="energy-text" id="teamEnergyText">5/5</div>
      </div>

      <!-- 3. Character Display (Center) -->
      <div class="character-display-container">
        <div class="character-display-title">Characters</div>

        <div class="character-display-item">
          <div class="character-display-name">Qistina</div>
          <div class="character-display-hp-bar">
            <div class="character-display-hp-fill" id="qistinaDisplayHPBar" style="width: 100%"></div>
          </div>
          <span class="character-display-hp-text" id="qistinaDisplayHPText">100/100</span>
        </div>

        <div class="character-display-item">
          <div class="character-display-name">Zulaikha</div>
          <div class="character-display-hp-bar">
            <div class="character-display-hp-fill" id="zulaikhaDisplayHPBar" style="width: 100%"></div>
          </div>
          <span class="character-display-hp-text" id="zulaikhaDisplayHPText">100/100</span>
        </div>
      </div>

      <!-- 4. Enemy Shadow (Next to Character) -->
      <div class="enemy-status-container">
        <div class="enemy-status-name" id="enemyStatusName">Shadow Enemy</div>
        <div class="enemy-status-hp">
          <span style="font-size: 12px; color: #ff6666;">HP:</span>
          <div class="enemy-status-hp-bar">
            <div class="enemy-status-hp-fill" id="enemyStatusHealthBar" style="width: 100%"></div>
          </div>
          <span class="enemy-status-hp-text" id="enemyStatusHPText">150/150</span>
        </div>
      </div>

      <!-- 5. Skill Buttons (Far Right) -->
      <div class="action-panel">
        <div class="action-button" id="attackBtn" onclick="performAction('attack')" title="Basic Attack (+1 Energy)">
          <div>Attack</div>
          <div class="action-cost">+1 Energy</div>
        </div>

        <div class="action-button" id="skillBtn" onclick="performAction('skill')" title="Skill Attack (-1 Energy)">
          <div>Skill</div>
          <div class="action-cost">-1 Energy</div>
        </div>
      </div>
    </div>
  </div>

<script>
// Game configuration
const config = {
  type: Phaser.AUTO,
  width: window.innerWidth,
  height: window.innerHeight,
  backgroundColor: '#1a1a2e',
  scene: {
    preload: preload,
    create: create,
    update: update
  },
  scale: {
    mode: Phaser.Scale.RESIZE,
    autoCenter: Phaser.Scale.CENTER_BOTH
  }
};

const game = new Phaser.Game(config);

// Game state
let gameState = {
  currentTurn: 0, // 0: Qistina, 1: Zulaikha, 2: Enemy
  turnOrder: ['qistina', 'zulaikha', 'enemy'],
  battlePhase: 'action', // 'action', 'animation', 'victory', 'defeat'
  battleStartTime: Date.now(),
  totalDamageDealt: 0,
  turnCount: 0,
  // HSR Style Energy System
  teamEnergy: 5, // Shared energy pool (like HSR)
  maxTeamEnergy: 5,
  characters: {
    qistina: {
      name: 'Qistina',
      hp: 100,
      maxHp: 100,
      ultimateEnergy: 0, // Start at 0%
      maxUltimateEnergy: 100, // 100% system
      attack: 25,
      defense: 15,
      speed: 20,
      sprite: null,
      isPlayer: true
    },
    zulaikha: {
      name: 'Zulaikha',
      hp: 100,
      maxHp: 100,
      ultimateEnergy: 0, // Start at 0%
      maxUltimateEnergy: 100, // 100% system
      attack: 22,
      defense: 18,
      speed: 18,
      sprite: null,
      isPlayer: true
    },
    enemy: {
      name: 'Shadow Enemy',
      hp: 150,
      maxHp: 150,
      ultimateEnergy: 0,
      maxUltimateEnergy: 100,
      attack: 30,
      defense: 12,
      speed: 15,
      sprite: null,
      isPlayer: false
    }
  }
};

let scene;

function preload() {
  scene = this;

  // Load character sprites (using existing assets or create fallbacks)
  this.load.image('qistina-battle', 'zulaikha2.png');
  this.load.image('zulaikha-battle', 'Zulaikha.png');
  this.load.image('background-battle', 'winter-city.png');

  // Load enemy image (try to use existing character as enemy)
  this.load.spritesheet('enemy-battle', 'azz.png', {
    frameWidth: 384,
    frameHeight: 1024
  });

  // Load character spritesheets for animations
  this.load.spritesheet('qistina-sheet', 'qistina.png', {
    frameWidth: 430.75,
    frameHeight: 1147
  });

  this.load.spritesheet('zulaikha-sheet', 'Zulaikha.png', {
    frameWidth: 384,
    frameHeight: 1024
  });

  // Create fallback textures for missing assets
  this.load.on('loaderror', function (file) {
    console.log('Failed to load:', file.src, 'Creating fallback...');
  });
}

function create() {
  // Add battle background
  try {
    const bg = this.add.image(0, 0, 'background-battle');
    bg.setOrigin(0, 0);
    bg.setDisplaySize(this.sys.game.config.width, this.sys.game.config.height);
    bg.setTint(0x666666); // Darken for battle atmosphere
  } catch (error) {
    // Create gradient background as fallback
    const graphics = this.add.graphics();
    graphics.fillGradientStyle(0x1a1a2e, 0x1a1a2e, 0x16213e, 0x16213e, 1);
    graphics.fillRect(0, 0, this.sys.game.config.width, this.sys.game.config.height);
  }

  // Create character sprites
  createCharacterSprites.call(this);

  // Create animations
  createAnimations.call(this);

  // Create particle systems
  createParticleSystems.call(this);

  // Initialize battle
  updateUI();
  updateTurnIndicator();

  console.log('Turn-based RPG battle initialized!');
}

function createCharacterSprites() {
  const centerX = this.sys.game.config.width / 2;
  const centerY = this.sys.game.config.height / 2;

  // Store original positions for animations - pindahkan karakter lebih jauh ke kiri dan musuh lebih jauh ke kanan
  gameState.originalPositions = {
    qistina: { x: centerX - 400, y: centerY - 50 },  // Pindah Qistina lebih jauh ke kiri
    zulaikha: { x: centerX - 400, y: centerY + 50 }, // Pindah Zulaikha lebih jauh ke kiri
    enemy: { x: centerX + 400, y: centerY }          // Pindah musuh lebih jauh ke kanan
  };

  // Create Qistina sprite (try spritesheet first, then static image)
  try {
    if (this.textures.exists('qistina-sheet')) {
      gameState.characters.qistina.sprite = this.add.sprite(
        centerX - 400, centerY - 50, 'qistina-sheet'  // Pindah Qistina lebih jauh ke kiri
      );
      // Tetapkan skala dan arah yang sama
      gameState.characters.qistina.sprite.setScale(0.3);
      gameState.characters.qistina.sprite.setFlipX(true);
      gameState.characters.qistina.hasAnimation = true;
    } else if (this.textures.exists('qistina-battle')) {
      gameState.characters.qistina.sprite = this.add.image(
        centerX - 400, centerY - 50, 'qistina-battle'  // Pindah Qistina lebih jauh ke kiri
      );
      gameState.characters.qistina.sprite.setScale(0.3);
      gameState.characters.qistina.sprite.setFlipX(true);
      gameState.characters.qistina.hasAnimation = false;
    } else {
      throw new Error('Qistina sprite not found');
    }
  } catch (error) {
    // Fallback sprite for Qistina
    const graphics = this.add.graphics();
    graphics.fillStyle(0xff6b9d);
    graphics.fillCircle(0, 0, 40);
    graphics.generateTexture('qistina-fallback', 80, 80);
    gameState.characters.qistina.sprite = this.add.image(
      centerX - 400, centerY - 50, 'qistina-fallback'  // Pindah Qistina lebih jauh ke kiri
    );
    gameState.characters.qistina.hasAnimation = false;
  }

  // Create Zulaikha sprite
  try {
    if (this.textures.exists('zulaikha-sheet')) {
      gameState.characters.zulaikha.sprite = this.add.sprite(
        centerX - 400, centerY + 50, 'zulaikha-sheet'  // Pindah Zulaikha lebih jauh ke kiri
      );
      gameState.characters.zulaikha.sprite.setScale(0.3);
      gameState.characters.zulaikha.sprite.setFlipX(true);
      gameState.characters.zulaikha.hasAnimation = true;
    } else if (this.textures.exists('zulaikha-battle')) {
      gameState.characters.zulaikha.sprite = this.add.image(
        centerX - 400, centerY + 50, 'zulaikha-battle'  // Pindah Zulaikha lebih jauh ke kiri
      );
      gameState.characters.zulaikha.sprite.setScale(0.3);
      gameState.characters.zulaikha.sprite.setFlipX(true);
      gameState.characters.zulaikha.hasAnimation = false;
    } else {
      throw new Error('Zulaikha sprite not found');
    }
  } catch (error) {
    // Fallback sprite for Zulaikha
    const graphics = this.add.graphics();
    graphics.fillStyle(0x9d6bff);
    graphics.fillCircle(0, 0, 40);
    graphics.generateTexture('zulaikha-fallback', 80, 80);
    gameState.characters.zulaikha.sprite = this.add.image(
      centerX - 400, centerY + 50, 'zulaikha-fallback'  // Pindah Zulaikha lebih jauh ke kiri
    );
    gameState.characters.zulaikha.hasAnimation = false;
  }

  // Create enemy sprite (try to use real image first)
  try {
    if (this.textures.exists('enemy-battle')) {
      gameState.characters.enemy.sprite = this.add.image(
        centerX + 400, centerY, 'enemy-battle'  // Pindah musuh lebih jauh ke kanan
      );
      gameState.characters.enemy.sprite.setScale(0.3);
      gameState.characters.enemy.sprite.setFlipX(false);
      gameState.characters.enemy.hasAnimation = false;
      console.log('Enemy sprite loaded successfully!');
    } else {
      throw new Error('Enemy sprite not found');
    }
  } catch (error) {
    // Create better fallback enemy sprite
    const graphics = this.add.graphics();

    // Body (dark purple/black)
    graphics.fillStyle(0x2a0845);
    graphics.fillCircle(0, 0, 50);

    // Evil aura
    graphics.fillStyle(0x4a0e4e);
    graphics.fillCircle(0, 0, 60);

    // Red glowing eyes
    graphics.fillStyle(0xff0000);
    graphics.fillCircle(-20, -15, 10);
    graphics.fillCircle(20, -15, 10);

    // Evil grin
    graphics.lineStyle(3, 0xff0000);
    graphics.beginPath();
    graphics.arc(0, 10, 25, 0.2, Math.PI - 0.2);
    graphics.strokePath();

    graphics.generateTexture('enemy-fallback', 120, 120);
    gameState.characters.enemy.sprite = this.add.image(
      centerX + 400, centerY, 'enemy-fallback'  // Pindah musuh lebih jauh ke kanan
    );
    gameState.characters.enemy.hasAnimation = false;
    console.log('Using enhanced fallback enemy sprite');
  }

  // Start idle animations for all characters
  startIdleAnimations();

  // Add glow effect to current turn character
  updateCharacterGlow();
}

function createAnimations() {
  // Create character animations if spritesheets are available
  if (this.textures.exists('qistina-sheet')) {
    this.anims.create({
      key: 'qistina-idle',
      frames: [{ key: 'qistina-sheet', frame: 0 }],
      frameRate: 1
    });

    this.anims.create({
      key: 'qistina-attack',
      frames: this.anims.generateFrameNumbers('qistina-sheet', { start: 0, end: 3 }),
      frameRate: 8,
      repeat: 0
    });
  }

  if (this.textures.exists('zulaikha-sheet')) {
    this.anims.create({
      key: 'zulaikha-idle',
      frames: [{ key: 'zulaikha-sheet', frame: 0 }],
      frameRate: 1
    });

    this.anims.create({
      key: 'zulaikha-attack',
      frames: this.anims.generateFrameNumbers('zulaikha-sheet', { start: 0, end: 3 }),
      frameRate: 8,
      repeat: 0
    });
  }
}

function createParticleSystems() {
  // Create particle textures for effects
  const particleGraphics = this.add.graphics();

  // Magic particle
  particleGraphics.fillStyle(0x00ffff);
  particleGraphics.fillCircle(0, 0, 3);
  particleGraphics.generateTexture('magic-particle', 6, 6);

  // Fire particle
  particleGraphics.clear();
  particleGraphics.fillStyle(0xff4400);
  particleGraphics.fillCircle(0, 0, 4);
  particleGraphics.generateTexture('fire-particle', 8, 8);

  // Lightning particle
  particleGraphics.clear();
  particleGraphics.fillStyle(0xffff00);
  particleGraphics.fillCircle(0, 0, 2);
  particleGraphics.generateTexture('lightning-particle', 4, 4);

  particleGraphics.destroy();
}

function startIdleAnimations() {
  // Add subtle breathing animation to all characters
  Object.values(gameState.characters).forEach(char => {
    if (char.sprite) {
      scene.tweens.add({
        targets: char.sprite,
        scaleY: char.sprite.scaleY * 1.008, // More subtle than before (0.8% instead of 2%)
        duration: 2500, // Slightly slower breathing
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      });
    }
  });
}

function update() {
  // Game loop - handle animations and effects
  if (gameState.battlePhase === 'animation') {
    // Handle battle animations here
  }
}

// Battle system functions
function performAction(actionType) {
  if (gameState.battlePhase !== 'action') return;

  const currentCharacter = getCurrentCharacter();
  if (!currentCharacter.isPlayer) return;

  // Set battle phase to animation to prevent multiple actions
  gameState.battlePhase = 'animation';

  let damage = 0;
  let teamEnergyCost = 0;
  let ultimateEnergyGain = 0;
  let target = gameState.characters.enemy;

  switch (actionType) {
    case 'attack':
      // HSR System: Basic Attack gives +1 team energy and +10% ultimate energy
      damage = Math.floor(currentCharacter.attack * (0.8 + Math.random() * 0.4));
      teamEnergyCost = -1; // Negative means gain energy
      ultimateEnergyGain = 10; // +10% ultimate energy
      performAttackAnimation(currentCharacter, target, damage, teamEnergyCost, ultimateEnergyGain);
      break;
    case 'skill':
      // HSR System: Skill costs 1 team energy and gives +40% ultimate energy
      if (gameState.teamEnergy < 1) {
        showMessage('Not enough team energy!');
        gameState.battlePhase = 'action';
        return;
      }
      damage = Math.floor(currentCharacter.attack * 1.5 * (0.8 + Math.random() * 0.4));
      teamEnergyCost = 1;
      ultimateEnergyGain = 40; // +40% ultimate energy
      performSkillAnimation(currentCharacter, target, damage, teamEnergyCost, ultimateEnergyGain);
      break;
    case 'ultimate':
      // Ultimate uses 100% ultimate energy
      if (currentCharacter.ultimateEnergy < 100) {
        showMessage('Ultimate not ready! Need 100%');
        gameState.battlePhase = 'action';
        return;
      }
      damage = Math.floor(currentCharacter.attack * 2.5 * (0.8 + Math.random() * 0.4));
      teamEnergyCost = 0; // Ultimate doesn't use team energy
      ultimateEnergyGain = -100; // Consumes all ultimate energy (back to 0%)
      performUltimateAnimation(currentCharacter, target, damage, teamEnergyCost, ultimateEnergyGain);
      break;
  }
}

function performAttackAnimation(attacker, target, damage, teamEnergyCost, ultimateEnergyGain) {
  const originalPos = gameState.originalPositions[attacker.name.toLowerCase()];

  // Character moves toward enemy
  scene.tweens.add({
    targets: attacker.sprite,
    x: target.sprite.x - 100,
    duration: 300,
    ease: 'Power2',
    onComplete: () => {
      // Play attack animation if available
      if (attacker.hasAnimation) {
        attacker.sprite.play(attacker.name.toLowerCase() + '-attack');
      }

      // Screen shake effect
      scene.cameras.main.shake(200, 0.01);

      // Apply damage after a short delay
      setTimeout(() => {
        applyDamageWithHSREffects(attacker, target, damage, teamEnergyCost, ultimateEnergyGain);

        // Return character to original position
        scene.tweens.add({
          targets: attacker.sprite,
          x: originalPos.x,
          duration: 300,
          ease: 'Power2',
          onComplete: () => {
            gameState.battlePhase = 'action';
            checkBattleEnd(target);
          }
        });
      }, 200);
    }
  });
}

function performSkillAnimation(attacker, target, damage, teamEnergyCost, ultimateEnergyGain) {
  // Create magic circle effect
  createMagicCircle(attacker.sprite.x, attacker.sprite.y);

  // Character glows with magic energy
  attacker.sprite.setTint(0x00ffff);

  setTimeout(() => {
    // Create particle burst toward enemy
    createParticleBurst(attacker.sprite.x, attacker.sprite.y, target.sprite.x, target.sprite.y, 'magic-particle');

    // Screen flash effect
    scene.cameras.main.flash(300, 0, 255, 255);

    setTimeout(() => {
      applyDamageWithHSREffects(attacker, target, damage, teamEnergyCost, ultimateEnergyGain);
      attacker.sprite.clearTint();
      gameState.battlePhase = 'action';
      checkBattleEnd(target);
    }, 500);
  }, 300);
}

function performUltimateAnimation(attacker, target, damage, teamEnergyCost, ultimateEnergyGain) {
  // Screen darkens
  const overlay = scene.add.graphics();
  overlay.fillStyle(0x000000, 0.7);
  overlay.fillRect(0, 0, scene.sys.game.config.width, scene.sys.game.config.height);

  // Character becomes the focus
  attacker.sprite.setTint(0xffffff);
  attacker.sprite.setScale(attacker.sprite.scaleX * 1.5, attacker.sprite.scaleY * 1.5);

  // Create dramatic effect
  createLightningEffect(target.sprite.x, target.sprite.y);

  setTimeout(() => {
    // Multiple screen shakes
    scene.cameras.main.shake(500, 0.02);

    // Massive damage
    applyDamageWithHSREffects(attacker, target, damage, teamEnergyCost, ultimateEnergyGain);

    // Clean up effects
    overlay.destroy();
    attacker.sprite.clearTint();
    attacker.sprite.setScale(attacker.sprite.scaleX / 1.5, attacker.sprite.scaleY / 1.5);

    gameState.battlePhase = 'action';
    checkBattleEnd(target);
  }, 1000);
}

function performDefendAnimation(character, energyCost) {
  // Create shield effect
  const shield = scene.add.graphics();
  shield.lineStyle(4, 0x00ff00);
  shield.strokeCircle(character.sprite.x, character.sprite.y, 60);

  character.sprite.setTint(0x00ff00);
  character.energy = Math.max(0, character.energy - energyCost);

  showMessage(character.name + ' is defending!');

  setTimeout(() => {
    shield.destroy();
    character.sprite.clearTint();
    gameState.battlePhase = 'action';
    nextTurn();
  }, 1000);
}

function createMagicCircle(x, y) {
  const circle = scene.add.graphics();
  circle.lineStyle(3, 0x00ffff);
  circle.strokeCircle(x, y, 20);
  circle.lineStyle(2, 0xffffff);
  circle.strokeCircle(x, y, 40);

  // Rotate the magic circle
  scene.tweens.add({
    targets: circle,
    rotation: Math.PI * 2,
    duration: 1000,
    repeat: 0,
    onComplete: () => circle.destroy()
  });
}

function createParticleBurst(fromX, fromY, toX, toY, particleType) {
  for (let i = 0; i < 10; i++) {
    const particle = scene.add.image(fromX, fromY, particleType);

    scene.tweens.add({
      targets: particle,
      x: toX + (Math.random() - 0.5) * 100,
      y: toY + (Math.random() - 0.5) * 100,
      alpha: 0,
      duration: 500 + Math.random() * 300,
      ease: 'Power2',
      onComplete: () => particle.destroy()
    });
  }
}

function createLightningEffect(x, y) {
  for (let i = 0; i < 5; i++) {
    const lightning = scene.add.graphics();
    lightning.lineStyle(3, 0xffff00);

    // Draw zigzag lightning
    lightning.beginPath();
    lightning.moveTo(x, 0);
    for (let j = 0; j < 10; j++) {
      lightning.lineTo(
        x + (Math.random() - 0.5) * 50,
        (j + 1) * (y / 10)
      );
    }
    lightning.strokePath();

    // Fade out lightning
    scene.tweens.add({
      targets: lightning,
      alpha: 0,
      duration: 200 + i * 100,
      onComplete: () => lightning.destroy()
    });
  }
}

function applyDamageWithHSREffects(attacker, target, damage, teamEnergyCost, ultimateEnergyGain) {
  // HSR Energy System
  // Handle team energy
  if (teamEnergyCost > 0) {
    gameState.teamEnergy = Math.max(0, gameState.teamEnergy - teamEnergyCost);
  } else if (teamEnergyCost < 0) {
    // Gain energy (basic attack)
    gameState.teamEnergy = Math.min(gameState.maxTeamEnergy, gameState.teamEnergy + Math.abs(teamEnergyCost));
  }

  // Handle ultimate energy
  if (ultimateEnergyGain > 0) {
    attacker.ultimateEnergy = Math.min(attacker.maxUltimateEnergy, attacker.ultimateEnergy + ultimateEnergyGain);
  } else if (ultimateEnergyGain < 0) {
    // Ultimate consumption
    attacker.ultimateEnergy = Math.max(0, attacker.ultimateEnergy + ultimateEnergyGain);
  }

  // Apply damage
  if (damage > 0) {
    const finalDamage = Math.max(1, damage - target.defense);
    target.hp = Math.max(0, target.hp - finalDamage);
    showDamage(target.sprite.x, target.sprite.y - 50, finalDamage);

    // Track total damage dealt (only count damage to enemy)
    if (!target.isPlayer) {
      gameState.totalDamageDealt += finalDamage;
    }

    // Enhanced damage animation
    scene.tweens.add({
      targets: target.sprite,
      scaleX: target.sprite.scaleX * 1.3,
      scaleY: target.sprite.scaleY * 1.3,
      duration: 100,
      yoyo: true,
      ease: 'Power2'
    });

    // Red flash on target
    target.sprite.setTint(0xff0000);
    setTimeout(() => target.sprite.clearTint(), 200);
  }

  updateUI();
}

function checkBattleEnd(target) {
  if (target.hp <= 0) {
    gameState.battlePhase = 'victory';
    showVictoryMessage();

    // Victory animation
    scene.cameras.main.flash(1000, 255, 255, 0);
    return true;
  }

  nextTurn();
  return false;
}

function showVictoryMessage() {
  showGameResults(true);
}

function showGameResults(isVictory) {
  const resultsContainer = document.getElementById('gameResults');
  const resultsTitle = document.getElementById('resultsTitle');
  const container = document.getElementById('resultsContainer');

  // Calculate battle stats
  const battleDuration = Math.floor((Date.now() - gameState.battleStartTime) / 1000);
  const minutes = Math.floor(battleDuration / 60);
  const seconds = battleDuration % 60;
  const durationText = `${minutes}:${seconds.toString().padStart(2, '0')}`;

  // Update results content
  if (isVictory) {
    resultsTitle.textContent = 'VICTORY!';
    resultsTitle.className = 'results-title victory-title';
    container.className = 'results-container victory-glow';
  } else {
    resultsTitle.textContent = 'DEFEAT';
    resultsTitle.className = 'results-title defeat-title';
    container.className = 'results-container defeat-shake';
  }

  // Update stats
  document.getElementById('battleDuration').textContent = durationText;
  document.getElementById('qistinaFinalHP').textContent = `${gameState.characters.qistina.hp}/${gameState.characters.qistina.maxHp}`;
  document.getElementById('zulaikhaFinalHP').textContent = `${gameState.characters.zulaikha.hp}/${gameState.characters.zulaikha.maxHp}`;
  document.getElementById('totalDamage').textContent = gameState.totalDamageDealt;
  document.getElementById('turnsTaken').textContent = gameState.turnCount;

  // Show results container
  resultsContainer.style.display = 'flex';

  // Start countdown
  let countdown = 5;
  const countdownElement = document.getElementById('countdownNumber');

  const countdownInterval = setInterval(() => {
    countdown--;
    countdownElement.textContent = countdown;

    if (countdown <= 0) {
      clearInterval(countdownInterval);

      // Redirect to previous page
      if (window.history.length > 1) {
        window.history.back();
      } else {
        // If no previous page, go to a default location
        window.location.href = '../menu/home.php';
      }
    }
  }, 1000);
}

function getCurrentCharacter() {
  const characterName = gameState.turnOrder[gameState.currentTurn];
  return gameState.characters[characterName];
}

function nextTurn() {
  gameState.currentTurn = (gameState.currentTurn + 1) % gameState.turnOrder.length;
  gameState.turnCount++; // Track total turns

  // HSR System: No automatic energy regeneration
  // Energy is gained through basic attacks and consumed by skills

  updateUI();
  updateTurnIndicator();
  updateCharacterGlow();

  // AI turn for enemy
  if (!getCurrentCharacter().isPlayer) {
    setTimeout(enemyTurn, 1000);
  }
}

function enemyTurn() {
  const enemy = gameState.characters.enemy;
  const targets = [gameState.characters.qistina, gameState.characters.zulaikha];
  const aliveTargets = targets.filter(t => t.hp > 0);

  if (aliveTargets.length === 0) {
    gameState.battlePhase = 'defeat';
    showMessage('Defeat! All characters have fallen!');
    return;
  }

  gameState.battlePhase = 'animation';

  const target = aliveTargets[Math.floor(Math.random() * aliveTargets.length)];
  const damage = Math.floor(enemy.attack * (0.7 + Math.random() * 0.6));

  // Enemy attack animation with effects
  performEnemyAttackAnimation(enemy, target, damage);
}

function performEnemyAttackAnimation(enemy, target, damage) {
  // Enemy glows red before attacking
  enemy.sprite.setTint(0xff0000);

  // Create dark energy particles
  createParticleBurst(enemy.sprite.x, enemy.sprite.y, target.sprite.x, target.sprite.y, 'fire-particle');

  // Enemy moves toward target
  const originalX = enemy.sprite.x;
  scene.tweens.add({
    targets: enemy.sprite,
    x: target.sprite.x + 100,
    duration: 400,
    ease: 'Power2',
    onComplete: () => {
      // Screen shake on impact
      scene.cameras.main.shake(300, 0.015);

      // Apply damage
      const finalDamage = Math.max(1, damage - target.defense);
      target.hp = Math.max(0, target.hp - finalDamage);
      showDamage(target.sprite.x, target.sprite.y - 50, finalDamage);

      // Enhanced target damage animation
      scene.tweens.add({
        targets: target.sprite,
        scaleX: target.sprite.scaleX * 1.3,
        scaleY: target.sprite.scaleY * 1.3,
        duration: 150,
        yoyo: true,
        ease: 'Power2'
      });

      // Target flashes red
      target.sprite.setTint(0xff0000);
      setTimeout(() => target.sprite.clearTint(), 300);

      // Enemy returns to position
      scene.tweens.add({
        targets: enemy.sprite,
        x: originalX,
        duration: 300,
        ease: 'Power2',
        onComplete: () => {
          enemy.sprite.clearTint();
          updateUI();

          // Check for defeat
          if (gameState.characters.qistina.hp <= 0 && gameState.characters.zulaikha.hp <= 0) {
            gameState.battlePhase = 'defeat';
            showDefeatMessage();

            // Defeat animation
            scene.cameras.main.fade(2000, 0, 0, 0);
            return;
          }

          gameState.battlePhase = 'action';
          nextTurn();
        }
      });
    }
  });
}

function updateCharacterGlow() {
  // Remove existing glows
  Object.values(gameState.characters).forEach(char => {
    if (char.sprite) {
      char.sprite.clearTint();
    }
  });

  // Add glow to current character
  const currentChar = getCurrentCharacter();
  if (currentChar.sprite) {
    currentChar.sprite.setTint(0xffff88);
  }
}

function updateUI() {
  // Update character portraits
  updateCharacterPortrait('qistina');
  updateCharacterPortrait('zulaikha');

  // Update enemy health (old display)
  const enemy = gameState.characters.enemy;
  const enemyHealthPercent = (enemy.hp / enemy.maxHp) * 100;
  const enemyHealthBar = document.getElementById('enemyHealthBar');
  const enemyHPText = document.getElementById('enemyHPText');

  if (enemyHealthBar) enemyHealthBar.style.width = enemyHealthPercent + '%';
  if (enemyHPText) enemyHPText.textContent = enemy.hp + '/' + enemy.maxHp;

  // Update new enemy status container
  updateEnemyStatus();

  // Update character status container
  updateCharacterStatus();

  // Update team energy display
  updateTeamEnergyDisplay();
}

function updateEnemyStatus() {
  const enemy = gameState.characters.enemy;
  const enemyHealthPercent = (enemy.hp / enemy.maxHp) * 100;

  // Update enemy status in right container
  const enemyStatusHealthBar = document.getElementById('enemyStatusHealthBar');
  const enemyStatusHPText = document.getElementById('enemyStatusHPText');

  if (enemyStatusHealthBar) enemyStatusHealthBar.style.width = enemyHealthPercent + '%';
  if (enemyStatusHPText) enemyStatusHPText.textContent = enemy.hp + '/' + enemy.maxHp;
}

function updateCharacterStatus() {
  // Update Qistina display
  const qistina = gameState.characters.qistina;
  const qistinaHealthPercent = (qistina.hp / qistina.maxHp) * 100;
  const qistinaDisplayHPBar = document.getElementById('qistinaDisplayHPBar');
  const qistinaDisplayHPText = document.getElementById('qistinaDisplayHPText');

  if (qistinaDisplayHPBar) qistinaDisplayHPBar.style.width = qistinaHealthPercent + '%';
  if (qistinaDisplayHPText) qistinaDisplayHPText.textContent = qistina.hp + '/' + qistina.maxHp;

  // Update Zulaikha display
  const zulaikha = gameState.characters.zulaikha;
  const zulaikhaHealthPercent = (zulaikha.hp / zulaikha.maxHp) * 100;
  const zulaikhaDisplayHPBar = document.getElementById('zulaikhaDisplayHPBar');
  const zulaikhaDisplayHPText = document.getElementById('zulaikhaDisplayHPText');

  if (zulaikhaDisplayHPBar) zulaikhaDisplayHPBar.style.width = zulaikhaHealthPercent + '%';
  if (zulaikhaDisplayHPText) zulaikhaDisplayHPText.textContent = zulaikha.hp + '/' + zulaikha.maxHp;
}

function updateTeamEnergyDisplay() {
  const energyDots = document.getElementById('teamEnergyDots');
  const energyText = document.getElementById('teamEnergyText');

  if (energyDots) {
    const dots = energyDots.querySelectorAll('.energy-dot');
    dots.forEach((dot, index) => {
      if (index < gameState.teamEnergy) {
        dot.classList.add('active');
      } else {
        dot.classList.remove('active');
      }
    });
  }

  if (energyText) {
    energyText.textContent = `${gameState.teamEnergy}/${gameState.maxTeamEnergy}`;
  }
}

function updateCharacterPortrait(charName) {
  const char = gameState.characters[charName];
  const canUseUltimate = char.ultimateEnergy >= 100; // 100% system

  // Update ultimate button percentage
  const ultimatePercent = document.getElementById(charName + 'UltimatePercent');
  if (ultimatePercent) {
    const percentage = Math.floor(char.ultimateEnergy);
    ultimatePercent.textContent = percentage + '%';
  }

  // Update ultimate button glow for ultimate ready
  const ultimateButton = document.getElementById(charName + 'Ultimate');
  if (ultimateButton) {
    if (canUseUltimate && char.isPlayer) {
      ultimateButton.classList.add('ultimate-ready');
    } else {
      ultimateButton.classList.remove('ultimate-ready');
    }
  }
}

function updateTurnIndicator() {
  const indicator = document.getElementById('turnIndicator');
  const currentChar = getCurrentCharacter();
  indicator.textContent = currentChar.name + "'s Turn";

  // Update turn order display
  updateTurnOrderDisplay();

  // Disable/enable action buttons based on turn
  const actionButtons = document.querySelectorAll('.action-button');
  actionButtons.forEach(btn => {
    btn.style.opacity = (!currentChar.isPlayer || gameState.battlePhase !== 'action') ? '0.5' : '1';
    btn.style.pointerEvents = (!currentChar.isPlayer || gameState.battlePhase !== 'action') ? 'none' : 'auto';
  });

  // Update skill button based on team energy (HSR system)
  const skillBtn = document.getElementById('skillBtn');
  if (skillBtn && currentChar.isPlayer) {
    if (gameState.teamEnergy < 1) {
      skillBtn.style.opacity = '0.5';
      skillBtn.style.pointerEvents = 'none';
    } else {
      skillBtn.style.opacity = '1';
      skillBtn.style.pointerEvents = 'auto';
    }
  }
}

function updateTurnOrderDisplay() {
  // Remove active class from all turn order items
  document.querySelectorAll('.turn-order-item').forEach(item => {
    item.classList.remove('active');
  });

  // Add active class to current turn
  const currentCharacterName = gameState.turnOrder[gameState.currentTurn];
  let turnOrderId = '';

  switch(currentCharacterName) {
    case 'qistina':
      turnOrderId = 'turnOrderQistina';
      break;
    case 'zulaikha':
      turnOrderId = 'turnOrderZulaikha';
      break;
    case 'enemy':
      turnOrderId = 'turnOrderEnemy';
      break;
  }

  const activeItem = document.getElementById(turnOrderId);
  if (activeItem) {
    activeItem.classList.add('active');
  }
}

// New function for ultimate activation via character portraits
function tryUltimate(characterName) {
  if (gameState.battlePhase !== 'action') return;

  const character = gameState.characters[characterName];
  const currentChar = getCurrentCharacter();

  // Check if it's this character's turn and they have 100% ultimate energy
  if (currentChar.name.toLowerCase() === characterName && character.ultimateEnergy >= 100) {
    performAction('ultimate');
  } else if (currentChar.name.toLowerCase() !== characterName) {
    showMessage("It's not " + character.name + "'s turn!");
  } else {
    const currentPercent = Math.floor(character.ultimateEnergy);
    showMessage(character.name + " ultimate: " + currentPercent + "%/100%");
  }
}

function showDamage(x, y, damage) {
  const damageText = document.createElement('div');
  damageText.className = 'damage-text';
  damageText.textContent = '-' + damage;
  damageText.style.left = x + 'px';
  damageText.style.top = y + 'px';
  document.body.appendChild(damageText);

  setTimeout(() => {
    document.body.removeChild(damageText);
  }, 1000);
}

function showDefeatMessage() {
  showGameResults(false);
}

function showMessage(message) {
  const indicator = document.getElementById('turnIndicator');
  const originalText = indicator.textContent;
  indicator.textContent = message;
  indicator.style.background = 'rgba(255, 100, 100, 0.8)';

  setTimeout(() => {
    indicator.textContent = originalText;
    indicator.style.background = 'rgba(0, 0, 0, 0.8)';
    indicator.style.fontSize = '16px'; // Reset font size
  }, 2000);
}

// Pause functionality
let isPaused = false;

function togglePause() {
  isPaused = !isPaused;
  const pauseButton = document.querySelector('.pause-button');

  if (isPaused) {
    // Pause the game
    scene.scene.pause();
    pauseButton.innerHTML = '<i class="fas fa-play"></i>';
    pauseButton.title = 'Resume Game';
    showMessage('Game Paused');
  } else {
    // Resume the game
    scene.scene.resume();
    pauseButton.innerHTML = '<i class="fas fa-pause"></i>';
    pauseButton.title = 'Pause Game';
    showMessage('Game Resumed');
  }
}

function surrenderGame() {
  // Confirm surrender
  if (confirm('Are you sure you want to surrender? This will end the battle immediately.')) {
    gameState.battlePhase = 'defeat';
    showMessage('You have surrendered the battle!');

    // Show defeat screen after a short delay
    setTimeout(() => {
      showDefeatMessage();

      // Fade out the game
      if (scene && scene.cameras) {
        scene.cameras.main.fade(2000, 0, 0, 0);
      }
    }, 1500);
  }
}

// Initialize the game
console.log('Turn-based RPG game loaded!');
</script>

</body>
</html>




