{"items": [{"id": 1, "name": "Experience Log", "type": "material", "category": "upgrade", "rarity": "common", "description": "A detailed log containing valuable experience points. Used to upgrade character levels and unlock new abilities.", "long_description": "This ancient tome contains the accumulated wisdom and experiences of countless adventurers. When used, it grants substantial experience points to characters, helping them grow stronger and unlock new potential. The pages shimmer with magical energy, indicating the powerful knowledge contained within.", "icon": "📖", "image": "../assets/icons/experience_log.png", "stack_size": 999, "sellable": true, "sell_price": 50, "usage": {"type": "character_upgrade", "effect": "Grants experience points to selected character", "value": 1000, "target": "character"}, "obtain_methods": ["Daily missions", "Story chapters", "Event rewards", "Mail attachments"], "tags": ["experience", "upgrade", "character", "material"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 2, "name": "Skill Enhancement Crystal", "type": "material", "category": "upgrade", "rarity": "rare", "description": "A crystallized essence that enhances character skills and abilities.", "long_description": "This rare crystal pulses with concentrated magical energy. When applied to a character's skills, it can enhance their power, reduce cooldowns, or unlock new skill variations. The crystal's faceted surface reflects different colors depending on the element it resonates with.", "icon": "💎", "image": "../assets/icons/skill_crystal.png", "stack_size": 99, "sellable": true, "sell_price": 200, "usage": {"type": "skill_upgrade", "effect": "Enhances character skills", "value": 1, "target": "character_skill"}, "obtain_methods": ["Weekly missions", "Boss battles", "Gacha rewards", "Store purchase"], "tags": ["skill", "enhancement", "crystal", "rare"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 3, "name": "Health Potion", "type": "consumable", "category": "healing", "rarity": "common", "description": "A basic healing potion that restores health during battle.", "long_description": "A standard healing potion brewed from medicinal herbs and magical spring water. This crimson liquid can quickly restore a character's health during combat, making it an essential item for any adventurer. The bottle glows with a warm, comforting light.", "icon": "🧪", "image": "../assets/icons/health_potion.png", "stack_size": 50, "sellable": true, "sell_price": 25, "usage": {"type": "heal", "effect": "Restores character health", "value": 500, "target": "character"}, "obtain_methods": ["Store purchase", "Alchemy crafting", "Treasure chests", "Enemy drops"], "tags": ["healing", "consumable", "battle", "health"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 4, "name": "<PERSON><PERSON>", "type": "consumable", "category": "restoration", "rarity": "uncommon", "description": "A magical elixir that restores mana and energy for skill usage.", "long_description": "This azure elixir contains concentrated magical essence that can instantly restore a character's mana reserves. The liquid swirls with ethereal energy, and drinking it provides a refreshing surge of magical power that enables the use of powerful skills and abilities.", "icon": "🔮", "image": "../assets/icons/mana_elixir.png", "stack_size": 30, "sellable": true, "sell_price": 75, "usage": {"type": "restore_mana", "effect": "<PERSON>ores character mana", "value": 100, "target": "character"}, "obtain_methods": ["Store purchase", "Alchemy crafting", "Magic fountain", "Wizard rewards"], "tags": ["mana", "restoration", "magic", "elixir"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 5, "name": "Fire Essence", "type": "material", "category": "elemental", "rarity": "rare", "description": "Pure fire elemental essence used for crafting and character enhancement.", "long_description": "A concentrated essence of pure fire element, captured from the heart of volcanic regions. This glowing red substance radiates intense heat and can be used to enhance fire-based characters, craft powerful fire weapons, or create flame-based consumables. Handle with extreme care.", "icon": "🔥", "image": "../assets/icons/fire_essence.png", "stack_size": 99, "sellable": true, "sell_price": 150, "usage": {"type": "elemental_enhancement", "effect": "Enhances fire element characters", "value": 1, "target": "fire_character"}, "obtain_methods": ["Fire dungeons", "Volcanic exploration", "Fire boss defeats", "Elemental synthesis"], "tags": ["fire", "elemental", "essence", "crafting"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 6, "name": "Ice Shard", "type": "material", "category": "elemental", "rarity": "rare", "description": "Crystallized ice essence with powerful freezing properties.", "long_description": "A beautiful crystalline shard formed from pure ice elemental energy. Despite its delicate appearance, this shard never melts and radiates an aura of absolute cold. It's particularly valuable for enhancing ice-based characters and crafting frost weapons that can freeze enemies solid.", "icon": "❄️", "image": "../assets/icons/ice_shard.png", "stack_size": 99, "sellable": true, "sell_price": 150, "usage": {"type": "elemental_enhancement", "effect": "Enhances ice element characters", "value": 1, "target": "ice_character"}, "obtain_methods": ["Ice caves", "Frozen wasteland", "Ice boss defeats", "Winter events"], "tags": ["ice", "elemental", "shard", "freezing"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 7, "name": "Lightning Core", "type": "material", "category": "elemental", "rarity": "epic", "description": "A highly charged core containing pure lightning energy.", "long_description": "This spherical core crackles with contained lightning energy, creating small electrical arcs that dance across its surface. Extremely rare and dangerous to handle, it contains enough electrical power to enhance lightning-based characters significantly or craft the most powerful thunder weapons known to exist.", "icon": "⚡", "image": "../assets/icons/lightning_core.png", "stack_size": 50, "sellable": true, "sell_price": 300, "usage": {"type": "elemental_enhancement", "effect": "Enhances lightning element characters", "value": 2, "target": "lightning_character"}, "obtain_methods": ["Storm dungeons", "Thunder peaks", "Lightning boss defeats", "Tempest events"], "tags": ["lightning", "elemental", "core", "electric"], "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}], "categories": {"material": {"name": "Materials", "description": "Items used for crafting and character enhancement", "icon": "🔧"}, "consumable": {"name": "Consumables", "description": "Items that can be used during battle or exploration", "icon": "🍯"}, "equipment": {"name": "Equipment", "description": "Weapons, armor, and accessories for characters", "icon": "⚔️"}, "key": {"name": "Key Items", "description": "Important items for story progression", "icon": "🗝️"}}, "rarities": {"common": {"name": "Common", "color": "#9e9e9e", "border_color": "#757575"}, "uncommon": {"name": "Uncommon", "color": "#4caf50", "border_color": "#388e3c"}, "rare": {"name": "Rare", "color": "#2196f3", "border_color": "#1976d2"}, "epic": {"name": "Epic", "color": "#9c27b0", "border_color": "#7b1fa2"}, "legendary": {"name": "Legendary", "color": "#ff9800", "border_color": "#f57c00"}}}