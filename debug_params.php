<?php
// Debug parameter count
$item = [
    'name' => 'Test',
    'description' => 'Test desc',
    'category' => 'diamonds',
    'item_type' => 'test',
    'price_usd' => 4.99,
    'price_diamonds' => 0,
    'original_price_usd' => null,
    'original_price_diamonds' => 0,
    'discount_percentage' => 0,
    'reward_diamonds' => 300,
    'reward_coins' => 0,
    'reward_energy' => 0,
    'bonus_percentage' => 20,
    'purchase_limit' => 0,
    'time_limited' => false,
    'expires_at' => null,
    'is_featured' => true,
    'image_url' => 'test.png'
];

$name = $item['name'];
$description = $item['description'];
$category = $item['category'];
$item_type = $item['item_type'];
$price_usd = $item['price_usd'] ?? 0;
$price_diamonds = $item['price_diamonds'] ?? 0;
$original_price_usd = $item['original_price_usd'] ?? null;
$original_price_diamonds = $item['original_price_diamonds'] ?? 0;
$discount_percentage = $item['discount_percentage'] ?? 0;
$reward_diamonds = $item['reward_diamonds'] ?? 0;
$reward_coins = $item['reward_coins'] ?? 0;
$reward_energy = $item['reward_energy'] ?? 0;
$bonus_percentage = $item['bonus_percentage'] ?? 0;
$purchase_limit = $item['purchase_limit'] ?? 0;
$time_limited = $item['time_limited'] ?? false;
$expires_at = $item['expires_at'] ?? null;
$is_featured = $item['is_featured'] ?? false;
$image_url = $item['image_url'] ?? '';

$params = [
    $name,
    $description,
    $category,
    $item_type,
    $price_usd,
    $price_diamonds,
    $original_price_usd,
    $original_price_diamonds,
    $discount_percentage,
    $reward_diamonds,
    $reward_coins,
    $reward_energy,
    $bonus_percentage,
    $purchase_limit,
    $time_limited,
    $expires_at,
    $is_featured,
    $image_url
];

echo "Parameter count: " . count($params) . "\n";
echo "Type string: ssssdiidiiiiiisis\n";
echo "Type string length: " . strlen("ssssdiidiiiiiisis") . "\n";

foreach ($params as $i => $param) {
    echo ($i + 1) . ". " . gettype($param) . " - " . var_export($param, true) . "\n";
}
?>
