<?php
require_once 'config/database.php';
require_once 'includes/mail_manager.php';

echo "<h2>Setting up Mail System Database...</h2>";

try {
    $conn = getDatabaseConnection();
    
    // Create mail_messages table if not exists
    echo "<h3>Creating mail_messages table...</h3>";
    $sql_mail_messages = "CREATE TABLE IF NOT EXISTS mail_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        sender_name VARCHAR(255) DEFAULT 'System',
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        mail_type ENUM('system', 'reward', 'notification', 'event') DEFAULT 'system',
        is_read BOOL<PERSON>N DEFAULT FALSE,
        is_archived BOOLEAN DEFAULT FALSE,
        has_attachment BOOLEAN DEFAULT FALSE,
        attachment_data JSON NULL,
        expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_mail (user_id, is_archived, created_at)
    )";
    
    if (!$conn->query($sql_mail_messages)) {
        die("Error creating mail_messages table: " . $conn->error);
    }
    echo "<p style='color: green;'>✓ mail_messages table created successfully!</p>";
    
    // Add some sample mail for existing users
    echo "<h3>Adding sample mail for existing users...</h3>";
    
    // Get all existing users
    $users_result = $conn->query("SELECT id FROM users");
    
    if ($users_result->num_rows > 0) {
        while ($user = $users_result->fetch_assoc()) {
            $user_id = $user['id'];
            
            // Send welcome mail if user doesn't have any mail yet
            $check_mail = $conn->prepare("SELECT COUNT(*) as count FROM mail_messages WHERE user_id = ?");
            $check_mail->bind_param("i", $user_id);
            $check_mail->execute();
            $mail_count = $check_mail->get_result()->fetch_assoc()['count'];
            
            if ($mail_count == 0) {
                // Send welcome mail
                MailManager::sendWelcomeMail($user_id);
                
                // Send a system notification
                MailManager::sendMail(
                    $user_id,
                    "System Update: Mail System Now Available!",
                    "Great news! The mail system is now available. You can receive important notifications, rewards, and updates through this system. Check your mail regularly for special gifts and announcements!",
                    "JILBOOBS System",
                    "system"
                );
                
                // Send a reward mail
                $reward_attachment = [
                    'coins' => 1000,
                    'diamonds' => 50,
                    'energy' => 10
                ];
                
                MailManager::sendMail(
                    $user_id,
                    "Mail System Launch Bonus!",
                    "To celebrate the launch of our new mail system, here's a special bonus for you! Claim these rewards and enjoy your adventure in JILBOOBS WORLD!",
                    "JILBOOBS Team",
                    "reward",
                    $reward_attachment
                );
                
                echo "<p style='color: blue;'>✓ Sample mail sent to user ID: {$user_id}</p>";
            }
            
            $check_mail->close();
        }
    }
    
    $conn->close();
    
    echo "<h3 style='color: green;'>Mail system setup completed successfully!</h3>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li>Click the mail icon (✉️) in the top navigation to open the mail system</li>";
    echo "<li>View inbox and archived mail</li>";
    echo "<li>Claim attachments with rewards</li>";
    echo "<li>Mark mail as read or archive them</li>";
    echo "</ul>";
    echo "<p><a href='menu/home.php' style='color: #00b8ff; text-decoration: none; font-weight: bold;'>→ Go to Home Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Mail System Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2, h3 {
            color: #333;
        }
        p {
            margin: 10px 0;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
</body>
</html>
