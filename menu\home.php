<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header("Location: ../register/login.php");
    exit();
}

// Include required managers
require_once '../includes/resource_manager.php';
require_once '../includes/gacha_manager.php';

// Get username and user ID
$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// Initialize gacha tickets if not exists
GachaManager::initializeUserTickets($user_id);

// Get user resources
$resources = ResourceManager::getUserResources($user_id);

// Get user tickets
$tickets = GachaManager::getUserTickets($user_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JILBOOBS WORLD - Home</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="home.css">
</head>
<body>
    <div id="game-container">
        <!-- Top navigation bar -->
        <div class="top-nav">
            <div class="top-left">
                <div class="settings-icon" onclick="openSettings()">⚙️</div>
                <div class="mail-icon" onclick="openMail()">
                    ✉️
                    <span id="mail-badge" class="notification-badge" style="display: none;">0</span>
                </div>
                <div class="bag-icon" onclick="window.location.href='../MenuButton/bag.php'">🎒</div>
            </div>
            <div class="top-right">
                <div class="resource-item">
                    <span class="resource-icon">💎</span>
                    <span id="diamonds-count"><?php echo number_format($resources['diamonds']); ?></span>
                    <div class="plus-btn" onclick="goToStore('diamonds')" title="Buy Diamonds">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🪙</span>
                    <span id="coins-count"><?php echo number_format($resources['coins']); ?></span>
                    <div class="plus-btn" onclick="goToStore('coins')" title="Buy Coins">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">⚡</span>
                    <span id="energy-count"><?php echo $resources['energy']; ?></span>
                    <div class="plus-btn" onclick="goToStore('energy')" title="Buy Energy">+</div>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">🎫</span>
                    <span id="standard-tickets"><?php echo $tickets['standard']; ?></span>
                    <div class="plus-btn" onclick="goToStore('tickets')" title="Buy Tickets">+</div>
                </div>
            </div>
        </div>
        
        <!-- Character display -->
        <img src="../assets/qistina/qistina2.png" alt="Character" class="character-display">
        
        <!-- Level indicator -->
        <div class="level-indicator">
            <div class="level-number">1</div>
            <div class="level-text">LV</div>
        </div>
        
        <!-- Username display -->
        <div class="username-display">
            <?php echo htmlspecialchars($username); ?>
        </div>
        
        <!-- Main menu cards -->
        <div class="menu-cards">
            <div class="menu-card" onclick="window.location.href='../menuchapters/story-chapters.php'">
                <div class="card-title">Story</div>
                <div class="card-subtitle">Continue your adventure</div>
            </div>
            
            <div class="menu-card" onclick="window.location.href='../MenuButton/Starlight.php'">
                <div class="card-title">Starlight membership</div>
                <div class="card-subtitle">Starlight rewards</div>
            </div>

            <div class="menu-card" onclick="window.location.href='../MenuButton/gacha.php'">
                <div class="card-title">Gacha</div>
                <div class="card-subtitle">Spin for characters & items</div>
            </div>
            
            <div class="menu-card" onclick="window.location.href='../MenuButton/Mission.php'">
                <div class="card-title">Missions</div>
                <div class="card-subtitle">Daily tasks and rewards</div>
            </div>
            
            <div class="menu-card" onclick="window.location.href='../MenuButton/Character.php'">
                <div class="card-title">Characters</div>
                <div class="card-subtitle">Manage your relationships</div>
            </div>
            
            <div class="menu-card" onclick="window.location.href='../MenuButton/store.php'">
                <div class="card-title">Store</div>
                <div class="card-subtitle">Buy items and outfits</div>
            </div>


        </div>
        
        <!-- News banner -->
        <div class="news-banner">
            <div class="news-header">BREAKING NEWS</div>
            <div class="news-content">
                <div class="news-title">NEW EPISODE AVAILABLE</div>
                <div class="news-subtitle">Explore the latest chapter now!</div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settings-modal" class="settings-modal">
            <div class="settings-content">
                <div class="settings-header">
                    <h2 class="settings-title">Settings</h2>
                    <button class="close-settings" onclick="closeSettings()">×</button>
                </div>

                <div class="profile-section">
                    <h3 style="margin-bottom: 15px; color: var(--primary-color);">Profile</h3>
                    <div class="profile-info">
                        <div class="profile-item">
                            <span class="profile-label">Username:</span>
                            <span class="profile-value"><?php echo htmlspecialchars($username); ?></span>
                        </div>
                        <div class="profile-item">
                            <span class="profile-label">Level:</span>
                            <span class="profile-value">1</span>
                        </div>
                        <div class="profile-item">
                            <span class="profile-label">Diamonds:</span>
                            <span class="profile-value"><?php echo number_format($resources['diamonds']); ?></span>
                        </div>
                        <div class="profile-item">
                            <span class="profile-label">Coins:</span>
                            <span class="profile-value"><?php echo number_format($resources['coins']); ?></span>
                        </div>
                        <div class="profile-item">
                            <span class="profile-label">Energy:</span>
                            <span class="profile-value"><?php echo $resources['energy']; ?></span>
                        </div>
                    </div>
                </div>

                <div class="logout-section">
                    <a href="#" class="logout-button" onclick="showLogoutConfirm(event)">Logout</a>
                </div>
            </div>
        </div>

        <!-- Logout Confirmation Modal -->
        <div id="logout-confirm" class="modal-overlay">
            <div class="modal-content">
                <h3>Logout Confirmation</h3>
                <p>Are you sure you want to logout?</p>
                <div class="modal-buttons">
                    <button id="cancel-logout" class="cancel-btn">Cancel</button>
                    <button id="confirm-logout" class="confirm-btn">Logout</button>
                </div>
            </div>
        </div>

        <!-- Mail Modal -->
        <div id="mail-modal" class="mail-modal">
            <div class="mail-container">
                <div class="mail-header">
                    <div class="mail-title">
                        📧 Minos System-Mail
                    </div>
                    <div class="mail-tabs">
                        <div class="mail-tab active" data-tab="inbox">📥 Inbox</div>
                        <div class="mail-tab" data-tab="archived">📁 Archived</div>
                    </div>
                    <button class="close-mail" onclick="closeMail()">×</button>
                </div>
                <div class="mail-content">
                    <div class="mail-sidebar">
                        <div class="mail-list" id="mail-list">
                            <div class="no-mail">No mail for now</div>
                        </div>
                    </div>
                    <div class="mail-viewer" id="mail-viewer">
                        <div class="no-mail-selected">
                            <div class="mail-icon-large">📧</div>
                            <div>No mail for now</div>
                        </div>
                    </div>
                </div>
                <div class="mail-actions">
                    <button class="mail-action-btn" onclick="markAllAsRead()">Delete Read</button>
                    <button class="mail-action-btn" onclick="refreshMail()">Process All</button>
                </div>
            </div>
        </div>


    </div>

    <script>
        // Settings modal functions
        function openSettings() {
            const modal = document.getElementById('settings-modal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('active');
            }, 10);
        }

        function closeSettings() {
            const modal = document.getElementById('settings-modal');
            modal.classList.remove('active');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // Logout confirmation functions
        function showLogoutConfirm(e) {
            e.preventDefault();
            const logoutModal = document.getElementById('logout-confirm');
            logoutModal.style.display = 'flex';
            setTimeout(() => {
                logoutModal.classList.add('active');
            }, 10);
        }

        // Close settings modal when clicking outside
        document.getElementById('settings-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSettings();
            }
        });

        // Logout modal event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const logoutModal = document.getElementById('logout-confirm');
            const cancelLogout = document.getElementById('cancel-logout');
            const confirmLogout = document.getElementById('confirm-logout');

            // Hide modal when cancel is clicked
            if (cancelLogout) {
                cancelLogout.addEventListener('click', function() {
                    logoutModal.classList.remove('active');
                    setTimeout(() => {
                        logoutModal.style.display = 'none';
                    }, 300);
                });
            }

            // Proceed with logout when confirm is clicked
            if (confirmLogout) {
                confirmLogout.addEventListener('click', function() {
                    window.location.href = '../register/logout.php';
                });
            }

            // Close modal when clicking outside
            logoutModal.addEventListener('click', function(e) {
                if (e.target === logoutModal) {
                    logoutModal.classList.remove('active');
                    setTimeout(() => {
                        logoutModal.style.display = 'none';
                    }, 300);
                }
            });
        });

        // Mail system functions
        let currentMailTab = 'inbox';
        let mailData = [];
        let selectedMailId = null;

        // Open mail modal
        function openMail() {
            const modal = document.getElementById('mail-modal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('active');
            }, 10);
            loadMail();
        }

        // Close mail modal
        function closeMail() {
            const modal = document.getElementById('mail-modal');
            modal.classList.remove('active');
            selectedMailId = null; // Reset selection
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // Load mail data
        async function loadMail() {
            try {
                const archived = currentMailTab === 'archived';
                const response = await fetch(`../api/mail.php?action=list&archived=${archived}`);
                const result = await response.json();

                if (result.success) {
                    mailData = result.mail;
                    displayMail();
                    updateUnreadCount();
                } else {
                    console.error('Failed to load mail:', result.message);
                }
            } catch (error) {
                console.error('Error loading mail:', error);
            }
        }

        // Display mail in the list
        function displayMail() {
            const mailList = document.getElementById('mail-list');

            if (mailData.length === 0) {
                mailList.innerHTML = '<div class="no-mail">No mail for now</div>';
                showNoMailSelected();
                return;
            }

            mailList.innerHTML = mailData.map(mail => {
                const date = new Date(mail.created_at).toLocaleDateString();
                const isUnread = !mail.is_read;
                const hasAttachment = mail.has_attachment;
                const isSelected = selectedMailId === mail.id;

                return `
                    <div class="mail-item ${isUnread ? 'unread' : ''} ${isSelected ? 'selected' : ''}" onclick="selectMail(${mail.id})">
                        ${hasAttachment ? '<div class="mail-attachment">📎</div>' : ''}
                        <div class="mail-item-header">
                            <div class="mail-sender">${mail.sender_name}</div>
                            <div class="mail-date">${date}</div>
                        </div>
                        <div class="mail-subject">${mail.subject}</div>
                        <div class="mail-preview">${mail.message.substring(0, 50)}${mail.message.length > 50 ? '...' : ''}</div>
                    </div>
                `;
            }).join('');

            // If no mail is selected and we have mail, show the no selection message
            if (!selectedMailId && mailData.length > 0) {
                showNoMailSelected();
            }
        }

        // Show no mail selected message
        function showNoMailSelected() {
            const mailViewer = document.getElementById('mail-viewer');
            mailViewer.innerHTML = `
                <div class="no-mail-selected">
                    <div class="mail-icon-large">📧</div>
                    <div>No mail for now</div>
                </div>
            `;
        }

        // Select and display a mail
        function selectMail(mailId) {
            selectedMailId = mailId;
            const mail = mailData.find(m => m.id === mailId);
            if (!mail) return;

            // Update mail list to show selection
            displayMail();

            // Mark as read if unread
            if (!mail.is_read) {
                markMailAsRead(mailId);
                mail.is_read = true; // Update local data
            }

            // Display mail in viewer
            displayMailViewer(mail);
        }

        // Display mail in the viewer
        function displayMailViewer(mail) {
            const mailViewer = document.getElementById('mail-viewer');
            const date = new Date(mail.created_at).toLocaleString();

            let attachmentHtml = '';
            if (mail.has_attachment && mail.attachment_data) {
                const attachment = typeof mail.attachment_data === 'string'
                    ? JSON.parse(mail.attachment_data)
                    : mail.attachment_data;

                const isClaimed = attachment.claimed;

                let rewardsHtml = '';
                if (attachment.coins) rewardsHtml += `<div class="reward-item">🪙 ${attachment.coins}</div>`;
                if (attachment.diamonds) rewardsHtml += `<div class="reward-item">💎 ${attachment.diamonds}</div>`;
                if (attachment.energy) rewardsHtml += `<div class="reward-item">⚡ ${attachment.energy}</div>`;

                attachmentHtml = `
                    <div class="mail-viewer-attachment">
                        <div class="attachment-header">
                            <span>📎</span>
                            <span>Attachment</span>
                        </div>
                        <div class="attachment-rewards">
                            ${rewardsHtml}
                        </div>
                        <button class="claim-attachment-btn"
                                onclick="claimAttachment(${mail.id})"
                                ${isClaimed ? 'disabled' : ''}>
                            ${isClaimed ? 'Already Claimed' : 'Claim Rewards'}
                        </button>
                    </div>
                `;
            }

            mailViewer.innerHTML = `
                <div class="mail-viewer-header">
                    <div class="mail-viewer-subject">${mail.subject}</div>
                    <div class="mail-viewer-meta">
                        <div class="mail-viewer-sender">From: ${mail.sender_name}</div>
                        <div class="mail-viewer-date">${date}</div>
                    </div>
                </div>
                <div class="mail-viewer-body">
                    <div class="mail-viewer-message">${mail.message}</div>
                    ${attachmentHtml}
                </div>
            `;
        }

        // Switch mail tabs
        document.addEventListener('DOMContentLoaded', function() {
            const mailTabs = document.querySelectorAll('.mail-tab');
            mailTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    mailTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');
                    // Update current tab
                    currentMailTab = this.dataset.tab;
                    // Reset selection
                    selectedMailId = null;
                    // Reload mail
                    loadMail();
                });
            });
        });

        // Mark mail as read
        async function markMailAsRead(mailId) {
            try {
                const response = await fetch('../api/mail.php?action=mark_read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ mail_id: mailId })
                });

                const result = await response.json();
                if (result.success) {
                    loadMail(); // Refresh mail list
                }
            } catch (error) {
                console.error('Error marking mail as read:', error);
            }
        }

        // Mark all mail as read
        async function markAllAsRead() {
            try {
                const response = await fetch('../api/mail.php?action=mark_all_read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                if (result.success) {
                    loadMail(); // Refresh mail list
                    showNotification('All mail marked as read', 'success');
                } else {
                    showNotification('Failed to mark all mail as read', 'error');
                }
            } catch (error) {
                console.error('Error marking all mail as read:', error);
                showNotification('Error marking all mail as read', 'error');
            }
        }

        // Refresh mail
        function refreshMail() {
            loadMail();
            showNotification('Mail refreshed', 'success');
        }

        // Claim attachment
        async function claimAttachment(mailId) {
            try {
                const response = await fetch('../api/mail.php?action=claim_attachment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ mail_id: mailId })
                });

                const result = await response.json();
                if (result.success) {
                    showNotification('Attachment claimed successfully!', 'success');

                    // Update the mail data locally
                    const mail = mailData.find(m => m.id === mailId);
                    if (mail && mail.attachment_data) {
                        if (typeof mail.attachment_data === 'string') {
                            mail.attachment_data = JSON.parse(mail.attachment_data);
                        }
                        mail.attachment_data.claimed = true;
                    }

                    // Refresh the viewer to show claimed status
                    if (selectedMailId === mailId) {
                        displayMailViewer(mail);
                    }

                    // Update resource displays
                    setTimeout(() => {
                        location.reload(); // Simple way to update resources
                    }, 1000);
                } else {
                    showNotification(result.message || 'Failed to claim attachment', 'error');
                }
            } catch (error) {
                console.error('Error claiming attachment:', error);
                showNotification('Error claiming attachment', 'error');
            }
        }

        // Update unread mail count
        async function updateUnreadCount() {
            try {
                const response = await fetch('../api/mail.php?action=unread_count');
                const result = await response.json();

                if (result.success) {
                    const badge = document.getElementById('mail-badge');
                    if (result.unread_count > 0) {
                        badge.textContent = result.unread_count;
                        badge.style.display = 'flex';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Error updating unread count:', error);
            }
        }

        // Simple notification function
        function showNotification(message, type = 'success') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                background: ${type === 'success' ? 'linear-gradient(135deg, #00cc66, #00ff80)' : 'linear-gradient(135deg, #ff4d7e, #ff6b9d)'};
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Close mail modal when clicking outside
        document.getElementById('mail-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMail();
            }
        });

        // Load unread count on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateUnreadCount();
        });


    </script>

    <!-- Include shared functions -->
    <script src="../MenuButton/shared-functions.js"></script>

    <script>
        // Override goToStore for home page to use correct path
        function goToStore(resourceType) {
            // Add a small animation effect
            if (event && event.target) {
                event.target.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    event.target.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        event.target.style.transform = '';
                        // Navigate to store page with the specific resource type
                        window.location.href = `../MenuButton/store.php?resource=${resourceType}`;
                    }, 100);
                }, 100);
            } else {
                // Fallback without animation
                window.location.href = `../MenuButton/store.php?resource=${resourceType}`;
            }
        }
    </script>
</body>
</html>