<?php
/**
 * Database Setup Script
 * Run this file once to set up the database tables for the resource system
 */

require_once 'config/database.php';

echo "<h2>Setting up Database Tables...</h2>";

try {
    // Initialize database tables
    initializeDatabaseTables();
    echo "<p style='color: green;'>✓ Database tables created successfully!</p>";
    
    // Check if we need to add resources to existing users
    $conn = getDatabaseConnection();
    
    // Get users who don't have resources yet
    $sql = "SELECT u.id, u.username FROM users u 
            LEFT JOIN user_resources ur ON u.id = ur.user_id 
            WHERE ur.user_id IS NULL";
    
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo "<h3>Adding default resources to existing users:</h3>";
        
        while ($row = $result->fetch_assoc()) {
            $user_id = $row['id'];
            $username = $row['username'];
            
            if (ResourceManager::initializeUserResources($user_id)) {
                echo "<p style='color: blue;'>✓ Added default resources to user: {$username}</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to add resources to user: {$username}</p>";
            }
        }
    } else {
        echo "<p>All existing users already have resources configured.</p>";
    }
    
    $conn->close();
    
    echo "<h3>Setup Complete!</h3>";
    echo "<p>Default resources for new users:</p>";
    echo "<ul>";
    echo "<li>Coins: 1,000</li>";
    echo "<li>Diamonds: 1,000</li>";
    echo "<li>Energy: 40</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2, h3 { color: #333; }
        p { margin: 10px 0; }
        ul { margin: 10px 0; }
    </style>
</head>
<body>
    <a href="menu/home.php">← Back to Game</a>
</body>
</html>
