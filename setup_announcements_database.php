<?php
require_once 'config/database.php';
require_once 'includes/announcement_manager.php';

echo "<h2>Setting up Announcements Database...</h2>";

try {
    $conn = getDatabaseConnection();
    
    // Create announcements table
    echo "<h3>Creating announcements table...</h3>";
    $sql_announcements = "CREATE TABLE IF NOT EXISTS announcements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        announcement_type ENUM('system', 'event', 'maintenance', 'update', 'notice') DEFAULT 'system',
        category ENUM('System Announcement', 'Event Announcement', 'Dis Info') DEFAULT 'System Announcement',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        is_active BOOLEAN DEFAULT TRUE,
        is_featured BOOLEAN DEFAULT FALSE,
        start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_date TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_active_date (is_active, start_date, end_date),
        INDEX idx_type_priority (announcement_type, priority)
    )";
    
    if ($conn->query($sql_announcements)) {
        echo "✅ Announcements table created successfully!<br>";
    } else {
        echo "❌ Error creating announcements table: " . $conn->error . "<br>";
    }
    
    // Create user_announcement_reads table to track which announcements users have read
    echo "<h3>Creating user_announcement_reads table...</h3>";
    $sql_user_reads = "CREATE TABLE IF NOT EXISTS user_announcement_reads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        announcement_id INT NOT NULL,
        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (announcement_id) REFERENCES announcements(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_announcement (user_id, announcement_id)
    )";
    
    if ($conn->query($sql_user_reads)) {
        echo "✅ User announcement reads table created successfully!<br>";
    } else {
        echo "❌ Error creating user announcement reads table: " . $conn->error . "<br>";
    }
    
    // Check if we have any announcements, if not, create sample ones
    $result = $conn->query("SELECT COUNT(*) as count FROM announcements");
    $row = $result->fetch_assoc();
    $announcement_count = $row['count'];
    
    if ($announcement_count == 0) {
        echo "<h3>Creating sample announcements...</h3>";
        
        // Sample System Announcements
        AnnouncementManager::createAnnouncement(
            "Maintenance Ended (May 27)",
            "Dear Chief:\n\nThe maintenance has ended. Please log back into the game to update the patch.\n\nThe maintenance compensation has been sent to your in-game mailbox. The compensation will expire after 7 days. Thank you for your understanding and support.\n\n📋Update Content📋",
            "maintenance",
            "System Announcement",
            "high"
        );
        
        AnnouncementManager::createAnnouncement(
            "Maintenance Notice (May 27)",
            "Dear Players,\n\nWe will be performing scheduled maintenance on May 27th from 02:00 to 06:00 UTC. During this time, the game will be temporarily unavailable.\n\nMaintenance includes:\n• Server optimization\n• Bug fixes\n• New content updates\n\nCompensation will be sent to all players after maintenance. Thank you for your patience!",
            "maintenance",
            "System Announcement",
            "normal"
        );
        
        AnnouncementManager::createAnnouncement(
            "Important Notice Regarding Twitter/X Login",
            "Due to recent changes in Twitter/X API policies, we are temporarily experiencing issues with Twitter/X login functionality.\n\nWe are working on a solution and will update you as soon as possible. In the meantime, please use alternative login methods.\n\nThank you for your understanding.",
            "notice",
            "System Announcement",
            "high"
        );
        
        AnnouncementManager::createAnnouncement(
            "Path to Nowhere Code of Conduct",
            "To ensure a positive gaming environment for all players, please review our updated Code of Conduct:\n\n• Be respectful to other players\n• No harassment or toxic behavior\n• Report any bugs or issues through proper channels\n• Follow fair play guidelines\n\nViolations may result in account restrictions. Let's build a great community together!",
            "notice",
            "System Announcement",
            "normal"
        );
        
        // Sample Event Announcements
        AnnouncementManager::createAnnouncement(
            "Summer Festival Event - Limited Time!",
            "Join our Summer Festival event running from June 1st to June 30th!\n\n🎉 Event Features:\n• Daily login bonuses\n• Special summer-themed characters\n• Exclusive rewards and items\n• Limited-time story chapters\n\nDon't miss out on this exciting celebration!",
            "event",
            "Event Announcement",
            "high"
        );
        
        AnnouncementManager::createAnnouncement(
            "Double EXP Weekend",
            "This weekend only - earn double EXP on all activities!\n\n⚡ Event Details:\n• Start: Friday 6PM UTC\n• End: Sunday 11:59PM UTC\n• Applies to: Story missions, daily quests, and battles\n\nLevel up faster and unlock new content!",
            "event",
            "Event Announcement",
            "normal"
        );
        
        // Sample Dis Info (Developer Information)
        AnnouncementManager::createAnnouncement(
            "Developer Update - June 2024",
            "Hello Commanders!\n\nWe wanted to share some exciting updates about what's coming next:\n\n🔮 Upcoming Features:\n• New character progression system\n• Enhanced guild functionality\n• Quality of life improvements\n• Performance optimizations\n\nStay tuned for more details in the coming weeks!",
            "update",
            "Dis Info",
            "normal"
        );
        
        echo "✅ Sample announcements created successfully!<br>";
    }
    
    $conn->close();
    echo "<h3>✅ Announcements database setup completed!</h3>";
    echo "<p><a href='menu/home.php'>← Back to Game</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error setting up announcements database: " . $e->getMessage();
}
?>
