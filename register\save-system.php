<?php
session_start();

// Handle save/load requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save') {
        // Get game state data from POST
        $gameState = $_POST['gameState'] ?? '';
        
        if (!empty($gameState)) {
            // Store game state in session
            $_SESSION['game_state'] = $gameState;
            echo json_encode(['success' => true, 'message' => 'Game saved successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'No game data provided']);
        }
    } 
    else if ($action === 'load') {
        // Return saved game state
        if (isset($_SESSION['game_state'])) {
            echo json_encode([
                'success' => true, 
                'gameState' => $_SESSION['game_state']
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'No saved game found']);
        }
    }
    exit;
}
?>