<?php
session_start();

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: ../menu/home.php");
    exit;
}

// Include database configuration and resource manager
require_once '../config/database.php';
require_once '../includes/resource_manager.php';

$conn = getDatabaseConnection();

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Validate input
    if (empty($username) || empty($password)) {
        $error = "Username and password are required";
    } else {
        // Check if username already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $error = "Username already exists";
        } else {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $stmt = $conn->prepare("INSERT INTO users (username, password) VALUES (?, ?)");
            $stmt->bind_param("ss", $username, $hashed_password);
            
            if ($stmt->execute()) {
                // Get the new user ID
                $user_id = $conn->insert_id;

                // Initialize user resources with default values (1000 coins, 1000 diamonds)
                ResourceManager::initializeUserResources($user_id);

                // Initialize starlight progress for new user
                require_once '../includes/starlight_manager.php';
                StarlightManager::initializeUserStarlight($user_id);

                // Initialize gacha tickets for new user
                require_once '../includes/gacha_manager.php';
                GachaManager::initializeUserTickets($user_id);

                // Send welcome mail to new user
                require_once '../includes/mail_manager.php';
                MailManager::sendWelcomeMail($user_id);

                // Unlock first chapter for new user
                $stmt = $conn->prepare("INSERT INTO user_progress (user_id, chapter_id, completed) VALUES (?, '01-01', TRUE)");
                $stmt->bind_param("i", $user_id);
                $stmt->execute();

                // Set session
                $_SESSION['user_id'] = $user_id;
                $_SESSION['username'] = $username;

                $success = "Registration successful! Redirecting...";
                header("Refresh: 2; URL=../menuchapters/story-chapters.php");
            } else {
                $error = "Registration failed: {$conn->error}";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Malay RPG</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;700&family=Poppins:wght@300;400;500;600&family=Cinzel:wght@600;700;900&display=swap');
        
        body {
            font-family: 'Montserrat', sans-serif;
            background-image: url('../assets/city-backgroundj.png');
            background-size: cover;
            background-position: center;
            height: 100vh;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.3), rgba(78, 205, 196, 0.3));
            z-index: -1;
        }
        
        .brand-header {
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            text-align: center;
            padding: 15px;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            border-bottom: 2px solid rgba(255, 107, 107, 0.3);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
        }
        
        .main-title {
            font-family: 'Cinzel', serif;
            font-size: 3.5rem;
            font-weight: 900;
            color: #fff;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.8), 
                         0 0 20px rgba(255, 107, 107, 0.5),
                         0 0 30px rgba(255, 107, 107, 0.3);
            margin: 0;
            letter-spacing: 4px;
            background: linear-gradient(45deg, #ffd166, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            transform: scale(1, 1.2);
        }
        
        .sub-brand {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            color: #ffd166;
            margin-top: 15px;
            letter-spacing: 2px;
            text-shadow: 0 0 10px rgba(255, 209, 102, 0.5);
        }
        
        .register-container {
            background: rgba(0, 0, 0, 0.7);
            padding: 2rem;
            border-radius: 15px;
            width: 100%;
            max-width: 350px;
            box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
            border: 2px solid rgba(255, 107, 107, 0.2);
            backdrop-filter: blur(5px);
            position: relative;
            overflow: hidden;
            margin-top: 120px;
        }
        
        .register-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
            z-index: -1;
            animation: pulse 8s infinite ease-in-out;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.05); opacity: 0.8; }
            100% { transform: scale(1); opacity: 0.5; }
        }
        
        h1 {
            text-align: center;
            margin-bottom: 1.5rem;
            color: #ff6b6b;
            font-family: 'Cinzel', serif;
            font-size: 2rem;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
            position: relative;
            letter-spacing: 2px;
        }
        
        h1::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, transparent, #ff6b6b, transparent);
        }
        
        .form-group {
            margin-bottom: 1.2rem;
            position: relative;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #ffd166;
            letter-spacing: 1px;
            font-family: 'Poppins', sans-serif;
        }
        
        input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid rgba(255, 107, 107, 0.3);
            border-radius: 8px;
            background: rgba(30, 39, 46, 0.6);
            box-sizing: border-box;
            color: white;
            font-family: 'Montserrat', sans-serif;
            transition: all 0.3s ease;
        }
        
        input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }
        
        button {
            width: 100%;
            padding: 0.8rem;
            background: linear-gradient(45deg, #ff6b6b, #c83349);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 1.2rem;
            transition: all 0.3s ease;
            font-size: 1rem;
            letter-spacing: 2px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(200, 51, 73, 0.4);
            font-family: 'Cinzel', serif;
        }
        
        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }
        
        button:hover {
            background: linear-gradient(45deg, #c83349, #ff6b6b);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(200, 51, 73, 0.6);
        }
        
        button:hover::before {
            left: 100%;
        }
        
        .error {
            color: #ff5252;
            margin-bottom: 1.2rem;
            text-align: center;
            background: rgba(255, 82, 82, 0.1);
            padding: 8px;
            border-radius: 5px;
            border-left: 3px solid #ff5252;
            font-size: 0.9rem;
        }
        
        .success {
            color: #4CAF50;
            margin-bottom: 1.2rem;
            text-align: center;
            background: rgba(76, 175, 80, 0.1);
            padding: 8px;
            border-radius: 5px;
            border-left: 3px solid #4CAF50;
            font-size: 0.9rem;
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.2rem;
            padding-top: 1.2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }
        
        .login-link a {
            color: #ff6b6b;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .login-link a:hover {
            color: #ffd166;
            text-shadow: 0 0 5px rgba(255, 209, 102, 0.5);
        }
        
        .decorative-element {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.2), transparent);
            z-index: -1;
        }
        
        .decorative-element.top-right {
            top: -50px;
            right: -50px;
        }
        
        .decorative-element.bottom-left {
            bottom: -50px;
            left: -50px;
        }
    </style>
</head>
<body>
    <div class="brand-header">
        <h1 class="main-title">MALAY RPG</h1>
        <div class="sub-brand">Ai Generation</div>
    </div>
    
    <div class="register-container">
        <div class="decorative-element top-right"></div>
        <div class="decorative-element bottom-left"></div>
        
        <h1>DAFTAR</h1>
        
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
            <div class="form-group">
                <label for="username">USERNAME</label>
                <input type="text" id="username" name="username" required placeholder="Pilih nama pengguna">
            </div>
            
            <div class="form-group">
                <label for="password">PASSWORD</label>
                <input type="password" id="password" name="password" required placeholder="Cipta kata laluan">
            </div>
            
            <button type="submit">CREATE JOURNEY</button>
        </form>
        
        <div class="login-link">
            Sudah ada akaun? <a href="login.php">Masuk sekarang</a>
        </div>
    </div>
</body>
</html>



